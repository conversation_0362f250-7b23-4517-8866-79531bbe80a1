{"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "prettier.semi": false, "prettier.singleQuote": true, "prettier.singleAttributePerLine": true, "prettier.printWidth": 120, "prettier.trailingComma": "all", "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "i18n-ally.localesPaths": ["packages/i18n/translations"]}