// Google One Tap 配置和环境检查
export const getGoogleOneTapConfig = () => {
  const isProduction = process.env.NODE_ENV === 'production'
  const clientId = process.env.NEXT_PUBLIC_CLIENT_ID

  if (!clientId) {
    console.error('NEXT_PUBLIC_CLIENT_ID is not configured')
    return null
  }

  // 基础配置
  const baseConfig = {
    client_id: clientId,
    auto_select: false,
    cancel_on_tap_outside: false,
    context: 'signin' as const,
    ux_mode: 'redirect' as const,
    itp_support: true,
  }

  // 生产环境配置
  if (isProduction) {
    return {
      ...baseConfig,
      // Google One Tap不需要login_uri，因为它使用JWT token验证
      // 生产环境可以使用FedCM
      use_fedcm_for_prompt: true,
    }
  }

  // 开发环境配置
  return {
    ...baseConfig,
    // Google One Tap不需要login_uri，因为它使用JWT token验证
    // 开发环境禁用FedCM以避免CORS问题
    use_fedcm_for_prompt: false,
  }
}

// 检查环境是否支持Google One Tap
export const isGoogleOneTapSupported = (): boolean => {
  if (typeof window === 'undefined') return false

  // 检查是否使用代理
  const isLocalhost =
    window.location.hostname === 'localhost' ||
    window.location.hostname === '127.0.0.1'
  const hasProxy =
    window.location.port === '7890' || navigator.userAgent.includes('proxy')

  if (isLocalhost && hasProxy) {
    console.warn('Google One Tap is not supported with proxy servers')
    return false
  }

  // 检查是否为HTTPS（生产环境要求）
  const isSecure = window.location.protocol === 'https:' || isLocalhost
  if (!isSecure) {
    console.warn('Google One Tap requires HTTPS in production')
    return false
  }

  return true
}

// 获取错误信息的友好提示
export const getGoogleOneTapErrorMessage = (error: any): string => {
  if (typeof error === 'string') return error

  const errorMessage = error?.message || error?.toString() || 'Unknown error'

  // 常见错误的友好提示
  if (errorMessage.includes('CORS')) {
    return 'CORS error: Please check your domain configuration in Google Console'
  }

  if (errorMessage.includes('403')) {
    return 'Access denied: Please verify your Google OAuth configuration'
  }

  if (errorMessage.includes('client_id')) {
    return 'Invalid client ID: Please check your NEXT_PUBLIC_CLIENT_ID environment variable'
  }

  return errorMessage
}
