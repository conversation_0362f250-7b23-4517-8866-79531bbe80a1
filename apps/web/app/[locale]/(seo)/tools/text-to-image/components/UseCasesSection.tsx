'use client'

import { useState, useEffect, useRef } from 'react'
import { Button } from '@ui/components/button'
import {
  Palette,
  Heart,
  Wand2,
  Gamepad2,
  ArrowRight,
  Sparkles,
} from 'lucide-react'
import { Link } from '@i18n/routing'
import { useTranslations } from 'next-intl'

// 图片轮播组件
interface ImageCarouselProps {
  images: string[]
  alt: string
  className?: string
}

const ImageCarousel = ({ images, alt, className = '' }: ImageCarouselProps) => {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isTransitioning, setIsTransitioning] = useState(false)
  const [isPaused, setIsPaused] = useState(false)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  // 清理定时器的函数
  const clearCarouselInterval = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
  }

  // 启动轮播的函数
  const startCarousel = () => {
    if (images.length <= 1 || isPaused) return

    clearCarouselInterval()
    intervalRef.current = setInterval(() => {
      setIsTransitioning(true)

      setTimeout(() => {
        setCurrentIndex((prevIndex) => (prevIndex + 1) % images.length)
        setIsTransitioning(false)
      }, 300) // 渐变过渡时间的一半
    }, 3000) // 每3秒切换一次
  }

  useEffect(() => {
    startCarousel()
    return clearCarouselInterval
  }, [images.length, isPaused])

  // 鼠标悬停时暂停轮播
  const handleMouseEnter = () => {
    setIsPaused(true)
    clearCarouselInterval()
  }

  // 鼠标离开时恢复轮播
  const handleMouseLeave = () => {
    setIsPaused(false)
  }

  if (images.length === 0) return null

  return (
    <div
      className={`relative overflow-hidden ${className}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {images.map((image, index) => (
        <img
          key={index}
          src={image}
          alt={`${alt} - Image ${index + 1}`}
          className={`absolute inset-0 w-full h-full object-cover transition-all duration-700 ease-in-out ${
            index === currentIndex
              ? `opacity-100 scale-100 ${
                  isTransitioning ? 'blur-sm' : 'blur-0'
                }`
              : 'opacity-0 scale-105'
          }`}
          style={{
            transitionDelay: index === currentIndex ? '0ms' : '350ms',
          }}
        />
      ))}

      {/* 轮播指示器 */}
      {images.length > 1 && (
        <div className="absolute bottom-3 left-1/2 transform -translate-x-1/2 flex space-x-2 z-10">
          {images.map((_, index) => (
            <div
              key={index}
              className={`w-2 h-2 rounded-full transition-all duration-300 cursor-pointer ${
                index === currentIndex
                  ? 'bg-white shadow-lg scale-125'
                  : 'bg-white/50 hover:bg-white/70'
              }`}
              onClick={() => {
                setCurrentIndex(index)
                setIsTransitioning(true)
                setTimeout(() => setIsTransitioning(false), 300)
              }}
            />
          ))}
        </div>
      )}

      {/* 暂停指示器 */}
      {isPaused && images.length > 1 && (
        <div className="absolute top-3 left-3 bg-black/50 backdrop-blur-sm rounded-full px-2 py-1 z-10">
          <div className="flex items-center space-x-1">
            <div className="w-1 h-3 bg-white rounded-full"></div>
            <div className="w-1 h-3 bg-white rounded-full"></div>
          </div>
        </div>
      )}
    </div>
  )
}

interface UseCasesSectionProps {
  toolUrl: string
}

// UseCase 类型定义
interface UseCase {
  icon: React.ComponentType<{ className?: string }>
  title: string
  description: string
  image: string | string[] // 支持单个图片或图片数组
  alt: string
  buttonText: string
  gradient: string
  bgGradient: string
}

const UseCasesSection = ({ toolUrl }: UseCasesSectionProps) => {
  const t = useTranslations('aiTextToImage')
  const [isVisible, setIsVisible] = useState(false)
  const [hoveredCase, setHoveredCase] = useState<number | null>(null)
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.1 }
    )

    if (sectionRef.current) {
      observer.observe(sectionRef.current)
    }

    return () => observer.disconnect()
  }, [])

  const useCases: UseCase[] = [
    {
      icon: Palette,
      title: t('useCase1Title'),
      description: t('useCase1Description'),
      image: [
        '/images/ai-text-to-image/graffiti.webp',
        '/images/ai-text-to-image/graffiti1.png',
        '/images/ai-text-to-image/graffiti2.png',
        '/images/ai-text-to-image/graffiti3.png',
      ],
      alt: t('useCase1Alt'),
      buttonText: t('useCase1Button'),
      gradient: 'from-orange-500 to-red-500',
      bgGradient: 'from-orange-500/10 to-red-500/10',
    },
    {
      icon: Heart,
      title: t('useCase2Title'),
      description: t('useCase2Description'),
      image: [
        '/images/ai-text-to-image/fox.webp',
        '/images/ai-text-to-image/tatto1.png',
        '/images/ai-text-to-image/tatto2.png',
        '/images/ai-text-to-image/tatto3.png',
      ],
      alt: t('useCase2Alt'),
      buttonText: t('useCase2Button'),
      gradient: 'from-purple-500 to-pink-500',
      bgGradient: 'from-purple-500/10 to-pink-500/10',
    },
    {
      icon: Wand2,
      title: t('useCase3Title'),
      description: t('useCase3Description'),
      image: [
        '/images/ai-text-to-image/loong1.webp',
        '/images/ai-text-to-image/loong2.webp',
        '/images/ai-text-to-image/loong3.webp',
        '/images/ai-text-to-image/loong4.webp',
      ],
      alt: t('useCase3Alt'),
      buttonText: t('useCase3Button'),
      gradient: 'from-blue-500 to-purple-500',
      bgGradient: 'from-blue-500/10 to-purple-500/10',
    },
    {
      icon: Gamepad2,
      title: t('useCase4Title'),
      description: t('useCase4Description'),
      image: [
        '/images/ai-text-to-image/bit1.png',
        '/images/ai-text-to-image/bit2.png',
        '/images/ai-text-to-image/bit3.png',
      ],
      alt: t('useCase4Alt'),
      buttonText: t('useCase4Button'),
      gradient: 'from-green-500 to-teal-500',
      bgGradient: 'from-green-500/10 to-teal-500/10',
    },
  ]

  return (
    <section
      ref={sectionRef}
      className="py-20 bg-gradient-to-b from-slate-800 to-slate-900 relative overflow-hidden"
    >
      {/* 背景装饰 */}
      <div className="absolute inset-0">
        <div className="absolute top-40 left-20 w-64 h-64 bg-purple-500/5 rounded-full blur-3xl animate-pulse" />
        <div
          className="absolute bottom-40 right-20 w-80 h-80 bg-blue-500/5 rounded-full blur-3xl animate-pulse"
          style={{ animationDelay: '3s' }}
        />
      </div>

      <div className="relative z-10 container mx-auto px-4">
        {/* 标题部分 */}
        <div
          className={`text-center mb-16 transition-all duration-1000 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
        >
          <div className="inline-flex items-center gap-2 bg-purple-500/20 backdrop-blur-sm border border-purple-500/30 rounded-full px-4 py-2 text-purple-300 text-sm font-medium mb-6">
            <Sparkles className="w-4 h-4" />
            {t('useCasesTagText')}
          </div>

          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            {t('useCasesTitle')}{' '}
            <span className="text-pink-400 ">
              {t('useCasesTitleHighlight')}
            </span>
          </h2>

          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            {t('useCasesDescription')}
          </p>
        </div>

        {/* 用例网格 */}
        <div className="grid lg:grid-cols-2 gap-8">
          {useCases.map((useCase, index) => {
            const Icon = useCase.icon

            return (
              <div
                key={index}
                className={`group transition-all duration-1000 ${
                  isVisible
                    ? 'opacity-100 translate-y-0'
                    : 'opacity-0 translate-y-10'
                }`}
                style={{ transitionDelay: `${index * 150}ms` }}
                onMouseEnter={() => setHoveredCase(index)}
                onMouseLeave={() => setHoveredCase(null)}
              >
                <div
                  className={`relative bg-gradient-to-br ${useCase.bgGradient} backdrop-blur-sm border border-slate-700/50 rounded-2xl p-8 h-full group-hover:border-purple-500/30 transition-all duration-500 hover:scale-105`}
                >
                  {/* 背景光效 */}
                  <div
                    className={`absolute inset-0 pointer-events-none bg-gradient-to-br ${useCase.gradient} opacity-0 group-hover:opacity-5 rounded-2xl transition-opacity duration-500`}
                  />

                  {/* 图片部分 */}
                  <div className="relative mb-6 overflow-hidden rounded-xl h-48">
                    {Array.isArray(useCase.image) ? (
                      // 如果是数组，使用轮播组件
                      <ImageCarousel
                        images={useCase.image}
                        alt={useCase.alt}
                        className="w-full h-full group-hover:scale-110 transition-transform duration-500"
                      />
                    ) : (
                      // 如果是单个图片，使用普通img标签
                      <img
                        src={useCase.image}
                        alt={useCase.alt}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                      />
                    )}

                    {/* 图片覆盖层 */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                    {/* 图标 */}
                    <div
                      className={`absolute top-4 right-4 bg-gradient-to-r ${useCase.gradient} rounded-full p-3 shadow-lg transform translate-y-2 group-hover:translate-y-0 transition-transform duration-300 z-10`}
                    >
                      <Icon className="w-5 h-5 text-white" />
                    </div>
                  </div>

                  {/* 内容部分 */}
                  <div className="space-y-4">
                    <h3 className="text-2xl font-bold text-white leading-tight group-hover:text-purple-300 transition-colors duration-300">
                      {useCase.title}
                    </h3>

                    <p className="text-gray-300 leading-relaxed">
                      {useCase.description}
                    </p>

                    {/* CTA按钮 */}
                    <div className="pt-4">
                      <Link href={toolUrl}>
                        <Button
                          className={`group/btn bg-gradient-to-r ${useCase.gradient} hover:shadow-lg hover:shadow-purple-500/25 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105 w-full sm:w-auto`}
                        >
                          {useCase.buttonText}
                          <ArrowRight className="w-4 h-4 ml-2 group-hover/btn:translate-x-1 transition-transform" />
                        </Button>
                      </Link>
                    </div>
                  </div>

                  {/* 悬浮装饰 */}
                  {hoveredCase === index && (
                    <div className="absolute -top-2 -right-2 w-4 h-4 bg-purple-400 rounded-full animate-ping" />
                  )}
                </div>
              </div>
            )
          })}
        </div>

        {/* 底部CTA */}
        <div
          className={`text-center mt-16 transition-all duration-1000 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
          style={{ transitionDelay: '800ms' }}
        >
          <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-8 max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-white mb-4">
              {t('useCasesBottomTitle')}
            </h3>
            <p className="text-gray-300 mb-6">
              {t('useCasesBottomDescription')}
            </p>
            <Link href={toolUrl}>
              <Button
                size="lg"
                className="group max-md:whitespace-nowrap mx-auto bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-purple-500/25 transition-all duration-300 hover:scale-105"
              >
                <Sparkles className="w-5 h-5 mr-2 group-hover:animate-pulse" />
                {t('useCasesBottomButton')}
                <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  )
}

export default UseCasesSection
