'use client'

import { useState, useEffect, useRef } from 'react'
import { Star, Quote, ArrowLeft, ArrowRight, Sparkles } from 'lucide-react'
import { useTranslations } from 'next-intl'

const TestimonialsSection = () => {
  const t = useTranslations('aiTextToImage')
  const [isVisible, setIsVisible] = useState(false)
  const [currentTestimonial, setCurrentTestimonial] = useState(0)
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.1 }
    )

    if (sectionRef.current) {
      observer.observe(sectionRef.current)
    }

    return () => observer.disconnect()
  }, [])

  useEffect(() => {
    if (isVisible) {
      const interval = setInterval(() => {
        setCurrentTestimonial((prev) => (prev + 1) % testimonials.length)
      }, 5000)
      return () => clearInterval(interval)
    }
  }, [isVisible])

  const testimonials = [
    {
      content: t('testimonial1Content'),
      author: t('testimonial1Author'),
      role: t('testimonial1Role'),
      company: t('testimonial1Company'),
      avatar:
        'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=200&h=200&fit=crop&crop=face&auto=format',
      rating: 5,
    },
    {
      content: t('testimonial2Content'),
      author: t('testimonial2Author'),
      role: t('testimonial2Role'),
      company: t('testimonial2Company'),
      avatar:
        'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
      rating: 5,
    },
    {
      content: t('testimonial3Content'),
      author: t('testimonial3Author'),
      role: t('testimonial3Role'),
      company: t('testimonial3Company'),
      avatar:
        'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
      rating: 5,
    },
    {
      content: t('testimonial4Content'),
      author: t('testimonial4Author'),
      role: t('testimonial4Role'),
      company: t('testimonial4Company'),
      avatar:
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
      rating: 5,
    },
  ]

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length)
  }

  const prevTestimonial = () => {
    setCurrentTestimonial(
      (prev) => (prev - 1 + testimonials.length) % testimonials.length
    )
  }

  return (
    <section
      ref={sectionRef}
      className="py-20 bg-gradient-to-b from-slate-800 to-slate-900 relative overflow-hidden"
    >
      {/* 背景装饰 */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-72 h-72 bg-purple-500/5 rounded-full blur-3xl animate-pulse" />
        <div
          className="absolute bottom-20 right-20 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl animate-pulse"
          style={{ animationDelay: '3s' }}
        />
      </div>

      <div className="relative z-10 container mx-auto px-4">
        {/* 标题部分 */}
        <div
          className={`text-center mb-16 transition-all duration-1000 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
        >
          <div className="inline-flex items-center gap-2 bg-purple-500/20 backdrop-blur-sm border border-purple-500/30 rounded-full px-4 py-2 text-purple-300 text-sm font-medium mb-6">
            <Sparkles className="w-4 h-4" />
            {t('testimonialsTagText')}
          </div>

          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            {t('testimonialsTitle')}{' '}
            <span className="text-pink-400 ">
              {t('testimonialsTitleHighlight')}
            </span>
          </h2>
        </div>

        {/* 主要见证 */}
        <div className="max-w-4xl mx-auto mb-16">
          <div
            className={`relative transition-all duration-1000 ${
              isVisible
                ? 'opacity-100 translate-y-0'
                : 'opacity-0 translate-y-10'
            }`}
            style={{ transitionDelay: '200ms' }}
          >
            <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-8 md:p-12 relative overflow-hidden">
              {/* 背景引号 */}
              <div className="absolute top-6 left-6 opacity-10">
                <Quote className="w-16 h-16 text-purple-400" />
              </div>

              {/* 评分 */}
              <div className="flex justify-center mb-6">
                <div className="flex gap-1">
                  {[...Array(testimonials[currentTestimonial].rating)].map(
                    (_, i) => (
                      <Star
                        key={i}
                        className="w-6 h-6 text-yellow-400 fill-current"
                      />
                    )
                  )}
                </div>
              </div>

              {/* 见证内容 */}
              <div className="text-center mb-8">
                <p className="text-xl md:text-2xl text-gray-300 leading-relaxed italic">
                  "{testimonials[currentTestimonial].content}"
                </p>
              </div>

              {/* 作者信息 */}
              <div className="flex items-center justify-center gap-4">
                <img
                  src={testimonials[currentTestimonial].avatar}
                  alt={testimonials[currentTestimonial].author}
                  className="w-16 h-16 rounded-full border-2 border-purple-500/30"
                />
                <div className="text-center">
                  <h4 className="text-white font-semibold text-lg">
                    {testimonials[currentTestimonial].author}
                  </h4>
                  <p className="text-purple-300 text-sm">
                    {testimonials[currentTestimonial].role}
                  </p>
                  <p className="text-gray-400 text-xs">
                    {testimonials[currentTestimonial].company}
                  </p>
                </div>
              </div>

              {/* 导航按钮 */}
              <div className="absolute top-1/2 -translate-y-1/2 left-4">
                <button
                  onClick={prevTestimonial}
                  className="w-10 h-10 bg-slate-700/50 hover:bg-purple-600/50 border border-slate-600 hover:border-purple-500 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110"
                >
                  <ArrowLeft className="w-5 h-5 text-gray-300 hover:text-white" />
                </button>
              </div>

              <div className="absolute top-1/2 -translate-y-1/2 right-4">
                <button
                  onClick={nextTestimonial}
                  className="w-10 h-10 bg-slate-700/50 hover:bg-purple-600/50 border border-slate-600 hover:border-purple-500 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110"
                >
                  <ArrowRight className="w-5 h-5 text-gray-300 hover:text-white" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* 见证指示器 */}
        <div className="flex justify-center gap-3 mb-16">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentTestimonial(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                currentTestimonial === index
                  ? 'bg-purple-400 w-8'
                  : 'bg-gray-600 hover:bg-gray-500'
              }`}
            />
          ))}
        </div>

        {/* 所有见证的网格展示 */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {testimonials.map((testimonial, index) => (
            <div
              key={index}
              className={`group transition-all duration-1000 ${
                isVisible
                  ? 'opacity-100 translate-y-0'
                  : 'opacity-0 translate-y-10'
              } ${currentTestimonial === index ? 'scale-105' : ''}`}
              style={{ transitionDelay: `${400 + index * 100}ms` }}
            >
              <div
                className={`bg-slate-800/30 backdrop-blur-sm border rounded-xl p-6 h-full transition-all duration-300 ${
                  currentTestimonial === index
                    ? 'border-purple-500/50 shadow-lg shadow-purple-500/20'
                    : 'border-slate-700/30 hover:border-purple-500/30'
                }`}
              >
                {/* 评分 */}
                <div className="flex gap-1 mb-3">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star
                      key={i}
                      className="w-4 h-4 text-yellow-400 fill-current"
                    />
                  ))}
                </div>

                {/* 内容 */}
                <p className="text-gray-300 text-sm leading-relaxed mb-4 line-clamp-5">
                  "{testimonial.content}"
                </p>

                {/* 作者 */}
                <div className="flex items-center gap-3">
                  <img
                    src={testimonial.avatar}
                    alt={testimonial.author}
                    className="w-10 h-10 rounded-full border border-purple-500/30"
                  />
                  <div>
                    <h5 className="text-white font-medium text-sm">
                      {testimonial.author}
                    </h5>
                    <p className="text-gray-400 text-xs">{testimonial.role}</p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default TestimonialsSection
