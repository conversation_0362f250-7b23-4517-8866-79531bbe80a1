import React from 'react'
import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'
import HeroSection from './components/HeroSection'
import ProductAdvantagesSection from './components/ProductAdvantagesSection'
import UseCasesSection from './components/UseCasesSection'
import HowToGuideSection from './components/HowToGuideSection'
import TestimonialsSection from './components/TestimonialsSection'
import FAQSection from './components/FAQSection'
import './styles.css'

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('aiTextToImage')

  return {
    title: t('title'),
    description: t('description'),
    keywords: t('keywords'),
    openGraph: {
      title: t('openGraphTitle'),
      description: t('openGraphDescription'),
      images: [
        {
          url: 'https://imggen.ai/og-image.jpg',
          width: 1200,
          height: 630,
          alt: t('openGraphAlt'),
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: t('twitterTitle'),
      description: t('twitterDescription'),
      images: ['https://imggen.ai/twitter-card.jpg'],
    },
  }
}

const TextToImagePage = async () => {
  // 统一配置跳转URL
  const AI_TEXT_TO_IMAGE_TOOL_URL = '/ai/text-to-image'
  const t = await getTranslations('aiTextToImage')

  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: t('jsonLdName'),
    description: t('jsonLdDescription'),
    applicationCategory: 'MultimediaApplication',
    operatingSystem: 'Web',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
    },
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: '4.9',
      reviewCount: '1250',
    },
    keywords: t('jsonLdKeywords'),
  }

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <main className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        <HeroSection toolUrl={AI_TEXT_TO_IMAGE_TOOL_URL} />
        <ProductAdvantagesSection toolUrl={AI_TEXT_TO_IMAGE_TOOL_URL} />
        <UseCasesSection toolUrl={AI_TEXT_TO_IMAGE_TOOL_URL} />
        <HowToGuideSection toolUrl={AI_TEXT_TO_IMAGE_TOOL_URL} />
        <TestimonialsSection />
        <FAQSection toolUrl={AI_TEXT_TO_IMAGE_TOOL_URL} />
      </main>
    </>
  )
}

export default TextToImagePage
