'use client'

import { Tilt } from '@jdion/tilt-react'
import Image from 'next/image'

interface Props {
  title: string
  images: string[]
}

export default function SampleImageClient({ title, images }: Props) {
  return (
    <div className="mt-8">
      <p className="text-gray-400 mb-4">{title}</p>
      <div className="grid grid-cols-4 gap-4 [perspective:1200px]">
        {images.map((item) => (
          <Tilt key={item}>
            <div className="relative aspect-square rounded-lg overflow-hidden cursor-pointer border-2 border-transparent transition-all duration-300 hover:border-purple-500 hover:[transform:rotateX(10deg)_rotateY(-10deg)_scale(1.1)] hover:shadow-2xl hover:shadow-purple-500/30">
              <Image
                src={`/samples/${item}`}
                alt="Sample Image"
                fill
                className="object-cover bg-slate-800"
              />
            </div>
          </Tilt>
        ))}
      </div>
    </div>
  )
}
