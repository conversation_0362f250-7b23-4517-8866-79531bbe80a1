import { Link } from '@i18n/routing'
import { getTranslations } from 'next-intl/server'

export default async function FAQSection({ toolUrl }: { toolUrl: string }) {
  const t = await getTranslations()
  const faqs = [
    {
      question: t('phototoanime.faq1Question'),
      answer: t('phototoanime.faq1Answer'),
    },
    {
      question: t('phototoanime.faq2Question'),
      answer: t('phototoanime.faq2Answer'),
    },
    {
      question: t('phototoanime.faq3Question'),
      answer: t('phototoanime.faq3Answer'),
    },
    {
      question: t('phototoanime.faq4Question'),
      answer: t('phototoanime.faq4Answer'),
    },
  ]

  return (
    <section className="py-24 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-5xl font-bold mb-6">
            <span className="text-pink-400">{t('phototoanime.faqTitle')}</span>
          </h2>
          <p className="text-lg text-white/80 max-w-3xl mx-auto">
            {t('phototoanime.faqDescription')}
          </p>
        </div>

        {/* FAQ Items - Now using a CSS-only accordion */}
        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <div
              key={index}
              className="border border-white/10 rounded-2xl overflow-hidden"
            >
              {/* Hidden checkbox to control the state */}
              <input
                type="checkbox"
                id={`faq-final-${index}`}
                className="absolute opacity-0 peer"
                // Open the first item by default
                defaultChecked={index === 0}
              />

              {/* Label acts as the clickable header */}
              <label
                htmlFor={`faq-final-${index}`}
                className="w-full text-left p-6 flex items-center justify-between cursor-pointer hover:bg-white/5 transition-colors duration-200"
              >
                <h3 className="text-lg md:text-xl font-semibold text-white pr-4">
                  {faq.question}
                </h3>
                <div
                  className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center transition-transform duration-300
                             peer-checked:rotate-180"
                >
                  <i className="fas fa-chevron-down text-white text-sm" />
                </div>
              </label>

              {/* Answer Panel animates with CSS Grid */}
              <div
                className="grid transition-all duration-500 ease-in-out
                           grid-rows-[0fr] peer-checked:grid-rows-[1fr]"
              >
                <div className="overflow-hidden">
                  <div className="px-6 pb-6 border-t border-white/10">
                    <p className="text-white/80 leading-relaxed pt-4">
                      {faq.answer}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Additional Help - AnimatedCard wrapper removed */}
        <div className="mt-16 text-center">
          <div className="p-8 bg-gradient-to-r from-purple-600/10 to-pink-600/10 backdrop-blur-sm border border-white/10 rounded-2xl">
            <div className="flex flex-col items-center gap-4">
              <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center">
                <i className="fas fa-question-circle text-white text-xl" />
              </div>
              <h3 className="text-xl font-semibold text-white">
                {t('phototoanime.faqHelpTitle')}
              </h3>
              <p className="text-white/70 max-w-md">
                {t('phototoanime.faqHelpDescription')}
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  href={toolUrl}
                  className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl hover:scale-105 transition-transform duration-200"
                >
                  <i className="fas fa-envelope mr-2" />
                  {t('phototoanime.faqHelpCtaContact')}
                </Link>
                <Link
                  href={toolUrl}
                  className="px-6 py-3 bg-white/10 backdrop-blur-sm border border-white/20 text-white rounded-xl hover:bg-white/20 transition-colors duration-200"
                >
                  <i className="fas fa-book mr-2" />
                  {t('phototoanime.faqHelpCtaDocs')}
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
