'use client'
import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { useTranslations } from 'next-intl'

export default function Features() {
  const t = useTranslations('aiRemoveWatermark')
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  const features = [
    {
      icon: '🎯',
      title: t('feature1Title'),
      description: t('feature1Description'),
      gradient: 'from-purple-500 to-pink-500',
    },
    {
      icon: '✨',
      title: t('feature2Title'),
      description: t('feature2Description'),
      gradient: 'from-purple-500 to-pink-500',
    },
    {
      icon: '⚡',
      title: t('feature3Title'),
      description: t('feature3Description'),
      gradient: 'from-purple-500 to-pink-500',
    },
    {
      icon: '🎯',
      title: t('feature4Title'),
      description: t('feature4Description'),
      gradient: 'from-purple-500 to-pink-500',
    },
    {
      icon: '📱',
      title: t('feature5Title'),
      description: t('feature5Description'),
      gradient: 'from-purple-500 to-pink-500',
    },
    {
      icon: '🔒',
      title: t('feature6Title'),
      description: t('feature6Description'),
      gradient: 'from-purple-500 to-pink-500',
    },
  ]

  const stats = [
    { number: t('stat1Number'), label: t('stat1Label') },
    { number: t('stat2Number'), label: t('stat2Label') },
    { number: t('stat3Number'), label: t('stat3Label') },
    { number: t('stat4Number'), label: t('stat4Label') },
  ]

  return (
    <section className="w-full py-24 bg-gradient-to-b from-slate-900 to-gray-900">
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            <span className="text-purple-500">{t('featuresTitle')}</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            {t('featuresDescription')}
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              className="group relative"
            >
              <div className="relative p-8 overflow-hidden bg-white/5 backdrop-blur border border-white/10 rounded-2xl hover:bg-white/10 transition-all duration-300 h-full">
                {/* 悬停光效 */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 rounded-2xl"></div>

                {/* 图标 */}
                <div
                  className={`inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl mb-6 text-2xl`}
                >
                  {feature.icon}
                </div>

                {/* 内容 */}
                <h3 className="text-xl font-bold text-white mb-4 group-hover:text-cyan-300 transition-colors duration-300">
                  {feature.title}
                </h3>
                <p className="text-gray-300 leading-relaxed">
                  {feature.description}
                </p>

                {/* 装饰性边框 */}
                <div
                  className={`absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-purple-500 to-pink-500 rounded-t-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300`}
                ></div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* 底部统计 */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8 text-center"
        >
          {stats.map((stat, index) => (
            <div key={index} className="group">
              <div className="text-3xl md:text-4xl font-bold text-purple-500 mb-2 group-hover:scale-110 transition-transform duration-300">
                {stat.number}
              </div>
              <div className="text-gray-300 text-sm font-medium">
                {stat.label}
              </div>
            </div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}
