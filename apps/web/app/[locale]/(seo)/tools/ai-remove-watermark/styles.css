/* AI Remove Watermark Page Styles */

/* 确保页面滚动平滑 */
html {
  scroll-behavior: smooth;
}

/* 动画关键帧 */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse-glow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(162, 28, 175, 0.3),
      0 0 20px rgba(236, 72, 153, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(162, 28, 175, 0.6),
      0 0 40px rgba(236, 72, 153, 0.6);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 工具类 */
.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-shimmer {
  animation: shimmer 2s ease-in-out infinite;
}

.faq-glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(16px);
  border-radius: 2rem;
  border: 4px solid;
  border-image: linear-gradient(90deg, #a21caf 0%, #ec4899 100%);
  border-image-slice: 1;
  box-shadow: 0 4px 32px 0 rgba(162, 28, 175, 0.1),
    0 1.5px 8px 0 rgba(236, 72, 153, 0.1);
  transition: box-shadow 0.3s, transform 0.3s;
}
.faq-glass:hover,
.faq-glass.faq-open {
  box-shadow: 0 8px 48px 0 rgba(162, 28, 175, 0.18),
    0 3px 16px 0 rgba(236, 72, 153, 0.18);
  transform: translateY(-2px) scale(1.025);
}
.faq-qicon {
  background: linear-gradient(135deg, #a21caf 0%, #ec4899 100%);
  color: white;
  border-radius: 9999px;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  margin-right: 1rem;
  box-shadow: 0 2px 8px 0 rgba(162, 28, 175, 0.1);
}
.faq-divider {
  height: 2px;
  width: 100%;
  background: linear-gradient(90deg, #a21caf 0%, #ec4899 100%);
  opacity: 0.18;
  border-radius: 1px;
  margin: 1rem 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
    line-height: 1.2;
  }

  .hero-subtitle {
    font-size: 1.125rem;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .bg-white\/5 {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .border-white\/10 {
    border-color: rgba(255, 255, 255, 0.2);
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }
}
