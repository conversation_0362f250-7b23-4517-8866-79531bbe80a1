import React from 'react'
import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'
import HeaderSection from './components/HeaderSection'
import WhyChooseSection from './components/WhyChooseSection'
import UseCaseSection from './components/UseCaseSection'
import HowToGuideSection from './components/HowToGuideSection'
import TestimonialSection from './components/TestimonialSection'
import TryFreeSection from './components/TryFreeSection'
import FAQSection from './components/FAQSection'

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('imageExtenter')

  return {
    title: t('title'),
    description: t('description'),
    keywords: t('keywords'),
    openGraph: {
      title: t('openGraphTitle'),
      description: t('openGraphDescription'),
      url: 'https://imggen.ai/ai-image-extender',
      images: [
        {
          url: 'https://imggen.ai/images/og-image-extender.jpg',
          width: 1200,
          height: 630,
          alt: 'AI Image Extender Tool',
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: t('twitterTitle'),
      description: t('twitterDescription'),
      images: ['https://imggen.ai/images/twitter-card-extender.jpg'],
    },
  }
}

const ImageExtenderPage = async () => {
  const t = await getTranslations('imageExtenter')

  // 统一配置跳转URL
  const AI_IMAGE_EXTENDER_TOOL_URL = '/ai/image-extender'

  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: t('jsonLdName'),
    description: t('jsonLdDescription'),
    url: 'https://www.imggen.org/tools/image-extenter',
    applicationCategory: 'DesignApplication',
    operatingSystem: 'Web',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
      availability: 'https://schema.org/InStock',
    },
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: '4.8',
      reviewCount: '1250',
    },
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': 'https://www.imggen.org/tools/image-extenter',
    },
  }

  return (
    <div className="min-h-screen bg-slate-900">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />

      {/* Header Section */}
      <HeaderSection toolUrl={AI_IMAGE_EXTENDER_TOOL_URL} />

      {/* Main Content */}
      <WhyChooseSection toolUrl={AI_IMAGE_EXTENDER_TOOL_URL} />
      <UseCaseSection toolUrl={AI_IMAGE_EXTENDER_TOOL_URL} />
      <HowToGuideSection toolUrl={AI_IMAGE_EXTENDER_TOOL_URL} />
      <TestimonialSection />
      <TryFreeSection toolUrl={AI_IMAGE_EXTENDER_TOOL_URL} />
      <FAQSection toolUrl={AI_IMAGE_EXTENDER_TOOL_URL} />
    </div>
  )
}

export default ImageExtenderPage
