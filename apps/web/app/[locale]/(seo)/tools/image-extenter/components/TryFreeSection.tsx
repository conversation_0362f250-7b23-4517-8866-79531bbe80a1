'use client'

import React from 'react'
import { useTranslations } from 'next-intl'
import { Link } from '@i18n/routing'

interface TryFreeSectionProps {
  toolUrl: string
}

const TryFreeSection: React.FC<TryFreeSectionProps> = ({ toolUrl }) => {
  const t = useTranslations('imageExtenter')

  return (
    <section className="py-20 bg-slate-900 relative overflow-hidden">
      {/* 背景装饰圆圈 */}
      <div className="absolute inset-0">
        {/* 左上角蓝色圆圈 */}
        <div className="absolute top-10 left-10 w-16 h-16 bg-blue-500/20 rounded-full blur-xl"></div>

        {/* 右上角蓝色圆圈 */}
        <div className="absolute top-20 right-20 w-12 h-12 bg-blue-400/30 rounded-full blur-lg"></div>

        {/* 左下角蓝色圆圈 */}
        <div className="absolute bottom-32 left-16 w-10 h-10 bg-blue-600/25 rounded-full blur-lg"></div>

        {/* 右下角蓝色圆圈 */}
        <div className="absolute bottom-10 right-32 w-14 h-14 bg-blue-500/20 rounded-full blur-xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* 左侧用户头像 */}
            <div className="relative">
              {/* 左上角头像 */}
              <div className="absolute top-0 left-8 w-20 h-20 rounded-full overflow-hidden border-4 border-blue-400/50 shadow-xl">
                <img
                  src="https://images.unsplash.com/photo-1494790108755-2616c9c0e8e3?w=100&h=100&fit=crop&crop=face"
                  alt="User avatar"
                  className="w-full h-full object-cover"
                />
              </div>

              {/* 左下角头像 */}
              <div className="absolute bottom-0 left-0 w-24 h-24 rounded-full overflow-hidden border-4 border-blue-500/50 shadow-xl">
                <img
                  src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face"
                  alt="User avatar"
                  className="w-full h-full object-cover"
                />
              </div>

              {/* 右上角头像 */}
              <div className="absolute top-8 right-0 w-28 h-28 rounded-full overflow-hidden border-4 border-blue-400/50 shadow-xl">
                <img
                  src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face"
                  alt="User avatar"
                  className="w-full h-full object-cover"
                />
              </div>

              {/* 右下角头像 */}
              <div className="absolute bottom-8 right-8 w-22 h-22 rounded-full overflow-hidden border-4 border-blue-500/50 shadow-xl">
                <img
                  src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face"
                  alt="User avatar"
                  className="w-full h-full object-cover"
                />
              </div>

              {/* 占位空间 */}
              <div className="h-80"></div>
            </div>

            {/* 右侧内容 */}
            <div className="text-center lg:text-left">
              {/* 中央图标 */}
              <div className="flex justify-center lg:justify-start mb-8">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                  <svg
                    className="w-8 h-8 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                    />
                  </svg>
                </div>
              </div>

              {/* 标题 */}
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 leading-tight">
                {t('tryFreeTitle1')}
                <br />
                {t('tryFreeTitle2')}
                <br />
                {/* <span className="text-blue-400">{t('tryFreeTitle3')}</span> */}
              </h2>

              {/* CTA按钮 */}
              <div className="flex mt-2 flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <Link
                  href={toolUrl}
                  className="inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold text-lg rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <span>{t('tryFreeButton')}</span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default TryFreeSection
