'use client'

import React from 'react'

interface PhoneWallpaperComparisonProps {
  originalImage: string
  extendedImage: string
  alt: string
}

const PhoneWallpaperComparison: React.FC<PhoneWallpaperComparisonProps> = ({
  originalImage,
  extendedImage,
  alt,
}) => {
  return (
    <div className="relative flex items-center justify-center gap-8 p-8">
      {/* 左侧 - 原始正方形图片 */}
      <div className="relative group">
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-sm border border-slate-700/50 group-hover:border-cyan-500/50 transition-all duration-500">
          <div className="w-56 h-56 relative">
            <img
              src={originalImage}
              alt={`Original ${alt}`}
              className="w-full h-full object-cover transform transition duration-700 group-hover:scale-105"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-black/20"></div>

            {/* 悬浮效果 */}
            <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          </div>
        </div>

        {/* 标签 */}
        <div className="absolute -bottom-3 left-1/2 transform -translate-x-1/2">
          <div className="bg-slate-800 border border-slate-600 rounded-full px-3 py-1 text-xs text-gray-300 font-medium">
            Original
          </div>
        </div>
      </div>

      {/* 箭头指示器 */}
      {/* <div className="flex flex-col items-center">
        <div className="w-8 h-0.5 bg-gradient-to-r from-cyan-400 to-purple-400 mb-2"></div>
        <svg
          className="w-6 h-6 text-cyan-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 5l7 7-7 7"
          />
        </svg>
        <div className="w-8 h-0.5 bg-gradient-to-r from-cyan-400 to-purple-400 mt-2"></div>
      </div> */}

      {/* 右侧 - 手机形状的扩展图片 */}
      <div className="relative group ml-4">
        <div className="relative">
          {/* 手机外框 */}
          <div className="relative w-64 aspect-[208/384] bg-gradient-to-b from-slate-700 to-slate-800 rounded-[2.5rem] p-2 shadow-2xl border border-slate-600 group-hover:border-cyan-500/50 transition-all duration-500">
            {/* 屏幕区域 */}
            <div className="w-full h-full bg-black rounded-[2rem] overflow-hidden relative">
              {/* 刘海/动态岛 */}
              <div className="absolute top-3 left-1/2 transform -translate-x-1/2 w-20 h-7 bg-slate-800 rounded-full z-10"></div>

              {/* 扩展后的图片 */}
              <img
                src={extendedImage}
                alt={`Extended ${alt}`}
                className="w-full h-full object-cover transform transition duration-700 group-hover:scale-105"
              />

              {/* 屏幕反光效果 */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-transparent"></div>

              {/* 悬浮效果 */}
              <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            </div>

            {/* 手机按键 */}
            <div className="absolute right-0 top-24 w-1 h-10 bg-slate-600 rounded-l-sm"></div>
            <div className="absolute right-0 top-40 w-1 h-14 bg-slate-600 rounded-l-sm"></div>
            <div className="absolute right-0 top-60 w-1 h-14 bg-slate-600 rounded-l-sm"></div>
          </div>
        </div>

        {/* 标签 */}
        <div className="absolute -bottom-3 left-1/2 transform -translate-x-1/2">
          <div className="bg-gradient-to-r from-cyan-500 to-purple-500 rounded-full px-3 py-1 text-xs text-white font-medium">
            Extended
          </div>
        </div>
      </div>

      {/* 背景装饰 */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute top-1/2 left-1/4 w-40 h-40 bg-cyan-500/5 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 right-1/4 w-40 h-40 bg-purple-500/5 rounded-full blur-3xl"></div>
      </div>
    </div>
  )
}

export default PhoneWallpaperComparison
