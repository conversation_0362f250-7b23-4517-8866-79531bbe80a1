'use client'

import React from 'react'
import PhoneWallpaperComparison from './PhoneWallpaperComparison'

const TestPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-slate-900 py-20">
      <div className="container mx-auto px-4">
        <h1 className="text-4xl font-bold text-white text-center mb-12">
          Phone Wallpaper Comparison Test
        </h1>
        
        <div className="max-w-4xl mx-auto">
          <PhoneWallpaperComparison
            originalImage="https://images.unsplash.com/photo-1494790108755-2616c9c0e8e3?w=400&h=400&fit=crop"
            extendedImage="https://images.unsplash.com/photo-1494790108755-2616c9c0e8e3?w=600&h=800&fit=crop"
            alt="Portrait photo expanded for mobile wallpaper"
          />
        </div>
      </div>
    </div>
  )
}

export default TestPage
