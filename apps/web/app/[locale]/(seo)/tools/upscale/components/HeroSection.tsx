import React from 'react'
import { UploadCloud, ArrowR<PERSON>, CheckCircle } from 'lucide-react'
import ImageCompare from '../../components/ImageCompare'
import SampleImageClient from './SampleImageClient'
import { Link } from '@i18n/routing'
import { getTranslations } from 'next-intl/server'

const HeroSection = async ({ toolUrl }: { toolUrl: string }) => {
  const t = await getTranslations('upscale')

  return (
    <div className="relative z-10 my-14">
      <div className="relative container mx-auto px-4 md:px-8 py-12 md:py-20">
        {/* 标题区域 - 居中显示 */}
        <div className="text-center mb-16 animate-fade-in-up">
          <div className="inline-flex items-center px-3 py-1 rounded-full bg-purple-500/10 border border-purple-500/20 text-purple-400 text-sm font-medium mb-6">
            <span className="w-2 h-2 bg-purple-400 rounded-full mr-2 animate-pulse"></span>
            {t('heroAiPowered')}
          </div>

          <h1 className="text-4xl md:text-5xl xl:text-6xl font-bold leading-tight text-fuchsia-400 mb-6">
            {t('heroTitle')}
          </h1>

          <p className="text-lg md:text-xl text-gray-300 font-light leading-relaxed max-w-3xl mx-auto">
            {t('heroDescription')}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-start">
          {/* Left Column - 重新组织内容 */}
          <div className="text-white animate-fade-in-up space-y-8">
            {/* 特性列表 */}
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                <span className="text-gray-300">{t('heroFeature1')}</span>
              </div>
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                <span className="text-gray-300">{t('heroFeature2')}</span>
              </div>
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                <span className="text-gray-300">{t('heroFeature3')}</span>
              </div>
            </div>

            {/* 上传区域 */}
            <div className="w-full group">
              <div className="border-2 border-dashed border-gray-600 rounded-xl p-8 bg-slate-900/30 backdrop-blur-sm transition-all duration-500 group-hover:border-purple-500 group-hover:bg-slate-900/50">
                <div className="flex flex-col items-center text-center space-y-4">
                  <Link href={toolUrl}>
                    <button className="relative inline-flex items-center px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-purple-500/25 group/btn">
                      <UploadCloud className="w-6 h-6 mr-3 transition-transform duration-300 group-hover/btn:rotate-12" />
                      <span>{t('heroUpload')}</span>
                      <ArrowRight className="w-5 h-5 ml-2 transition-transform duration-300 group-hover/btn:translate-x-1" />
                    </button>
                  </Link>

                  <p className="text-gray-400 text-lg">{t('heroDrag')}</p>

                  {/* 支持的格式提示 */}
                  <div className="flex items-center justify-center space-x-6 text-sm text-gray-500">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                      <span>{t('heroJpg')}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                      <span>{t('heroPng')}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                      <span>{t('heroWebp')}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* 底部示例图片组件 */}
            <div
              className="mt-16 animate-fade-in-up"
              style={{ animationDelay: '1s' }}
            >
              <SampleImageClient />
            </div>

            {/* 移动端图片对比 */}
            <div className="block lg:hidden">
              <div className="text-center mb-6">
                <h3 className="text-2xl font-semibold text-white mb-2">
                  {t('heroBeforeAfter')}
                </h3>
                <p className="text-gray-400">{t('heroDifference')}</p>
              </div>
              <div className="rounded-2xl overflow-hidden shadow-2xl border border-gray-700">
                <ImageCompare
                  leftImage="/samples/image-upscaler-low-res-photo_before.webp"
                  rightImage="/samples/image-upscaler-low-res-photo_after.webp"
                />
              </div>
            </div>
          </div>

          {/* Right Column - 增加更多内容 */}
          <div
            className="hidden lg:block animate-fade-in-up space-y-8"
            style={{ animationDelay: '0.5s' }}
          >
            {/* 图片对比区域 */}
            <div className="relative">
              <div className="text-center mb-6">
                <h3 className="text-2xl font-semibold text-white mb-2">
                  {t('heroBeforeAfter')}
                </h3>
                <p className="text-gray-400">{t('heroDifference')}</p>
              </div>

              <div className="rounded-2xl relative overflow-hidden shadow-2xl border border-gray-700 bg-gradient-to-br from-slate-800/50 to-slate-900/50">
                <ImageCompare
                  leftImage="/samples/image-upscaler-low-res-photo_before.webp"
                  rightImage="/samples/image-upscaler-low-res-photo_after.webp"
                />
                {/* 对比标签 */}
                <div className="absolute top-4 left-4">
                  <span className="px-3 py-1 bg-red-500/80 text-white text-sm font-medium rounded-full backdrop-blur-sm">
                    {t('heroBefore')}
                  </span>
                </div>
                <div className="absolute top-4 right-4">
                  <span className="px-3 py-1 bg-green-500/80 text-white text-sm font-medium rounded-full backdrop-blur-sm">
                    {t('heroAfter')}
                  </span>
                </div>
              </div>
            </div>

            {/* 统计信息 */}
            <div className="grid grid-cols-3 gap-4 text-center">
              <div className="p-4 rounded-xl bg-slate-800/50 border border-gray-700">
                <div className="text-2xl font-bold text-purple-400">
                  {t('hero4k')}
                </div>
                <div className="text-sm text-gray-400">
                  {t('heroMaxResolution')}
                </div>
              </div>
              <div className="p-4 rounded-xl bg-slate-800/50 border border-gray-700">
                <div className="text-2xl font-bold text-pink-400">
                  {t('hero100')}
                </div>
                <div className="text-sm text-gray-400">
                  {t('heroFreeToUse')}
                </div>
              </div>
              <div className="p-4 rounded-xl bg-slate-800/50 border border-gray-700">
                <div className="text-2xl font-bold text-fuchsia-400">
                  {t('heroAi')}
                </div>
                <div className="text-sm text-gray-400">{t('heroPowered')}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default HeroSection
