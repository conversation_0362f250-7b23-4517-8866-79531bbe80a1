import React from 'react'
import { Sparkles } from 'lucide-react'
import { getTranslations } from 'next-intl/server'

export default async function TattooBrandIntro({
  toolUrl,
}: {
  toolUrl: string
}) {
  const t = await getTranslations('Tattoo.TattooBrandIntro')

  return (
    <div className="w-full">
      <div className="max-w-7xl mx-auto px-4 py-16">
        <div className="flex flex-col lg:flex-row gap-12 items-center">
          {/* Left side - All text content */}
          <div className="w-full lg:w-[55%] space-y-12">
            {/* First Section */}
            <div className="space-y-4">
              <div className="inline-block">
                <div className="relative">
                  <span className="absolute -inset-1 rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 opacity-50 blur"></span>
                  <span className="relative inline-block text-xs font-medium text-white bg-gradient-to-r from-blue-600 to-indigo-600 px-3 py-1 rounded-full">
                    <Sparkles className="w-4 h-4 inline-block mr-1" />{' '}
                    {t('badge')}
                  </span>
                </div>
              </div>

              <h2 className="text-3xl md:text-4xl font-bold text-blue-600">
                {t('title')}
              </h2>

              <div className="space-y-3">
                <p className="text-gray-600 text-base leading-relaxed">
                  {t('description1')}
                </p>

                <p className="text-gray-600 text-base leading-relaxed">
                  {t('description2')}
                </p>
              </div>
            </div>

            {/* Second Section */}
            <div className="space-y-4">
              <div className="inline-block">
                <div className="relative">
                  <span className="absolute -inset-1 rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 opacity-50 blur"></span>
                  <span className="relative inline-block text-xs font-medium text-white bg-gradient-to-r from-blue-600 to-indigo-600 px-3 py-1 rounded-full">
                    <Sparkles className="w-4 h-4 inline-block mr-1" />{' '}
                    {t('whatSetsUsApart')}
                  </span>
                </div>
              </div>

              <h2 className="text-3xl md:text-4xl font-bold text-purple-600">
                {t('whatSetsUsApart')}
              </h2>

              <ul className="space-y-2">
                <li className="flex items-start gap-3 text-gray-600 text-base">
                  <span className="w-1.5 h-1.5 rounded-full bg-gradient-to-r from-blue-600 to-indigo-600 mt-2"></span>
                  <span>{t('feature1')}</span>
                </li>
                <li className="flex items-start gap-3 text-gray-600 text-base">
                  <span className="w-1.5 h-1.5 rounded-full bg-gradient-to-r from-blue-600 to-indigo-600 mt-2"></span>
                  <span>{t('feature2')}</span>
                </li>
                <li className="flex items-start gap-3 text-gray-600 text-base">
                  <span className="w-1.5 h-1.5 rounded-full bg-gradient-to-r from-blue-600 to-indigo-600 mt-2"></span>
                  <span>{t('feature3')}</span>
                </li>
                <li className="flex items-start gap-3 text-gray-600 text-base">
                  <span className="w-1.5 h-1.5 rounded-full bg-gradient-to-r from-blue-600 to-indigo-600 mt-2"></span>
                  <span>{t('feature4')}</span>
                </li>
                <li className="flex items-start gap-3 text-gray-600 text-base">
                  <span className="w-1.5 h-1.5 rounded-full bg-gradient-to-r from-blue-600 to-indigo-600 mt-2"></span>
                  <span>{t('feature5')}</span>
                </li>
                <li className="flex items-start gap-3 text-gray-600 text-base">
                  <span className="w-1.5 h-1.5 rounded-full bg-gradient-to-r from-blue-600 to-indigo-600 mt-2"></span>
                  <span>{t('feature6')}</span>
                </li>
              </ul>

              <p className="text-gray-600 text-base leading-relaxed mt-4">
                {t('conclusion')}
              </p>
            </div>
          </div>

          {/* Right side - Single image */}
          <div className="w-full lg:w-[45%] lg:flex lg:items-center lg:justify-center">
            <div className="relative w-full max-w-lg group">
              {/* Background gradient effect */}
              <div
                className="absolute -inset-4 rounded-[2rem] opacity-30 blur-3xl transition-all duration-500 group-hover:opacity-40 group-hover:scale-105"
                style={{
                  background:
                    'linear-gradient(90deg, #3b82f6 0%, #6366f1 50%, #9333ea 100%)',
                }}
              />

              {/* Image container with decorative elements */}
              <div className="relative">
                {/* Decorative border */}
                <div className="absolute -inset-1 rounded-[1.5rem] bg-gradient-to-r from-blue-600/20 via-indigo-500/20 to-purple-600/20 backdrop-blur-sm" />

                {/* Main image */}
                <div className="relative rounded-[1.5rem] overflow-hidden">
                  <img
                    src="/tattoo/phoenix.jpg"
                    alt={t('imageAlt')}
                    className="w-full h-full object-cover transform transition-all duration-500 group-hover:scale-105"
                  />

                  {/* Overlay gradient */}
                  <div className="absolute inset-0 bg-gradient-to-tr from-blue-600/10 via-transparent to-purple-600/10" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
