import { getTranslations } from 'next-intl/server'
import AnimatedCard from './AnimatedCard'
import CTAButton from './CTAButton'

export default async function UseCasesSection({
  toolUrl,
}: {
  toolUrl: string
}) {
  const t = await getTranslations('aiClothesChanger')
  const useCases = [
    {
      title: t('useCase1Title'),
      description: t('useCase1Description'),
      image: '/images/ai-clothes-changer/use-case-1.jpg',
      alt: 'A model using the virtual dressing room to try on different clothes with the AI clothes changer',
      ctaText: t('useCase1Cta'),
      stats: [
        {
          icon: 'fas fa-chart-line',
          value: '25%',
          label: t('conversionIncrease'),
        },
        { icon: 'fas fa-undo', value: '40%', label: t('returnReduction') },
        {
          icon: 'fas fa-heart',
          value: '90%',
          label: t('customerSatisfaction'),
        },
      ],
    },
    {
      title: t('useCase2Title'),
      description: t('useCase2Description'),
      image: '/images/ai-clothes-changer/use-case-2.jpg',
      alt: 'An influencer using the dress change AI to create magic clothing for a viral social media post',
      icon: 'fas fa-share-alt',
      ctaText: t('useCase2Cta'),
      stats: [
        { icon: 'fas fa-eye', value: '300%', label: t('engagementBoost') },
        { icon: 'fas fa-users', value: '50K+', label: t('activeCreators') },
        { icon: 'fas fa-clock', value: '2 min', label: t('contentCreation') },
      ],
    },
    {
      title: t('useCase3Title'),
      description: t('useCase3Description'),
      image: '/images/ai-clothes-changer/use-case-3.jpg',
      alt: 'A user planning their wardrobe with the AI clothing generator on their smartphone',
      icon: 'fas fa-user-check',
      ctaText: t('useCase3Cta'),
      stats: [
        {
          icon: 'fas fa-money-bill-wave',
          value: '60%',
          label: t('shoppingSavings'),
        },
        {
          icon: 'fas fa-thumbs-up',
          value: '95%',
          label: t('outfitSatisfaction'),
        },
        { icon: 'fas fa-lightbulb', value: '1000+', label: t('styleIdeas') },
      ],
    },
    {
      title: t('useCase4Title'),
      description: t('useCase4Description'),
      image: '/images/ai-clothes-changer/use-case-4.jpg',
      alt: 'Happy users demonstrating how to swap clothes virtually with AI for free',
      ctaText: t('useCase4Cta'),
      stats: [
        { icon: 'fas fa-users', value: '10K+', label: t('happyUsers') },
        { icon: 'fas fa-dollar-sign', value: 'Free', label: t('freeToStart') },
        { icon: 'fas fa-globe', value: '24/7', label: t('access247') },
      ],
    },
  ]

  return (
    <section className="py-24 px-4 relative">
      <div className="max-w-6xl mx-auto">
        {/* Section Header - H2 with exact README content */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-5xl font-bold mb-6">
            <span className="text-pink-400">{t('useCasesTitle')}</span>
          </h2>
          <p className="text-lg text-white/80 max-w-3xl mx-auto">
            {t('useCasesDescription')}
          </p>
        </div>

        {/* Use Cases Grid */}
        <div className="space-y-16">
          {useCases.map((useCase, index) => (
            <AnimatedCard key={index} delay={index * 100}>
              <div
                className={`flex flex-col ${
                  index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'
                } items-center gap-8`}
              >
                {/* Image */}
                <div className="w-full lg:w-1/2">
                  <div className="relative group">
                    <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl blur opacity-25 group-hover:opacity-75 transition duration-300" />
                    <div className="relative">
                      <img
                        src={useCase.image}
                        alt={useCase.alt}
                        className="w-full h-64 md:h-80 object-cover rounded-2xl"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent rounded-2xl" />
                      {/* Icon Overlay */}
                      {useCase.icon && (
                        <div className="absolute top-4 left-4">
                          <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center">
                            <i
                              className={`${useCase.icon} text-white text-lg`}
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Content */}
                <div className="w-full lg:w-1/2 space-y-6">
                  {/* H3 with exact README content */}
                  <h3 className="text-2xl md:text-3xl font-bold text-white">
                    {useCase.title}
                  </h3>

                  {/* P with exact README content */}
                  <p className="text-white/80 leading-relaxed">
                    {useCase.description}
                  </p>

                  {/* Stats */}
                  <div className="grid grid-cols-3 gap-4">
                    {useCase.stats.map((stat, statIndex) => (
                      <div
                        key={statIndex}
                        className="text-center p-3 bg-white/5 rounded-xl border border-white/10"
                      >
                        <i
                          className={`${stat.icon} text-purple-400 text-lg mb-2`}
                        />
                        <div className="text-white font-bold text-lg">
                          {stat.value}
                        </div>
                        <div className="text-white/60 text-xs">
                          {stat.label}
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* CTA */}
                  <div className="pt-4">
                    <CTAButton href={toolUrl}>
                      <i className="fas fa-arrow-right mr-2" />
                      {useCase.ctaText}
                    </CTAButton>
                  </div>
                </div>
              </div>
            </AnimatedCard>
          ))}
        </div>
      </div>
    </section>
  )
}
