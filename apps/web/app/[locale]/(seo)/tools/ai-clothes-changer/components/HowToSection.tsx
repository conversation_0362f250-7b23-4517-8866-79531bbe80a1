import { getTranslations } from 'next-intl/server'
import AnimatedCard from './AnimatedCard'
import { Link } from '@i18n/routing'

export default async function HowToSection({ toolUrl }: { toolUrl: string }) {
  const t = await getTranslations('aiClothesChanger')
  const steps = [
    {
      number: '01',
      title: t('step1Title'),
      description: t('step1Description'),
      icon: 'fas fa-upload',
      image: '/images/ai-clothes-changer/how-to-1.jpg',
      tips: [
        t('useHighQualityImages'),
        t('ensureGoodLighting'),
        t('frontFacingPoses'),
        t('clearBackgroundPreferred'),
      ],
    },
    {
      number: '02',
      title: t('step2Title'),
      description: t('step2Description'),
      icon: 'fas fa-tshirt',
      image: '/images/ai-clothes-changer/how-to-2.jpg',
      tips: [
        t('browseStyleLibrary'),
        t('describeIdealOutfit'),
        t('mixMatchStyles'),
        t('aiUnderstandsNaturalLanguage'),
      ],
    },
  ]

  return (
    <section className="py-24 px-4 relative">
      <div className="max-w-6xl mx-auto">
        {/* Section Header - H2 with exact README content */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-5xl font-bold mb-6">
            <span className="text-purple-400">{t('howToTitle')}</span>
          </h2>
          <p className="text-lg text-white/80 max-w-3xl mx-auto">
            {t('howToDescription')}
          </p>
        </div>

        {/* Steps */}
        <div className="space-y-12 mb-16">
          {steps.map((step, index) => (
            <AnimatedCard key={index} delay={index * 150}>
              <div className="flex flex-col lg:flex-row items-center gap-8">
                {/* Step Number & Image */}
                <div className="w-full lg:w-1/2 relative">
                  {/* Large Step Number */}
                  <div className="absolute -top-4 -left-4 z-10">
                    <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center">
                      <span className="text-white font-bold text-xl">
                        {step.number}
                      </span>
                    </div>
                  </div>

                  {/* Image */}
                  <div className="relative group">
                    <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl blur opacity-25 group-hover:opacity-75 transition duration-300" />
                    <div className="relative">
                      <img
                        src={step.image}
                        alt={`Step ${step.number}: ${step.title}`}
                        className="w-full h-64 object-cover rounded-2xl"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent rounded-2xl" />

                      {/* Icon */}
                      <div className="absolute bottom-4 right-4">
                        <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center">
                          <i className={`${step.icon} text-white text-lg`} />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Content */}
                <div className="w-full lg:w-1/2 space-y-6">
                  <div className="flex items-center gap-4">
                    <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg flex items-center justify-center flex-shrink-0">
                      <i className={`${step.icon} text-white text-sm`} />
                    </div>
                    {/* H3 with exact README content */}
                    <h3 className="text-2xl md:text-3xl font-bold text-white">
                      {step.title}
                    </h3>
                  </div>

                  {/* P with exact README content */}
                  <p className="text-white/80 leading-relaxed text-lg">
                    {step.description}
                  </p>

                  {/* Tips */}
                  <div className="space-y-3">
                    <h4 className="text-white font-semibold">
                      {t('proTipsLabel')}
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {step.tips.map((tip, tipIndex) => (
                        <div
                          key={tipIndex}
                          className="flex items-center gap-2 text-sm text-white/70"
                        >
                          <i className="fas fa-check-circle text-green-400 text-xs" />
                          <span>{tip}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </AnimatedCard>
          ))}
        </div>

        {/* Process Flow Visualization */}
        <AnimatedCard delay={300}>
          <div className="text-center space-y-8">
            <h3 className="text-2xl font-bold text-white mb-8">
              {t('completeProcessFlowTitle')}
            </h3>

            <div className="flex flex-col md:flex-row items-center justify-center gap-6">
              {/* Step 1 */}
              <div className="flex flex-col items-center">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center mb-3">
                  <i className="fas fa-upload text-white text-xl" />
                </div>
                <span className="text-white text-sm">
                  {t('uploadPhotoLabel')}
                </span>
              </div>

              {/* Arrow */}
              <div className="text-white/40">
                <div className="md:hidden">
                  <i className="fas fa-arrow-down text-2xl" />
                </div>
                <div className="hidden md:block">
                  <i className="fas fa-arrow-right text-2xl" />
                </div>
              </div>

              {/* Processing */}
              <div className="flex flex-col items-center">
                <div className="w-16 h-16 bg-gradient-to-r from-pink-600 to-purple-600 rounded-2xl flex items-center justify-center mb-3">
                  <i className="fas fa-magic text-white text-xl" />
                </div>
                <span className="text-white text-sm">
                  {t('aiProcessingLabel')}
                </span>
              </div>

              {/* Arrow */}
              <div className="text-white/40">
                <div className="hidden md:block">
                  <i className="fas fa-arrow-right text-2xl" />
                </div>
                <div className="md:hidden">
                  <i className="fas fa-arrow-down text-2xl" />
                </div>
              </div>

              {/* Step 2 */}
              <div className="flex flex-col items-center">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center mb-3">
                  <i className="fas fa-tshirt text-white text-xl" />
                </div>
                <span className="text-white text-sm">
                  {t('chooseOutfitLabel')}
                </span>
              </div>

              {/* Arrow */}
              <div className="text-white/40">
                <div className="hidden md:block">
                  <i className="fas fa-arrow-right text-2xl" />
                </div>
                <div className="md:hidden">
                  <i className="fas fa-arrow-down text-2xl" />
                </div>
              </div>

              {/* Result */}
              <div className="flex flex-col items-center">
                <div className="w-16 h-16 bg-gradient-to-r from-pink-600 to-purple-600 rounded-2xl flex items-center justify-center mb-3">
                  <i className="fas fa-download text-white text-xl" />
                </div>
                <span className="text-white text-sm">
                  {t('getResultLabel')}
                </span>
              </div>
            </div>
          </div>
        </AnimatedCard>

        {/* CTA - matching README button text */}
        <div className="text-center mt-12">
          <div className="inline-flex items-center gap-4 px-8 py-4 bg-gradient-to-r from-purple-600/20 to-pink-600/20 backdrop-blur-sm border border-white/10 rounded-2xl">
            <i className="fas fa-rocket text-purple-400 text-xl" />
            <span className="text-white font-medium">
              {t('readyToCreateQuestion')}
            </span>
            <Link
              href={toolUrl}
              className="px-6 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl hover:scale-105 transition-transform duration-200 inline-flex items-center"
            >
              {t('generateFirstDesignButton')}
            </Link>
          </div>
        </div>
      </div>
    </section>
  )
}
