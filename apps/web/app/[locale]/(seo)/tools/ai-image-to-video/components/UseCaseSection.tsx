import React from 'react'
import AnimatedCard from './AnimateCard'
import { Link } from '@i18n/routing'
import { useTranslations } from 'next-intl'

const UseCaseSection = ({ link = '' }) => {
  const t = useTranslations('aiImageToVideo')
  return (
    <section
      id="section-usecases"
      className={`py-20 px-4 transition-all duration-1000 delay-300`}
    >
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-5xl font-bold mb-6 text-purple-200">
            {t('useCaseTitle')}
          </h2>
          <p className="text-xl text-purple-100 max-w-3xl mx-auto">
            {t('useCaseDescription')}
          </p>
        </div>

        <div className="space-y-20">
          {/* Use Case 1 */}
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="group relative">
              <AnimatedCard>
                <div className=" rounded-3xl p-2  transition-all duration-500">
                  <div className="aspect-video overflow-hidden bg-gradient-to-br from-purple-800/50 to-pink-800/50 rounded-2xl flex items-center justify-center">
                    <div className="flex-1 h-full flex justify-center items-center">
                      <img
                        className="w-full h-full object-cover opacity-90"
                        src="/images/ai-image-to-video/butterfly-static.webp"
                        alt="A beautiful butterfly animation created from a static photo using our AI picture animator."
                      />
                    </div>
                    <div className="flex-1 h-full flex justify-center items-center">
                      <video
                        muted
                        autoPlay
                        preload="metadata"
                        loop
                        playsInline
                        className="w-full h-full object-cover opacity-90"
                        src="/videos/ai-image-to-video/butterfly-animate.mp4"
                      ></video>
                    </div>
                  </div>
                </div>
              </AnimatedCard>
            </div>
            <div className="space-y-6">
              <h3 className="text-2xl md:text-3xl font-bold text-purple-100">
                {t('useCase1Title')}
              </h3>
              <p className="text-purple-200 leading-relaxed">
                {t('useCase1Description')}
              </p>
              <button className="group px-6 py-3 bg-gradient-to-r from-pink-500 via-fuchsia-500 to-purple-500 text-white font-semibold rounded-full transition-all duration-300 hover:scale-110 hover:shadow-2xl hover:shadow-pink-500/40 relative overflow-hidden">
                <Link href={link}>{t('animatePhotoNow')}</Link>
              </button>
            </div>
          </div>

          {/* Use Case 2 */}
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6 lg:order-1">
              <h3 className="text-2xl md:text-3xl font-bold text-purple-100">
                {t('useCase2Title')}
              </h3>
              <p className="text-purple-200 leading-relaxed">
                {t('useCase2Description')}
              </p>
              <button className="group px-6 py-3 bg-gradient-to-r from-pink-500 via-fuchsia-500 to-purple-500 text-white font-semibold rounded-full transition-all duration-300 hover:scale-110 hover:shadow-2xl hover:shadow-pink-500/40 relative overflow-hidden">
                <Link href={link}>{t('animateIllustration')}</Link>
              </button>
            </div>
            <div className="group relative lg:order-2">
              <AnimatedCard>
                <div className=" rounded-3xl p-2  transition-all duration-500">
                  <div className="aspect-video overflow-hidden bg-gradient-to-br from-purple-800/50 to-pink-800/50 rounded-2xl flex items-center justify-center">
                    <div className="flex-1 h-full flex justify-center items-center">
                      <img
                        className="w-full h-full object-cover opacity-90"
                        src="/images/ai-image-to-video/product-static.webp"
                        alt="Animated illustrations of a fantasy landscape brought to life with our photo animation maker."
                      />
                    </div>
                    <div className="flex-1 h-full flex justify-center items-center">
                      <video
                        muted
                        autoPlay
                        preload="metadata"
                        loop
                        playsInline
                        className="w-full h-full object-cover opacity-90"
                        src="/videos/ai-image-to-video/product-animate.mp4"
                      ></video>
                    </div>
                  </div>
                </div>
              </AnimatedCard>
            </div>
          </div>

          {/* Use Case 3 */}
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="group relative">
              <AnimatedCard>
                <div className=" rounded-3xl p-2  transition-all duration-500">
                  <div className="aspect-video overflow-hidden bg-gradient-to-br from-purple-800/50 to-pink-800/50 rounded-2xl flex items-center justify-center">
                    <div className="flex-1 h-full flex justify-center items-center">
                      <img
                        className="w-full blur-[0.2px] h-full object-cover opacity-90"
                        src="/images/ai-image-to-video/cartoon-butterfly.webp"
                        alt="A cute cartoon butterfly animation made using our free motion machine."
                      />
                    </div>
                    <div className="flex-1 h-full flex justify-center items-center">
                      <video
                        muted
                        autoPlay
                        preload="metadata"
                        loop
                        playsInline
                        className="w-full h-full object-cover opacity-90"
                        src="/videos/ai-image-to-video/cartoon-butterfly.mp4"
                      ></video>
                    </div>
                  </div>
                </div>
              </AnimatedCard>
            </div>
            <div className="space-y-6">
              <h3 className="text-2xl md:text-3xl font-bold text-purple-100">
                {t('useCase3Title')}
              </h3>
              <p className="text-purple-200 leading-relaxed">
                {t('useCase3Description')}
              </p>
              <button className="group px-6 py-3 bg-gradient-to-r from-pink-500 via-fuchsia-500 to-purple-500 text-white font-semibold rounded-full transition-all duration-300 hover:scale-110 hover:shadow-2xl hover:shadow-pink-500/40 relative overflow-hidden">
                <Link href={link}>{t('createCartoonAnimation')}</Link>
              </button>
            </div>
          </div>

          {/* Use Case 4 */}
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6 lg:order-1">
              <h3 className="text-2xl md:text-3xl font-bold text-purple-100">
                {t('useCase4Title')}
              </h3>
              <p className="text-purple-200 leading-relaxed">
                {t('useCase4Description')}
              </p>
              <button className="group px-6 py-3 bg-gradient-to-r from-pink-500 via-fuchsia-500 to-purple-500 text-white font-semibold rounded-full transition-all duration-300 hover:scale-110 hover:shadow-2xl hover:shadow-pink-500/40 relative overflow-hidden">
                <Link href={link}>{t('boostSocialMedia')}</Link>
              </button>
            </div>
            <div className="group relative lg:order-2">
              <AnimatedCard>
                <div className=" rounded-3xl p-2  transition-all duration-500">
                  <div className="aspect-video overflow-hidden bg-gradient-to-br from-purple-800/50 to-pink-800/50 rounded-2xl flex items-center justify-center">
                    <div className="flex-1 h-full flex justify-center items-center">
                      <img
                        className="w-full h-full object-cover opacity-90"
                        src="/images/ai-image-to-video/ins-cofee.webp"
                        alt=""
                      />
                    </div>
                    <div className="flex-1 h-full flex justify-center items-center">
                      <video
                        muted
                        autoPlay
                        preload="metadata"
                        loop
                        playsInline
                        className="w-full h-full object-cover opacity-90"
                        src="/videos/ai-image-to-video/ins-cofee.mp4"
                      ></video>
                    </div>
                  </div>
                </div>
              </AnimatedCard>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
export default UseCaseSection
