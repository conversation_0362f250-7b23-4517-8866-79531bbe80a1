import { getTranslations } from 'next-intl/server'
import React from 'react'
import StepsCarouselClient from './StepsCarouselClient'
import { getHowToGuideData } from './data/howToGuideData'
import { Link } from '@i18n/routing'
//
export default async function StepsCarousel({ toolUrl }: { toolUrl: string }) {
  const t = await getTranslations('oldfilter')
  const howToGuideData = await getHowToGuideData()
  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center text-white mb-24">
          {t('howToTitle')}
        </h2>

        <StepsCarouselClient steps={howToGuideData} />

        <div className="mt-24 text-center">
          <div className="relative group inline-block w-full md:w-auto">
            <Link href={toolUrl}>
              <button className="relative p-px leading-6 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-medium rounded-full hover:opacity-90 transition-all hover:-translate-y-1 shadow-lg shadow-purple-500/20 w-full md:w-auto inline-block px-8 py-3 duration-300 ease-in-out hover:scale-105 active:scale-95">
                {t('howToCta')}
              </button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  )
}
