import React from 'react'
import { Star, Quote } from 'lucide-react'
import { getwhyUsData } from './data/whyUsData'
import { Link } from '@i18n/routing'
import { getTranslations } from 'next-intl/server'

const WhyUsSection = async ({ toolUrl }: { toolUrl: string }) => {
  const t = await getTranslations('photoToVideo')

  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center text-white mb-24">
          {t('whyUsTitle')}
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {getwhyUsData(t as any).map((item, index) => (
            <div
              key={index}
              className="bg-slate-900/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-800 transition-all duration-300 hover:border-purple-500 hover:shadow-2xl hover:shadow-purple-500/10 hover:-translate-y-2 flex flex-col"
              style={{
                animation: `fade-in-up 0.5s ${index * 0.2}s ease-out both`,
              }}
            >
              <Quote className="w-8 h-8 text-purple-400 mb-4" />

              {/* Image */}
              <div className="relative group mb-6">
                <div className="relative overflow-hidden rounded-xl">
                  <img
                    src={item.img}
                    alt={item.alt}
                    className="w-full aspect-video object-cover transform transition duration-500 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-black/20" />
                </div>
              </div>

              <div className="flex mb-4">
                {[...Array(item.stars)].map((_, i) => (
                  <Star
                    key={i}
                    className="w-5 h-5 text-yellow-400 fill-yellow-400"
                  />
                ))}
              </div>

              <h3 className="text-xl font-semibold text-white mb-4">
                {(item.title)}
              </h3>

              <p className="text-gray-300 mb-6 italic text-lg flex-grow leading-relaxed">
                {(item.desc)}
              </p>

              <div className="flex items-center justify-between mt-auto">
                <div className="flex items-center">
                  <img
                    src={item.avatar}
                    alt={item.author}
                    className="w-10 h-10 rounded-full mr-4 object-cover"
                  />
                  <div>
                    <p className="font-semibold text-white">{(item.author)}</p>
                  </div>
                </div>

                <Link
                  href={toolUrl}
                  className="px-6 py-2 bg-gradient-to-r from-purple-600 to-pink-500 text-white font-semibold rounded-lg shadow-lg hover:opacity-90 hover:scale-105 transition-all text-sm"
                >
                  {(item.btn)}
                </Link>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default WhyUsSection
