import { getTranslations } from 'next-intl/server';
import HeroS<PERSON><PERSON> from './components/HeroSection'
import AdvantagesSection from './components/AdvantagesSection'
import UseCasesSection from './components/UseCasesSection'
import HowToSection from './components/HowToSection'
import TestimonialsSection from './components/TestimonialsSection'
import FAQSection from './components/FAQSection'
//
export async function generateMetadata() {
  const t = await getTranslations('aiArtGeneratorFree')
  return {
    title: t('title'),
    description: t('description'),
    keywords: t('keywords'),
    openGraph: {
      title: t('openGraphTitle'),
      description: t('openGraphDescription'),
      type: 'website',
      url: 'https://imggen.org/tools/ai-art-generator-free',
      images: [
        {
          url: 'https://imggen.org/images/og-ai-art-generator-free.jpg',
          width: 1200,
          height: 630,
          alt: t('openGraphTitle'),
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: t('twitterTitle'),
      description: t('twitterDescription'),
      images: ['https://imggen.org/images/twitter-ai-art-generator-free.jpg'],
    },
  }
}

export default async function AIArtGeneratorFreePage() {
  // 统一配置跳转URL
  const AI_ART_GENERATOR_FREE_TOOL_URL = '/ai/art-generator-free'
  const t = await getTranslations('aiArtGeneratorFree')

  return (
    <div className="min-h-screen bg-gradient-to-r from-[#3b0764] via-[#10051A] to-[#3b0764]">
      {/* Font Awesome CDN */}
      <link
        rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
        crossOrigin="anonymous"
      />

      {/* Structured Data - Schema.org JSON-LD */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'SoftwareApplication',
            name: t('schemaName'),
            description: t('schemaDescription'),
            applicationCategory: 'DesignApplication',
            operatingSystem: 'Web',
            offers: {
              '@type': 'Offer',
              price: '0',
              priceCurrency: 'USD',
            },
            aggregateRating: {
              '@type': 'AggregateRating',
              ratingValue: '4.9',
              reviewCount: '1572',
            },
            url: 'https://imggen.org/tools/ai-art-generator-free',
            mainEntityOfPage: {
              '@type': 'WebPage',
              '@id': 'https://imggen.org/tools/ai-art-generator-free',
            },
          }),
        }}
      />

      <main className="relative">
        <HeroSection toolUrl={AI_ART_GENERATOR_FREE_TOOL_URL} />
        <AdvantagesSection toolUrl={AI_ART_GENERATOR_FREE_TOOL_URL} />
        <UseCasesSection toolUrl={AI_ART_GENERATOR_FREE_TOOL_URL} />
        <HowToSection toolUrl={AI_ART_GENERATOR_FREE_TOOL_URL} />
        <TestimonialsSection toolUrl={AI_ART_GENERATOR_FREE_TOOL_URL} />
        <FAQSection toolUrl={AI_ART_GENERATOR_FREE_TOOL_URL} />
      </main>
    </div>
  )
}
