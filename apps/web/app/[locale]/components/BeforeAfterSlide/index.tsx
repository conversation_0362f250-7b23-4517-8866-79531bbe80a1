'use client'
import { useState, useRef, useEffect } from 'react'
import { useTranslations } from 'next-intl'

interface BeforeAfterSliderProps {
  beforeImage: string
  afterImage: string
  className?: string
  beforeAlt?: string
  afterAlt?: string
  containerClassName?: string
  hideBlur?: boolean
}

export default function BeforeAfterSlider({
  beforeImage,
  afterImage,
  className = '',
  beforeAlt = 'before',
  afterAlt = 'after',
  containerClassName = '',
  hideBlur = false,
}: BeforeAfterSliderProps) {
  const t = useTranslations('aiImageBackgroundRemover')
  const [sliderPosition, setSliderPosition] = useState(50)
  const [isHovering, setIsHovering] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)

  const handleMove = (event: MouseEvent | TouchEvent) => {
    if (!isHovering || !containerRef.current) return

    const containerRect = containerRef.current.getBoundingClientRect()
    const x = 'touches' in event ? event.touches[0].clientX : event.clientX
    const position = ((x - containerRect.left) / containerRect.width) * 100

    setSliderPosition(Math.min(Math.max(position, 0), 100))
  }

  const handleMouseEnter = () => {
    setIsHovering(true)
  }

  const handleMouseLeave = () => {
    setIsHovering(false)
  }

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => handleMove(e)
    const handleTouchMove = (e: TouchEvent) => handleMove(e)

    if (isHovering) {
      window.addEventListener('mousemove', handleMouseMove)
      window.addEventListener('touchmove', handleTouchMove)
    }

    return () => {
      window.removeEventListener('mousemove', handleMouseMove)
      window.removeEventListener('touchmove', handleTouchMove)
    }
  }, [isHovering])

  return (
    <div
      ref={containerRef}
      className={`relative w-full cursor-pointer h-full border border-solid border-white/5 rounded-lg select-none ${className}`}
      style={{ touchAction: 'none' }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Container for both images */}
      <div
        className={`relative w-full overflow-hidden h-full rounded-lg ${containerClassName}`}
      >
        {/* After image (full width) with frosted glass effect background */}
        <div className="absolute inset-0 h-full">
          {/* Frosted glass background */}
          {!hideBlur && (
            <>
              <div className="absolute inset-0 bg-gradient-to-br from-gray-100/30 to-white/50 backdrop-blur-xl" />

              <div
                className="absolute inset-0 backdrop-blur-sm"
                style={{
                  backgroundImage: `linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
                              linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
                              linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
                              linear-gradient(-45deg, transparent 75%, #f0f0f0 75%)`,
                  backgroundSize: '20px 20px',
                  backgroundPosition: '0 0, 0 10px, 10px -10px, -10px 0px',
                  opacity: 0.1,
                }}
              />
            </>
          )}

          <img
            src={afterImage}
            alt={afterAlt}
            className="relative w-full h-full object-cover"
            draggable="false"
          />
        </div>

        {/* Before image (clipped) */}
        <div
          className="absolute inset-0"
          style={{
            clipPath: `inset(0 ${100 - sliderPosition}% 0 0)`,
          }}
        >
          <img
            src={beforeImage}
            alt={beforeAlt}
            className="w-full h-full object-cover"
            draggable="false"
          />
        </div>

        {/* Divider line */}
        <div
          className="absolute top-0 bottom-0 w-1 bg-white/50 backdrop-blur-sm cursor-ew-resize group"
          style={{
            left: `${sliderPosition}%`,
            transform: 'translateX(-50%)',
          }}
        >
          {/* Slider handle */}
          <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-10 h-10 bg-white rounded-full shadow-lg flex items-center justify-center cursor-ew-resize transition-transform duration-200 group-hover:scale-110">
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              className="text-gray-600"
            >
              <path
                d="M8 8L4 12M4 12L8 16M4 12H20M20 12L16 8M20 12L16 16"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
        </div>

        {/* Labels */}
        <div className="absolute bottom-4 left-4 px-3 py-1.5 bg-white/20 backdrop-blur text-black/30 text-sm font-medium rounded-full shadow-sm">
          {t('beforeLabel')}
        </div>
        <div className="absolute bottom-4 right-4 px-3 py-1.5 bg-white/20 backdrop-blur text-black/30 text-sm font-medium rounded-full shadow-sm">
          {t('afterLabel')}
        </div>
      </div>
    </div>
  )
}
