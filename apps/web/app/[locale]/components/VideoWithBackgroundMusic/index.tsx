'use client'

import React, { useRef, useEffect, forwardRef, useImperativeHandle } from 'react'

export interface VideoWithBackgroundMusicProps {
  /** 视频URL */
  videoUrl: string
  /** 背景音乐URL */
  audioUrl: string
  /** 视频是否自动播放 */
  autoPlay?: boolean
  /** 视频是否循环播放 */
  loop?: boolean
  /** 视频是否显示控制条 */
  controls?: boolean
  /** 视频是否静音（原始音频） */
  muted?: boolean
  /** 背景音乐音量 (0-1) */
  audioVolume?: number
  /** 视频音量 (0-1) */
  videoVolume?: number
  /** 背景音乐是否循环播放 */
  audioLoop?: boolean
  /** 自定义className */
  className?: string
  /** 自定义样式 */
  style?: React.CSSProperties
  /** 视频加载完成回调 */
  onVideoLoad?: () => void
  /** 音频加载完成回调 */
  onAudioLoad?: () => void
  /** 播放状态改变回调 */
  onPlayStateChange?: (isPlaying: boolean) => void
  /** 错误回调 */
  onError?: (error: string) => void
}

export interface VideoWithBackgroundMusicRef {
  /** 播放视频和音频 */
  play: () => Promise<void>
  /** 暂停视频和音频 */
  pause: () => void
  /** 设置播放时间 */
  setCurrentTime: (time: number) => void
  /** 获取当前播放时间 */
  getCurrentTime: () => number
  /** 获取视频总时长 */
  getDuration: () => number
  /** 设置音频音量 */
  setAudioVolume: (volume: number) => void
  /** 设置视频音量 */
  setVideoVolume: (volume: number) => void
  /** 获取视频元素 */
  getVideoElement: () => HTMLVideoElement | null
  /** 获取音频元素 */
  getAudioElement: () => HTMLAudioElement | null
}

export const VideoWithBackgroundMusic = forwardRef<
  VideoWithBackgroundMusicRef,
  VideoWithBackgroundMusicProps
>(({
  videoUrl,
  audioUrl,
  autoPlay = false,
  loop = false,
  controls = true,
  muted = true, // 默认静音原始视频音频
  audioVolume = 0.5,
  videoVolume = 0,
  audioLoop = true, // 默认背景音乐循环播放
  className,
  style,
  onVideoLoad,
  onAudioLoad,
  onPlayStateChange,
  onError,
}, ref) => {
  const videoRef = useRef<HTMLVideoElement>(null)
  const audioRef = useRef<HTMLAudioElement>(null)
  const isPlayingRef = useRef(false)

  // 同步播放状态
  const syncPlayback = async (shouldPlay: boolean) => {
    const video = videoRef.current
    const audio = audioRef.current
    
    if (!video || !audio) return

    try {
      if (shouldPlay) {
        // 同步播放
        await Promise.all([
          video.play(),
          audio.play()
        ])
        isPlayingRef.current = true
        onPlayStateChange?.(true)
      } else {
        // 同步暂停
        video.pause()
        audio.pause()
        isPlayingRef.current = false
        onPlayStateChange?.(false)
      }
    } catch (error) {
      onError?.(`播放失败: ${error}`)
    }
  }

  // 同步播放时间
  const syncTime = () => {
    const video = videoRef.current
    const audio = audioRef.current
    
    if (!video || !audio) return

    const videoTime = video.currentTime
    const audioDuration = audio.duration
    
    if (audioDuration && audioLoop) {
      // 如果音频循环播放，计算对应的音频时间
      audio.currentTime = videoTime % audioDuration
    } else {
      // 如果音频不循环，直接同步时间
      audio.currentTime = Math.min(videoTime, audioDuration || 0)
    }
  }

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    play: async () => {
      await syncPlayback(true)
    },
    pause: () => {
      syncPlayback(false)
    },
    setCurrentTime: (time: number) => {
      const video = videoRef.current
      if (video) {
        video.currentTime = time
        syncTime()
      }
    },
    getCurrentTime: () => {
      return videoRef.current?.currentTime || 0
    },
    getDuration: () => {
      return videoRef.current?.duration || 0
    },
    setAudioVolume: (volume: number) => {
      const audio = audioRef.current
      if (audio) {
        audio.volume = Math.max(0, Math.min(1, volume))
      }
    },
    setVideoVolume: (volume: number) => {
      const video = videoRef.current
      if (video) {
        video.volume = Math.max(0, Math.min(1, volume))
      }
    },
    getVideoElement: () => videoRef.current,
    getAudioElement: () => audioRef.current,
  }))

  // 初始化设置
  useEffect(() => {
    const video = videoRef.current
    const audio = audioRef.current

    if (!video || !audio) return

    // 设置初始音量
    video.volume = videoVolume
    audio.volume = audioVolume
    
    // 设置循环播放
    video.loop = loop
    audio.loop = audioLoop

    // 设置静音
    video.muted = muted

    // 视频事件监听
    const handleVideoLoad = () => {
      onVideoLoad?.()
    }

    const handleVideoPlay = () => {
      if (!isPlayingRef.current) {
        audio.play().catch(err => onError?.(`音频播放失败: ${err}`))
        isPlayingRef.current = true
        onPlayStateChange?.(true)
      }
    }

    const handleVideoPause = () => {
      if (isPlayingRef.current) {
        audio.pause()
        isPlayingRef.current = false
        onPlayStateChange?.(false)
      }
    }

    const handleVideoTimeUpdate = () => {
      syncTime()
    }

    const handleVideoError = () => {
      onError?.('视频加载失败')
    }

    // 音频事件监听
    const handleAudioLoad = () => {
      onAudioLoad?.()
    }

    const handleAudioError = () => {
      onError?.('音频加载失败')
    }

    // 添加事件监听器
    video.addEventListener('loadeddata', handleVideoLoad)
    video.addEventListener('play', handleVideoPlay)
    video.addEventListener('pause', handleVideoPause)
    video.addEventListener('timeupdate', handleVideoTimeUpdate)
    video.addEventListener('error', handleVideoError)
    
    audio.addEventListener('loadeddata', handleAudioLoad)
    audio.addEventListener('error', handleAudioError)

    // 自动播放
    if (autoPlay) {
      syncPlayback(true)
    }

    // 清理函数
    return () => {
      video.removeEventListener('loadeddata', handleVideoLoad)
      video.removeEventListener('play', handleVideoPlay)
      video.removeEventListener('pause', handleVideoPause)
      video.removeEventListener('timeupdate', handleVideoTimeUpdate)
      video.removeEventListener('error', handleVideoError)
      
      audio.removeEventListener('loadeddata', handleAudioLoad)
      audio.removeEventListener('error', handleAudioError)
    }
  }, [videoUrl, audioUrl, autoPlay, loop, muted, audioVolume, videoVolume, audioLoop])

  return (
    <div className={className} style={style}>
      <video
        ref={videoRef}
        src={videoUrl}
        controls={controls}
        style={{ width: '100%', height: '100%' }}
        preload="metadata"
      />
      <audio
        ref={audioRef}
        src={audioUrl}
        preload="metadata"
        style={{ display: 'none' }}
      />
    </div>
  )
})

VideoWithBackgroundMusic.displayName = 'VideoWithBackgroundMusic'

export default VideoWithBackgroundMusic
