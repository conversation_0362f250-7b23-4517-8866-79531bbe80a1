import toast from 'react-hot-toast'

// 下载图片函数
export const downloadImage = async (imageUrl: string, fileName: string) => {
  // 显示下载开始的提示
  const toastId = toast.loading('Downloading image...', {
    duration: 0, // 不自动消失
  })

  try {
    const response = await fetch(imageUrl)
    if (!response.ok) {
      throw new Error('Failed to fetch image')
    }

    const blob = await response.blob()
    const url = window.URL.createObjectURL(blob)

    // 创建下载链接
    const link = document.createElement('a')
    link.href = url
    link.download = `${fileName
      .replace(/[^a-z0-9\s]/gi, '_')
      .replace(/\s+/g, '_')
      .toLowerCase()}.jpg`

    // 触发下载
    document.body.appendChild(link)
    link.click()

    // 清理
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    // 显示下载成功提示
    toast.success('Image downloaded successfully!', {
      id: toastId,
    })
  } catch (error) {
    // 显示下载失败提示，并尝试在新窗口打开
    toast.error('Download failed. Opening image in new tab...', {
      id: toastId,
    })
    // 如果直接下载失败，尝试在新窗口打开
    window.open(imageUrl, '_blank')
  }
}
