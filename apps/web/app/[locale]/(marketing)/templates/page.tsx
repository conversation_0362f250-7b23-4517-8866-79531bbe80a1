import { getTranslations } from 'next-intl/server'
import TemplatesClientWrapper from './components/TemplatesClientWrapper'

export async function generateMetadata() {
  const t = await getTranslations()
  return {
    title: t('templates.pageTitle'),
    description: t('templates.pageDescription'),
    keywords: t('templates.pageKeywords'),
  }
}

const TemplatesPage = async () => {
  return (
    <div className="w-full min-h-screen pt-16 text-white">
      <TemplatesClientWrapper />
    </div>
  )
}

export default TemplatesPage
