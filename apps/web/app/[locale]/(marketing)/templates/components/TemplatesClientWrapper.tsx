'use client'

import { useState } from 'react'
import TemplatesHero from './TemplatesHero'
import TemplatesContent from './TemplatesContent'

const TemplatesClientWrapper = () => {
  const [searchQuery, setSearchQuery] = useState('')

  return (
    <>
      <TemplatesHero searchQuery={searchQuery} onSearchChange={setSearchQuery} />
      <TemplatesContent searchQuery={searchQuery} />
    </>
  )
}

export default TemplatesClientWrapper
