'use client'

import { useTranslations } from 'next-intl'
import { useMemo } from 'react'
import CategorySection from './CategorySection'
import { getMenuCategories } from '@shared/components/Logo'

interface TemplatesContentProps {
  searchQuery: string
}

const TemplatesContent = ({ searchQuery }: TemplatesContentProps) => {
  const t = useTranslations()

  // 获取菜单数据，isAiPage传false
  const menuCategories = getMenuCategories(false, t)

  // 需要展示的模块
  const targetCategories = [
    'business',
    'creative',
    'memory',
    'imageTools',
    'videoCreation',
    'utilities',
    'fun',
  ]

  // 模糊匹配搜索函数
  const fuzzyMatch = (text: string, query: string): boolean => {
    if (!query.trim()) return true

    const normalizedText = text.toLowerCase()
    const normalizedQuery = query.toLowerCase().trim()

    // 直接包含匹配
    if (normalizedText.includes(normalizedQuery)) return true

    // 分词匹配
    const queryWords = normalizedQuery.split(/\s+/)
    return queryWords.every((word) => normalizedText.includes(word))
  }

  // 过滤和搜索逻辑
  const filteredCategories = useMemo(() => {
    if (!searchQuery.trim()) {
      return targetCategories
    }

    const matchedCategories: string[] = []

    targetCategories.forEach((categoryKey) => {
      const category =
        menuCategories[categoryKey as keyof typeof menuCategories]
      if (!category) return

      // 检查分类名称是否匹配
      const categoryNameMatch = fuzzyMatch(category.label, searchQuery)

      // 检查分类下的工具是否匹配
      const hasMatchingTools = category.items.some(
        (item: any) =>
          fuzzyMatch(item.title || '', searchQuery) ||
          fuzzyMatch(item.desc || '', searchQuery)
      )

      if (categoryNameMatch || hasMatchingTools) {
        matchedCategories.push(categoryKey)
      }
    })

    return matchedCategories
  }, [searchQuery, menuCategories, targetCategories])
  return (
    <div className="relative w-full pt-9 mx-auto px-4 md:px-8 pb-16 bg-gradient-to-b from-slate-800 via-gray-800 to-slate-800 min-h-screen">
      {/* 添加微妙的装饰背景 */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-900/5 via-transparent to-blue-900/5"></div>
      <div className="absolute top-20 left-10 w-72 h-72 bg-purple-600/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-20 right-10 w-72 h-72 bg-blue-600/5 rounded-full blur-3xl"></div>

      <div className="relative z-10">
        {searchQuery.trim() && filteredCategories.length === 0 ? (
          <div className="text-center py-16">
            <div className="text-gray-400 text-lg mb-2">
              {t('templates.noResultsFound')}
            </div>
            <div className="text-gray-500 text-sm">
              {t('templates.tryDifferentKeywords')}
            </div>
          </div>
        ) : (
          filteredCategories.map((categoryKey) => {
            const category =
              menuCategories[categoryKey as keyof typeof menuCategories]
            if (!category) return null
            const isNotEmpty = !!category.items.filter(
              (item: any) => !item?.coming
            ).length
            // 如果有搜索查询，过滤分类中的工具项
            let filteredCategory = category
            if (searchQuery.trim()) {
              const filteredItems = category.items.filter(
                (item: any) =>
                  fuzzyMatch(item.title || '', searchQuery) ||
                  fuzzyMatch(item.desc || '', searchQuery)
              )

              // 如果没有匹配的工具项，跳过这个分类
              if (filteredItems.length === 0) return null

              filteredCategory = {
                ...category,
                items: filteredItems,
              }
            }

            return (
              isNotEmpty && (
                <CategorySection
                  key={categoryKey}
                  categoryKey={categoryKey}
                  category={filteredCategory}
                />
              )
            )
          })
        )}
      </div>
    </div>
  )
}

export default TemplatesContent
