'use client'

import TemplateCard from './TemplateCard'

interface CategorySectionProps {
  categoryKey?: string
  category: {
    label?: string
    items: Array<{
      title?: string
      desc?: string
      seoHref?: string
      mediaUrl?: string
      hidden?: boolean
      isHot?: boolean
      isNew?: boolean
      coming?: boolean
      point?: number
    }>
  }
}

const CategorySection = ({ categoryKey, category }: CategorySectionProps) => {
  return (
    <section className="mb-12 sm:mb-16">
      <div className="mb-6 sm:mb-8 px-2">
        <h2 className="text-2xl sm:text-3xl font-bold text-white mb-2">
          {category.label}
        </h2>
        <div className="w-16 sm:w-20 h-1 bg-gradient-to-r from-purple-500 to-fuchsia-500 rounded-full"></div>
      </div>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 sm:gap-6">
        {category.items
          .filter((item) => !item.hidden)
          .map((item, index) => (
            !item.coming && <TemplateCard key={`${categoryKey}-${index}`} item={item} />
          ))}
      </div>
    </section>
  )
}

export default CategorySection
