'use client'
//

import { useTranslations } from 'next-intl'

interface TemplatesHeroProps {
  searchQuery: string
  onSearchChange: (query: string) => void
}

const TemplatesHero = ({ searchQuery, onSearchChange }: TemplatesHeroProps) => {
  const t = useTranslations()

  return (
    <div className="relative pt-24 pb-20 bg-gradient-to-b from-purple-600/90 via-slate-700 to-slate-800">
      {/* 添加装饰性背景元素 */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-600/10 via-transparent to-blue-600/10"></div>
      <div className="absolute top-0 left-1/4 w-96 h-96 bg-purple-500/8 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-blue-500/8 rounded-full blur-3xl"></div>

      <div className="relative w-full max-w-[1300px] mx-auto px-4 md:px-8 z-10">
        <div className="text-center">
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-white via-purple-200 to-blue-200 bg-clip-text text-transparent drop-shadow-[0_2px_16px_rgba(99,102,241,0.5)] mb-6 px-2">
            {t('templates.title')}
          </h1>

          {/* 搜索框 */}
          <div className="max-w-2xl mx-auto mb-8 px-4">
            <div className="relative">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => onSearchChange(e.target.value)}
                placeholder={t('templates.searchPlaceholder')}
                className="w-full px-4 sm:px-6 py-3 sm:py-4 bg-white/10 backdrop-blur-md border border-white/20 rounded-full text-white placeholder-gray-300 focus:outline-none focus:border-purple-400/70 focus:ring-2 focus:ring-purple-400/30 text-sm sm:text-base shadow-lg"
              />
              <div className="absolute right-3 sm:right-4 top-1/2 transform -translate-y-1/2">
                <svg
                  className="w-4 h-4 sm:w-5 sm:h-5 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default TemplatesHero
