'use client'

import { useState } from 'react'
import { useTranslations } from 'next-intl'
import { Link } from '@i18n/routing'
import MediaDisplay from './MediaDisplay'

export interface TemplateCardProps {
  item: {
    title: string
    desc: string
    seoHref: string
    mediaUrl: string
    isHot?: boolean
    isNew?: boolean
    coming?: boolean
    point?: number
    bgRemove?: boolean
    beforeImage?: string
    afterImage?: string
  }
}

const TemplateCard = ({ item }: TemplateCardProps) => {
  const [isHovered, setIsHovered] = useState(false)
  const t = useTranslations()

  return (
    <Link
      href={item.seoHref}
      className="group block bg-gray-800/40 rounded-xl p-3 sm:p-4 hover:bg-gray-700/50 transition-all duration-300 border border-gray-700/50 hover:border-purple-500/30 hover:shadow-lg hover:shadow-purple-500/10"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* 媒体展示 */}
      <div className="mb-3 sm:mb-4 overflow-hidden rounded-lg">
        <MediaDisplay
          item={item}
          mediaUrl={item.mediaUrl}
          title={item.title}
          isHovered={isHovered}
        />
      </div>

      {/* 内容 */}
      <div className="space-y-2">
        <div className="flex items-start justify-between gap-2">
          <h3 className="font-semibold text-white group-hover:text-purple-200 transition-colors text-sm sm:text-base line-clamp-2 flex-1">
            {item.title}
          </h3>
          <div className="flex items-center gap-1 flex-shrink-0">
            {item.isHot && (
              <span className="inline-flex items-center justify-center bg-gradient-to-r from-orange-500 to-red-500 text-white px-1.5 sm:px-2 py-0.5 text-xs font-bold rounded">
                {t('templates.hot')}
              </span>
            )}
            {item.isNew && (
              <span className="inline-flex items-center justify-center bg-gradient-to-r from-green-500 to-emerald-500 text-white px-1.5 sm:px-2 py-0.5 text-xs font-bold rounded">
                {t('templates.new')}
              </span>
            )}
            {item.coming && (
              <span className="inline-flex items-center justify-center bg-orange-800/60 text-orange-300 px-1.5 sm:px-2 py-0.5 text-xs font-medium rounded border border-orange-500/20">
                {t('templates.coming')}
              </span>
            )}
          </div>
        </div>
        <p className="text-gray-400 text-xs sm:text-sm line-clamp-2">
          {item.desc}
        </p>
        {item.point && (
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-500">
              {/* {item.point} {t('templates.credits')} */}
            </span>
          </div>
        )}
      </div>
    </Link>
  )
}

export default TemplateCard
