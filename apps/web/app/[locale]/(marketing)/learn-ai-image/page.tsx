import { PostListItem } from '@marketing/blog/components/PostListItem'
import { allLearnAiImagePosts } from 'content-collections'
import { getLocale } from 'next-intl/server'

export async function generateMetadata() {
  return {
    title: 'Learn AI Image Generation',
    description:
      'Master AI image generation with our comprehensive tutorials and guides.',
  }
}

export default async function LearnAiImageListPage() {
  const locale = await getLocale()

  return (
    <div className="container max-w-6xl pt-32 pb-16">
      <div className="mb-12 text-center">
        <h1 className="font-bold text-4xl md:text-5xl leading-tight tracking-tight mb-4">
          Learn AI Image Generation
        </h1>
        <p className="text-xl text-muted-foreground">
          Master the art of AI-powered image creation
        </p>
      </div>

      <div className="grid gap-8 md:grid-cols-2">
        {allLearnAiImagePosts
          .filter((post) => post.published && locale === post.locale)
          .sort(
            (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
          )
          .map((post) => (
            <PostListItem
              post={post}
              key={post.path}
              basePath="/learn-ai-image"
            />
          ))}
      </div>
    </div>
  )
}
