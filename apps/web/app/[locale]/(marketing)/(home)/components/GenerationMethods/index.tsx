'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Wand2, Upload } from 'lucide-react'
import Image from 'next/image'
import { useTranslations } from 'next-intl'

type Tab = {
  title: string
  description: string
  useCases: string[]
  icon: React.ReactNode
  image: string
  button: string
}

type Tabs = {
  text: Tab
  image: Tab
}

type TabType = 'text' | 'image'

interface PreviewProps {
  activeTab: TabType
  tabs: Tabs
}

interface ContentProps {
  activeTab: TabType
  tabs: Tabs
}

const ImagePreview: React.FC<PreviewProps> = ({ activeTab, tabs }) => (
  <div className="relative">
    <div className="aspect-square w-full relative rounded-xl overflow-hidden">
      <Image
        src={tabs[activeTab].image}
        alt={
          activeTab === 'text'
            ? 'Text to Image example'
            : 'Image to Image example'
        }
        fill
        className="object-cover"
        sizes="(max-width: 768px) 100vw, 50vw"
        priority
      />
    </div>
  </div>
)

const Content: React.FC<ContentProps> = ({ activeTab, tabs }) => {
  const t = useTranslations('GenerationMethods')
  return (
    <div className="space-y-6 max-w-xl">
      <h3 className="text-2xl font-semibold text-gray-900">
        {tabs[activeTab].title}
      </h3>
      <p className="text-gray-600 leading-relaxed">
        {tabs[activeTab].description}
      </p>
      <div>
        <h4 className="font-medium text-gray-900 mb-3">{t('useCases')}</h4>
        <ul className="space-y-2">
          {tabs[activeTab].useCases.map((useCase, index) => (
            <li key={index} className="flex items-center gap-2 text-gray-600">
              <span className="w-1.5 h-1.5 rounded-full bg-blue-500" />
              {useCase}
            </li>
          ))}
        </ul>
      </div>
    </div>
  )
}

const GenerationMethods = () => {
  const t = useTranslations('GenerationMethods')
  const [activeTab, setActiveTab] = useState<TabType>('text')

  const tabs: Tabs = {
    text: {
      title: t('text.title'),
      description: t('text.description'),
      useCases: [
        t('text.useCases.0'),
        t('text.useCases.1'),
        t('text.useCases.2'),
        t('text.useCases.3'),
      ],
      image: '/images/text-to-image.png',
      icon: <Wand2 className="w-6 h-6" />,
      button: t('text.button'),
    },
    image: {
      title: t('image.title'),
      description: t('image.description'),
      useCases: [
        t('image.useCases.0'),
        t('image.useCases.1'),
        t('image.useCases.2'),
        t('image.useCases.3'),
      ],
      image: '/images/img-to-img.png',
      icon: <Upload className="w-6 h-6" />,
      button: t('image.button'),
    },
  }

  return (
    <div className="w-full max-w-6xl mx-auto px-4 py-12">
      <div className="text-center mb-8">
        <h2 className="py-11 text-3xl md:text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-blue-500">
          {t('title')}
        </h2>
        <p className="text-gray-600 text-lg max-w-2xl mx-auto">
          {t('description')}
        </p>
      </div>

      <div className="flex justify-center mb-8">
        <div className="bg-gray-100/80 p-1.5 rounded-xl flex w-full max-w-md gap-1.5">
          {Object.entries(tabs).map(([key, { icon }]) => (
            <button
              key={key}
              onClick={() => setActiveTab(key as TabType)}
              className="relative flex-1 min-w-[140px] group"
            >
              <div
                className={`absolute inset-0 rounded-lg transition-all duration-300 ${
                  activeTab === key ? 'shadow-lg shadow-purple-500/20' : ''
                }`}
              >
                {activeTab === key ? (
                  <motion.div
                    layoutId="activeTab"
                    className="absolute inset-0 bg-gradient-to-r from-indigo-500 via-purple-500 to-blue-500 rounded-lg"
                    initial={false}
                    transition={{ type: 'spring', duration: 0.5 }}
                  />
                ) : (
                  <div className="absolute inset-0 bg-gradient-to-br from-white/80 to-gray-50/80 backdrop-blur rounded-lg transition-all duration-300 border border-white/50 group-hover:border-transparent group-hover:bg-gradient-to-r group-hover:from-indigo-400/20 group-hover:via-purple-400/20 group-hover:to-blue-400/20" />
                )}
              </div>
              <div
                className={`relative px-2 sm:px-3 md:px-6 py-2.5 flex items-center justify-center gap-1 sm:gap-1.5 md:gap-2 text-xs sm:text-sm md:text-base font-medium transition-all duration-300 ${
                  activeTab === key ? 'text-white' : 'text-gray-700'
                }`}
              >
                {activeTab === key ? (
                  <div className="text-white/90 shrink-0">{icon}</div>
                ) : (
                  <div className="text-indigo-500 shrink-0">{icon}</div>
                )}
                <span className="whitespace-nowrap">
                  {tabs[key as keyof Tabs].button}
                </span>
              </div>
            </button>
          ))}
        </div>
      </div>

      <AnimatePresence mode="wait">
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
          className="rounded-2xl"
        >
          <div className="grid md:grid-cols-2 gap-12 items-center">
            {activeTab === 'text' ? (
              <>
                <ImagePreview activeTab={activeTab} tabs={tabs} />
                <Content activeTab={activeTab} tabs={tabs} />
              </>
            ) : (
              <>
                <Content activeTab={activeTab} tabs={tabs} />
                <ImagePreview activeTab={activeTab} tabs={tabs} />
              </>
            )}
          </div>
        </motion.div>
      </AnimatePresence>

      <div className="text-center mt-8 text-gray-600">{t('footer')}</div>
    </div>
  )
}

export default GenerationMethods
