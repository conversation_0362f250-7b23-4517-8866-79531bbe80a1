import { useTranslations } from 'next-intl'

// 图标样式数组
const iconStyles = [
  {
    backgroundPosition: '0 0',
  },
  {
    backgroundPosition: '-96px 0',
  },
  {
    backgroundPosition: '-192px 0',
  },
  {
    backgroundPosition: '0 -96px',
  },
  {
    backgroundPosition: '-96px -96px',
  },
  {
    backgroundPosition: '-192px -96px',
  },
]

const FeatureItem = ({
  iconStyle,
  title,
  description,
}: {
  iconStyle: { backgroundPosition: string }
  title: string
  description: string
}) => (
  <div className="flex flex-col items-center text-center p-4 md:p-6">
    <div
      className="p-2 mb-4 w-24 h-24"
      style={{
        backgroundImage: 'url(/features.png)',
        backgroundSize: '288px 192px',
        backgroundRepeat: 'no-repeat',
        ...iconStyle,
      }}
    />
    <h3 className="text-lg md:text-xl font-semibold mb-2 text-gray-800">
      {title}
    </h3>
    <p className="text-sm md:text-base text-gray-600">{description}</p>
  </div>
)

const FeatureGrid = () => {
  const t = useTranslations('FeatureGrid')

  const contents = [
    {
      title: t('features.0.title'),
      description: t('features.0.description'),
    },
    {
      title: t('features.1.title'),
      description: t('features.1.description'),
    },
    {
      title: t('features.2.title'),
      description: t('features.2.description'),
    },
    {
      title: t('features.3.title'),
      description: t('features.3.description'),
    },
    {
      title: t('features.4.title'),
      description: t('features.4.description'),
    },
    {
      title: t('features.5.title'),
      description: t('features.5.description'),
    },
  ]
  const features = contents.map((content, index) => ({
    iconStyle: iconStyles[index],
    title: content.title,
    description: content.description,
  }))

  return (
    <section className="py-20 px-4">
      <div className="container mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-2xl md:text-4xl font-bold text-gray-900 mb-4">
            {t('title')}
          </h2>
          <p className="text-gray-600 max-w-3xl mx-auto">{t('description')}</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <FeatureItem
              key={index}
              iconStyle={feature.iconStyle}
              title={feature.title}
              description={feature.description}
            />
          ))}
        </div>
      </div>
    </section>
  )
}

export default FeatureGrid
