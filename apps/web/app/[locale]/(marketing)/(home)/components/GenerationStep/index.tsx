import React from 'react'
import { Link } from '@i18n/routing'
//
import { getTranslations } from 'next-intl/server'

export default async function GenerationStep() {
  const t = await getTranslations('GenerationStep')

  return (
    <div className="w-full max-w-7xl mx-auto px-4 py-4">
      <div className="text-center mb-6">
        <h2 className="mb-2 text-3xl md:text-4xl py-1 font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-indigo-500 to-purple-600">
          {t('title')}
        </h2>
        <p className="text-gray-600 max-w-4xl mx-auto text-base md:text-lg leading-relaxed">
          {t('description')}
        </p>
        <Link href="/ai-generate-image" target="_blank">
          <button className="group relative mt-4 px-8 py-3 rounded-full bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white font-medium text-base overflow-hidden transition-all duration-300 hover:scale-105 hover:shadow-2xl hover:shadow-purple-500/25">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-700 via-purple-700 to-indigo-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div className="absolute inset-0 bg-white/20 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>
            <span className="relative z-10">{t('createButton')}</span>
          </button>
        </Link>
      </div>

      <div className="grid md:grid-cols-2 gap-6 items-stretch">
        <div className="space-y-3 flex flex-col justify-between">
          <div className="bg-white/80 rounded-xl p-3 backdrop-blur border border-gray-200 hover:border-blue-500/50 transition-colors shadow-sm">
            <div className="flex items-center gap-3 mb-2">
              <div className="flex-shrink-0 w-6 h-6 rounded-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white flex items-center justify-center font-bold text-xs">
                1
              </div>
              <h3 className="text-base font-semibold text-blue-600">
                {t('step1')}
              </h3>
            </div>
            <p className="text-gray-700 mb-2 text-sm leading-relaxed">
              {t('step1Desc')}
            </p>
            <ul className="text-gray-600 space-y-0.5 list-disc pl-4 text-sm">
              <li>{t('step1List.0')}</li>
              <li>{t('step1List.1')}</li>
            </ul>
          </div>

          <div className="bg-white/80 rounded-xl p-3 backdrop-blur border border-gray-200 hover:border-blue-500/50 transition-colors shadow-sm">
            <div className="flex items-center gap-3 mb-2">
              <div className="flex-shrink-0 w-6 h-6 rounded-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white flex items-center justify-center font-bold text-xs">
                2
              </div>
              <h3 className="text-base font-semibold text-blue-600">
                {t('step2')}
              </h3>
            </div>
            <p className="text-gray-700 mb-1 text-sm leading-relaxed">
              {t('step2Desc1')}
            </p>
            <p className="text-gray-700 text-sm leading-relaxed">
              {t('step2Desc2')}
            </p>
          </div>

          <div className="bg-white/80 rounded-xl p-3 backdrop-blur border border-gray-200 hover:border-blue-500/50 transition-colors shadow-sm">
            <div className="flex items-center gap-3 mb-2">
              <div className="flex-shrink-0 w-6 h-6 rounded-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white flex items-center justify-center font-bold text-xs">
                3
              </div>
              <h3 className="text-base font-semibold text-blue-600">
                {t('step3')}
              </h3>
            </div>
            <p className="text-gray-700 mb-2 text-sm leading-relaxed">
              {t('step3Desc')}
            </p>
            <ul className="text-gray-600 space-y-0.5">
              <li className="flex items-center gap-2">
                <span className="w-1.5 h-1.5 rounded-full bg-gradient-to-r from-blue-600 to-indigo-600"></span>
                <span className="text-sm">{t('step3List.0')}</span>
              </li>
              <li className="flex items-center gap-2">
                <span className="w-1.5 h-1.5 rounded-full bg-gradient-to-r from-blue-600 to-indigo-600"></span>
                <span className="text-sm">{t('step3List.1')}</span>
              </li>
              <li className="flex items-center gap-2">
                <span className="w-1.5 h-1.5 rounded-full bg-gradient-to-r from-blue-600 to-indigo-600"></span>
                <span className="text-sm">{t('step3List.2')}</span>
              </li>
            </ul>
          </div>

          <div className="bg-white/80 rounded-xl p-3 backdrop-blur border border-gray-200 hover:border-blue-500/50 transition-colors shadow-sm">
            <div className="flex items-center gap-3 mb-2">
              <div className="flex-shrink-0 w-6 h-6 rounded-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white flex items-center justify-center font-bold text-xs">
                4
              </div>
              <h3 className="text-base font-semibold text-blue-600">
                {t('step4')}
              </h3>
            </div>
            <p className="text-gray-700 text-sm leading-relaxed">
              {t('step4Desc')}
            </p>
          </div>
        </div>

        <div className="hidden md:block">
          <div className="w-full h-full rounded-2xl overflow-hidden">
            <div
              className="w-full h-full bg-center bg-cover"
              style={{
                backgroundImage: 'url("/images/step.png")',
              }}
            ></div>
          </div>
        </div>
      </div>
    </div>
  )
}
