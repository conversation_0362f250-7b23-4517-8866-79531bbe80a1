'use client'
import React, { useState } from 'react'
import { AnimatePresence, motion } from 'framer-motion'
import Image from 'next/image'

interface Card {
  key: string
  label: string
  img: string
  alt: string
  title: string
  desc: string
}

export default function ForEveryoneClient({
  title,
  subtitle,
  cards,
}: {
  title: string
  subtitle: string
  cards: Card[]
}) {
  const [active, setActive] = useState(0)
  const [imgLoaded, setImgLoaded] = useState(false)
  // 监听active变化时重置加载状态
  React.useEffect(() => {
    setImgLoaded(false)
  }, [active])
  return (
    <section className="w-full bg-[#141417] py-16">
      <div className="container mx-auto px-4 flex flex-col md:flex-row gap-10 items-center md:items-start">
        {/* 左侧文字列表 */}
        <div className="w-full md:w-1/2 flex flex-col gap-6">
          <h2 className="text-3xl md:text-4xl font-bold text-indigo-400 drop-shadow-[0_2px_16px_rgba(99,102,241,0.5)] mb-2">
            {title}
          </h2>
          <p className="text-lg md:text-xl text-gray-300 mb-6 max-w-2xl">
            {subtitle}
          </p>
          <div className="flex flex-col gap-2 mt-4">
            {cards.map((card, idx) => (
              <button
                key={card.key}
                className={`text-left px-4 py-3 rounded-lg font-semibold text-xl transition-all duration-200 focus:outline-none
                  ${
                    active === idx
                      ? 'bg-gradient-to-r from-purple-400 via-fuchsia-500 to-indigo-500 text-white scale-105 shadow-lg'
                      : 'bg-[#181a28] text-gray-300 hover:bg-gradient-to-r hover:from-[#23243a] hover:to-[#2d2250] hover:text-white'
                  }
                `}
                onClick={() => setActive(idx)}
              >
                {card.label}
              </button>
            ))}
          </div>
        </div>
        {/* 右侧图片和文案 */}
        <div className="w-full md:w-1/2 flex flex-col items-center justify-center min-h-[420px]">
          <AnimatePresence mode="wait">
            <motion.div
              key={cards[active].key}
              initial={{ opacity: 0, x: 40, scale: 0.98 }}
              animate={{ opacity: 1, x: 0, scale: 1 }}
              exit={{ opacity: 0, x: -40, scale: 0.98 }}
              transition={{ duration: 0.5, ease: 'easeInOut' }}
              className="w-full flex flex-col items-center"
            >
              <div className="relative rounded-2xl overflow-hidden shadow-2xl mb-6 w-full max-w-md min-h-[260px] md:min-h-[300px] flex items-center justify-center bg-[#181028]">
                {!imgLoaded && (
                  <div className="absolute inset-0 flex items-center justify-center z-10">
                    <div className="w-10 h-10 border-4 border-indigo-400 border-t-transparent rounded-full animate-spin"></div>
                  </div>
                )}
                <Image
                  src={cards[active].img}
                  alt={cards[active].alt}
                  width={480}
                  height={300}
                  className={`object-cover w-full h-[260px] md:h-[300px] transition-opacity duration-300 ${
                    imgLoaded ? 'opacity-100' : 'opacity-0'
                  }`}
                  unoptimized
                  onLoad={() => setImgLoaded(true)}
                />
              </div>
              <h3 className="text-2xl font-bold text-indigo-100 mb-2 text-center">
                {cards[active].title}
              </h3>
              <p className="text-gray-300 text-base md:text-lg text-center max-w-lg">
                {cards[active].desc}
              </p>
            </motion.div>
          </AnimatePresence>
        </div>
      </div>
    </section>
  )
}
