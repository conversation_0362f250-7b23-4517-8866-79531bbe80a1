'use client'
import React, { useState } from 'react'
import { AnimatePresence, motion } from 'framer-motion'
import { Link } from '@i18n/routing'

// 添加流动渐变动画样式
const gradientAnimationStyle = `
  @keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }
  
  @keyframes silverShimmer {
    0% { 
      background: linear-gradient(135deg, #c0c0c0, #e0e0e0, #a0a0a0, #d0d0d0);
      box-shadow: 
        0 4px 15px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
    }
    25% { 
      background: linear-gradient(135deg, #d0d0d0, #f0f0f0, #b0b0b0, #e0e0e0);
      box-shadow: 
        0 4px 15px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
    }
    50% { 
      background: linear-gradient(135deg, #e0e0e0, #f8f8f8, #c0c0c0, #f0f0f0);
      box-shadow: 
        0 4px 15px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.4),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
    }
    75% { 
      background: linear-gradient(135deg, #d0d0d0, #f0f0f0, #b0b0b0, #e0e0e0);
      box-shadow: 
        0 4px 15px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
    }
    100% { 
      background: linear-gradient(135deg, #c0c0c0, #e0e0e0, #a0a0a0, #d0d0d0);
      box-shadow: 
        0 4px 15px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
    }
  }
`

interface Tab {
  key: string
  label: string
  image: string
  alt: string
  title: string
  desc: string
  button: string
  href: string
}
export default function WhyUsClient({
  title,
  subtitle,
  tabs,
}: {
  title: string
  subtitle: string
  tabs: Tab[]
}) {
  const [active, setActive] = useState(0)
  const [imgLoaded, setImgLoaded] = useState(true)
  const [imgKey, setImgKey] = useState(0)

  const handleTabClick = (idx: number) => {
    setActive(idx)
    setImgLoaded(false)
    setImgKey((prev) => prev + 1)
  }

  return (
    <section className="w-full bg-[#141417] py-16 px-4 md:px-0">
      <style dangerouslySetInnerHTML={{ __html: gradientAnimationStyle }} />
      <div className="max-w-5xl mx-auto text-center">
        <h2 className="text-3xl md:text-4xl font-bold text-indigo-400 drop-shadow-[0_2px_16px_rgba(99,102,241,0.5)] mb-3">
          {title}
        </h2>
        <p className="text-lg md:text-xl text-gray-300 mb-10">{subtitle}</p>
        {/* Tabs */}
        <div className="flex justify-center gap-4 mb-8">
          {tabs.map((tab, idx) => (
            <button
              key={tab.key}
              className={`px-5 py-2 rounded-full font-semibold transition-all duration-300
                ${
                  active === idx
                    ? 'text-gray-800 scale-110 backdrop-blur-md'
                    : 'bg-[#1a1a2e] text-gray-400 hover:bg-[#2a2a3e] hover:text-gray-300'
                }
              `}
              style={
                active === idx
                  ? {
                      animation: 'silverShimmer 2s ease-in-out infinite',
                      background:
                        'linear-gradient(135deg, #c0c0c0, #e0e0e0, #a0a0a0, #d0d0d0)',
                      boxShadow:
                        '0 4px 15px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2), inset 0 -1px 0 rgba(0, 0, 0, 0.1)',
                    }
                  : {}
              }
              onClick={() => handleTabClick(idx)}
            >
              {tab.label}
            </button>
          ))}
        </div>
        {/* 内容区 */}
        <div className="flex flex-col md:flex-row items-center gap-8 rounded-2xl p-6 md:p-10 shadow-lg min-h-[320px] relative overflow-hidden">
          {/* 流动渐变色边框 */}
          <div
            className="absolute inset-0 rounded-2xl opacity-30"
            style={{
              background:
                'linear-gradient(45deg, #8b5cf6, #ec4899, #3b82f6, #8b5cf6)',
              backgroundSize: '400% 400%',
              animation: 'gradientShift 3s ease infinite',
            }}
          ></div>
          <div className="absolute inset-[2px] bg-[#181028] rounded-2xl"></div>

          <div className="relative z-10 w-full md:w-80 h-56 flex items-center justify-center overflow-hidden">
            <AnimatePresence mode="wait">
              <motion.img
                key={imgKey}
                src={tabs[active].image}
                alt={tabs[active].alt}
                initial={{ opacity: 0, scale: 1.1, x: 40 }}
                animate={{
                  opacity: imgLoaded ? 1 : 0,
                  scale: imgLoaded ? 1 : 1.05,
                  x: 0,
                }}
                exit={{ opacity: 0, scale: 0.95, x: -40 }}
                transition={{ duration: 0.5, ease: 'easeInOut' }}
                className="w-full md:w-80 h-56 object-cover rounded-xl shadow-md"
                onLoad={() => setImgLoaded(true)}
                draggable={false}
              />
            </AnimatePresence>
            {!imgLoaded && (
              <div className="absolute inset-0 flex items-center justify-center bg-[#181028] bg-opacity-80 z-10">
                <div className="w-8 h-8 border-4 border-blue-400 border-t-transparent rounded-full animate-spin"></div>
              </div>
            )}
          </div>
          <div className="relative z-10 flex-1 text-left">
            <AnimatePresence mode="wait">
              <motion.div
                key={tabs[active].key}
                initial={{ opacity: 0, y: 30, scale: 0.98 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -30, scale: 0.98 }}
                transition={{ duration: 0.5, ease: 'easeInOut' }}
              >
                <h3 className="text-2xl font-bold text-white mb-3">
                  {tabs[active].title}
                </h3>
                <p className="text-gray-300 mb-6">{tabs[active].desc}</p>
                <Link href={tabs[active].href}>
                  <button className="group p-[4px] rounded-[12px] bg-gradient-to-b from-gray-700 to-gray-600 shadow-[0_2px_4px_rgba(0,0,0,0.7)] hover:shadow-[0_4px_8px_rgba(0,0,0,0.6)] active:shadow-[0_0px_1px_rgba(0,0,0,0.8)] active:scale-[0.995] transition-all duration-200">
                    <div className="bg-gradient-to-b from-gray-600 to-gray-700 rounded-[8px] px-3 py-2">
                      <div className="flex gap-2 items-center">
                        <span className="font-semibold text-white">
                          {tabs[active].button}
                        </span>
                      </div>
                    </div>
                  </button>
                </Link>
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </div>
    </section>
  )
}
