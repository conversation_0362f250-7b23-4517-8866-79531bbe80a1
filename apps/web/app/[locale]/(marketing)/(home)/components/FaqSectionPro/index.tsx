// components/FaqSection.tsx
import { FC } from 'react'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@ui/components/accordion'
import { getTranslations } from 'next-intl/server'

const FaqSectionPro: FC = async () => {
  const t = await getTranslations()

  const faqData = [
    {
      question: t('FaqSectionPro.faqData.a.question'),
      answer: t('FaqSectionPro.faqData.a.answer'),
    },
    {
      question: t('FaqSectionPro.faqData.b.question'),
      answer: t('FaqSectionPro.faqData.b.answer'),
    },
    {
      question: t('FaqSectionPro.faqData.c.question'),
      answer: t('FaqSectionPro.faqData.c.answer'),
    },
    {
      question: t('FaqSectionPro.faqData.d.question'),
      answer: t('FaqSectionPro.faqData.d.answer'),
    },
    {
      question: t('FaqSectionPro.faqData.e.question'),
      answer: t('FaqSectionPro.faqData.e.answer'),
    },
    {
      question: t('FaqSectionPro.faqData.f.question'),
      answer: t('FaqSectionPro.faqData.f.answer'),
    },
    {
      question: t('FaqSectionPro.faqData.g.question'),
      answer: t('FaqSectionPro.faqData.g.answer'),
    },
    {
      question: t('FaqSectionPro.faqData.h.question'),
      answer: t('FaqSectionPro.faqData.h.answer'),
    },
    {
      question: t('FaqSectionPro.faqData.i.question'),
      answer: t('FaqSectionPro.faqData.i.answer'),
    },
    {
      question: t('FaqSectionPro.faqData.j.question'),
      answer: t('FaqSectionPro.faqData.j.answer'),
    },
    {
      question: t('FaqSectionPro.faqData.k.question'),
      answer: t('FaqSectionPro.faqData.k.answer'),
    },
    {
      question: t('FaqSectionPro.faqData.l.question'),
      answer: t('FaqSectionPro.faqData.l.answer'),
    },
  ]

  return (
    <section className="py-16 relative overflow-hidden bg-[#141417]">
      <div className="container max-w-6xl">
        <div className="pt-10 px-4 mb-10">
          <h2 className="text-3xl md:text-4xl font-bold text-indigo-400 drop-shadow-[0_2px_16px_rgba(99,102,241,0.5)] mb-4 text-center">
            {t('FaqSectionPro.title')}
          </h2>
          <p className="text-lg md:text-xl text-gray-300 max-w-4xl mx-auto text-center">
            {t('FaqSectionPro.description')}
          </p>
        </div>
      </div>
      <div className="max-w-4xl mx-auto">
        <Accordion type="single" collapsible className="flex flex-col gap-3">
          {faqData.map((item, i) => (
            <AccordionItem
              key={i}
              value={`faq-item-${i}`}
              className="rounded-2xl border-2 border-transparent bg-gradient-to-br from-[#1a1333cc] via-[#181028cc] to-[#23203aee] px-6 py-4 shadow-sm hover:shadow-[0_2px_12px_0_rgba(168,85,247,0.10)] hover:border-fuchsia-500/40 transition-all duration-300 backdrop-blur-md"
            >
              <AccordionTrigger className="py-2 text-lg text-left flex items-center">
                <div className="flex items-center gap-3 flex-1">
                  <span className="text-white font-semibold">
                    {item.question}
                  </span>
                  <div className="sr-only" itemProp="text">
                    {item.answer}
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <div className="space-y-3 leading-6 text-gray-300">
                  <p className="whitespace-pre-line">{item.answer}</p>
                </div>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
        <p className="text-center mt-8 text-gray-600">
          {t('FaqSectionPro.description')}
        </p>
      </div>
    </section>
  )
}

export default FaqSectionPro
