'use client'
//
import { useState, useCallback, useEffect, useMemo } from 'react'
import ReactCompareImage from 'react-compare-image'
import { motion } from 'framer-motion'
// import Masonry from 'react-masonry-css'
import type { StyleType } from '@marketing/home/<USER>'
import { useTranslations } from 'next-intl'
import { useRouter } from 'next/navigation'

// 添加银色闪烁动画样式和隐藏滚动条样式
const silverShimmerStyle = `
  @keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }
  
  @keyframes silverShimmer {
    0% { 
      background: linear-gradient(135deg, #c0c0c0, #e0e0e0, #a0a0a0, #d0d0d0);
      box-shadow: 
        0 4px 15px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
    }
    25% { 
      background: linear-gradient(135deg, #d0d0d0, #f0f0f0, #b0b0b0, #e0e0e0);
      box-shadow: 
        0 4px 15px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
    }
    50% { 
      background: linear-gradient(135deg, #e0e0e0, #f8f8f8, #c0c0c0, #f0f0f0);
      box-shadow: 
        0 4px 15px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.4),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
    }
    75% { 
      background: linear-gradient(135deg, #d0d0d0, #f0f0f0, #b0b0b0, #e0e0e0);
      box-shadow: 
        0 4px 15px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
    }
    100% { 
      background: linear-gradient(135deg, #c0c0c0, #e0e0e0, #a0a0a0, #d0d0d0);
      box-shadow: 
        0 4px 15px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
    }
  }

  /* 隐藏滚动条样式 */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Chrome, Safari and Opera */
  }

  /* 对比图容器内元素铺满并保持裁剪覆盖 */
  .compare-container { height: 100%; width: 100%; }
  .compare-container > div { height: 100% !important; width: 100% !important; }
  .compare-container img { 
    width: 100% !important; 
    height: 100% !important; 
    object-fit: cover !important;
    display: block !important;
  }
  
  /* 确保ReactCompareImage组件内部元素填满 */
  .compare-container [style*="position: relative"] { height: 100% !important; }
  .compare-container [style*="position: absolute"] { height: 100% !important; }
  .compare-container > div > div { height: 100% !important; }

  /* 固定 3:4 比例盒 (更适合展示对比图片) */
  .aspect-3-4 { position: relative; width: 100%; }
  .aspect-3-4::before { content: ''; display: block; padding-top: 133.3333%; }
  .aspect-3-4 > .aspect-content { position: absolute; inset: 0; }
`
interface ImageComparisonProps {
  hideCreateButton?: boolean
  hideFilterBar?: boolean
}

const ImageComparison = ({
  hideCreateButton = false,
  hideFilterBar = false,
}: ImageComparisonProps) => {
  const router = useRouter()
  const t = useTranslations()
  const [activeFilter, setActiveFilter] = useState('Ghibli Style')
  const [loadingStates, setLoadingStates] = useState<Record<number, boolean>>(
    {}
  )
  const customStyles: StyleType[] = [
    // Ghibli Style
    {
      id: 1,
      title: t('styles.ghibliWoman'),
      category: 'Ghibli Style',
      type: 'image-to-image',
      href: '/ai/ghibli',
      prompt: 'Transform this photo into Studio Ghibli animation style',
      ratio: '3:2',
      originalImage:
        'https://oss.x2one.us/image2image/2025/08/12/1754999222546_ay2obc.jpeg',
      generatedImage:
        'https://oss.x2one.us/image2image/2025/08/12/1754999252557_s4pp31.jpeg',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    {
      id: 2,
      title: t('styles.ghibliChild'),
      category: 'Ghibli Style',
      type: 'image-to-image',
      href: '/ai/ghibli',
      prompt: 'Transform this photo into Studio Ghibli animation style',
      ratio: '3:2',
      originalImage:
        'https://oss.x2one.us/image2image/2025/08/12/1754999539576_hnuhqz.jpeg',
      generatedImage:
        'https://oss.x2one.us/image2image/2025/08/12/1754999549693_a770wr.jpeg',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    {
      id: 3,
      title: t('styles.ghibliCouple'),
      category: 'Ghibli Style',
      type: 'image-to-image',
      href: '/ai/ghibli',
      prompt: 'Transform this photo into Studio Ghibli animation style',
      ratio: '3:2',
      originalImage:
        'https://oss.x2one.us/image2image/2025/08/12/1754999780814_ld1lug.jpeg',
      generatedImage:
        'https://oss.x2one.us/image2image/2025/08/12/1754999789344_nekph4.jpeg',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    {
      id: 4,
      title: t('styles.ghibliDog'),
      category: 'Ghibli Style',
      href: '/ai/ghibli',
      type: 'image-to-image',
      prompt: 'Transform this photo into Studio Ghibli animation style',
      ratio: '3:2',
      originalImage:
        'https://oss.x2one.us/image2image/2025/08/12/1755000111017_anqb8l.jpeg',
      generatedImage:
        'https://oss.x2one.us/image2image/2025/08/12/1755000122255_w5rcm9.jpeg',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    // Old Photo Restoration
    {
      id: 5,
      title: t('styles.restoreLittleGirl'),
      category: 'Old Photo Restoration',
      href: '/ai/photo-restoration',
      type: 'image-to-image',
      prompt: 'Restore and enhance this old damaged photograph',
      ratio: '3:2',
      originalImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/B-1.png',
      generatedImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/B-2.png',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    {
      id: 6,
      title: t('styles.restoreOldLady'),
      category: 'Old Photo Restoration',
      href: '/ai/photo-restoration',
      type: 'image-to-image',
      prompt: 'Restore and enhance this old damaged photograph',
      ratio: '3:2',
      originalImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/B-3.png',
      generatedImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/B-4.png',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    {
      id: 7,
      title: t('styles.restoreSoldier'),
      category: 'Old Photo Restoration',
      href: '/ai/photo-restoration',
      type: 'image-to-image',
      prompt: 'Restore and enhance this old damaged photograph',
      ratio: '3:2',
      originalImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/B-5.png',
      generatedImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/B-6.png',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    {
      id: 8,
      title: t('styles.restoreFamily'),
      category: 'Old Photo Restoration',
      href: '/ai/photo-restoration',
      type: 'image-to-image',
      prompt: 'Restore and enhance this old damaged photograph',
      ratio: '3:2',
      originalImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/B-7.png',
      generatedImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/B-8.png',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    // Background Removal
    {
      id: 9,
      title: t('styles.removeLipstickBG'),
      category: 'Background Removal',
      href: '/ai/background-removal',
      type: 'image-to-image',
      prompt: 'Remove background from this image',
      ratio: '3:2',
      originalImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/C-1.png',
      generatedImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/C-2.png',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    {
      id: 10,
      title: t('styles.removeBagBG'),
      category: 'Background Removal',
      href: '/ai/background-removal',
      type: 'image-to-image',
      prompt: 'Remove background from this image',
      ratio: '3:2',
      originalImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/C-3.png',
      generatedImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/C-4.png',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    {
      id: 11,
      title: t('styles.removeWomanBG'),
      category: 'Background Removal',
      href: '/ai/background-removal',
      type: 'image-to-image',
      prompt: 'Remove background from this image',
      ratio: '3:2',
      originalImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/C-5.png',
      generatedImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/C-6.png',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    {
      id: 12,
      title: t('styles.removeDogBG'),
      category: 'Background Removal',
      href: '/ai/background-removal',
      type: 'image-to-image',
      prompt: 'Remove background from this image',
      ratio: '3:2',
      originalImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/C-7.png',
      generatedImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/C-8.png',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    // Color Correction
    {
      id: 13,
      title: t('styles.fixCatColors'),
      category: 'Color Correction',
      href: '/tools/colorize',
      type: 'image-to-image',
      prompt: 'Correct and enhance the colors of this photograph',
      ratio: '3:2',
      originalImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/D-1.png',
      generatedImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/D-2.png',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    {
      id: 14,
      title: t('styles.fixTemperature'),
      category: 'Color Correction',
      href: '/tools/colorize',
      type: 'image-to-image',
      prompt: 'Correct and enhance the colors of this photograph',
      ratio: '3:2',
      originalImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/D-3.png',
      generatedImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/D-4.png',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    {
      id: 15,
      title: t('styles.fixRestaurant'),
      category: 'Color Correction',
      href: '/tools/colorize',
      type: 'image-to-image',
      prompt: 'Correct and enhance the colors of this photograph',
      ratio: '3:2',
      originalImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/D-5.png',
      generatedImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/D-6.png',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    {
      id: 16,
      title: t('styles.fixSofaColors'),
      category: 'Color Correction',
      href: '/tools/colorize',
      type: 'image-to-image',
      prompt: 'Correct and enhance the colors of this photograph',
      ratio: '3:2',
      originalImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/D-7.png',
      generatedImage:
        'https://imggen-website-static-resources.oss-ap-southeast-1.aliyuncs.com/imggen-home/D-8.png',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    // Age Progression
    {
      id: 17,
      title: t('styles.ageBoyToAdult'),
      category: 'Age Progression',
      href: '/ai/old-filter',
      type: 'image-to-image',
      prompt: 'Make the character in the picture grow 20 years older',
      ratio: '3:2',
      originalImage:
        'https://oss.x2one.us/image2image/2025/08/12/1755002991568_ectsei.jpeg',
      generatedImage:
        'https://oss.x2one.us/image2image/2025/08/12/1755002995377_46gdc5.jpeg',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    {
      id: 18,
      title: t('styles.ageWomanToElder'),
      category: 'Age Progression',
      href: '/ai/old-filter',
      type: 'image-to-image',
      prompt: 'Make the character in the picture grow 20 years older',
      ratio: '3:2',
      originalImage:
        'https://oss.x2one.us/image2image/2025/08/12/1755003240519_rdjwjb.jpeg',
      generatedImage:
        'https://oss.x2one.us/image2image/2025/08/12/1755003360396_sn58ac.jpeg',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    {
      id: 19,
      title: t('styles.ageCouple'),
      category: 'Age Progression',
      href: '/ai/old-filter',
      type: 'image-to-image',
      prompt: 'Make the character in the picture grow 20 years older',
      ratio: '3:2',
      originalImage:
        'https://oss.x2one.us/image2image/2025/08/12/1755002864519_q2ertm.jpeg',
      generatedImage:
        'https://oss.x2one.us/image2image/2025/08/12/1755002900212_ezmm4m.jpeg',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
    {
      id: 20,
      title: t('styles.ageFriends'),
      category: 'Age Progression',
      href: '/ai/old-filter',
      type: 'image-to-image',
      prompt: 'Make the two girls in the picture look old',
      ratio: '3:2',
      originalImage:
        'https://oss.x2one.us/image2image/2025/08/12/1755003970057_zdhoac.jpeg',
      generatedImage:
        'https://oss.x2one.us/image2image/2025/08/12/1755003975297_tvjk2u.jpeg',
      showButton: true,
      showPrompt: true,
      showInStyles: true,
    },
  ]
  // 使用自定义风格数据
  const styles = useMemo(() => customStyles, [])

  const categoriesMap = {
    'Ghibli Style': t('ImageComparison.categories.Portrait'),
    'Old Photo Restoration': t('ImageComparison.categories.Vintage'),
    'Background Removal': t('ImageComparison.categories.Product'),
    'Color Correction': t('ImageComparison.categories.Art'),
    'Age Progression': t('ImageComparison.categories.Vintage'),
  }

  // 预加载图片并跟踪加载状态
  const preloadImages = useCallback((style: StyleType) => {
    let loadedCount = 0
    const checkBothLoaded = () => {
      loadedCount++
      if (loadedCount === 2) {
        setLoadingStates((prev) => ({
          ...prev,
          [style.id]: false,
        }))
      }
    }

    // 预加载原始图片
    const originalImg = new Image()
    originalImg.onload = checkBothLoaded
    originalImg.onerror = checkBothLoaded // 处理加载失败的情况
    originalImg.src = style.originalImage

    // 预加载生成的图片
    const generatedImg = new Image()
    generatedImg.onload = checkBothLoaded
    generatedImg.onerror = checkBothLoaded // 处理加载失败的情况
    generatedImg.src = style.generatedImage
  }, [])

  // 初始化加载状态并开始预加载
  useEffect(() => {
    if (styles.length > 0) {
      const initialLoadingStates = styles.reduce((acc, style) => {
        acc[style.id] = true
        return acc
      }, {} as Record<number, boolean>)
      setLoadingStates(initialLoadingStates)

      // 开始预加载所有图片
      styles.forEach((style) => {
        preloadImages(style)
      })
    }
  }, [styles, preloadImages])

  // 处理风格选择
  const handleStyleSelect = (style: StyleType) => {
    router.push(style.href)
  }

  // 根据当前筛选器筛选风格
  const filteredStyles = styles.filter(
    (style) => style.category === activeFilter
  )

  // 获取所有有图片的类别
  const availableCategories: string[] = []
  styles.forEach((style) => {
    if (!availableCategories.includes(style.category)) {
      availableCategories.push(style.category)
    }
  })

  return (
    <div className="w-full px-4 md:px-0">
      <style dangerouslySetInnerHTML={{ __html: silverShimmerStyle }} />
      <div className="max-w-7xl mx-auto">
        {/* 风格选择器 */}
        {!hideFilterBar && (
          <div className="mb-8 overflow-x-auto pb-2 scrollbar-hide">
            <div className="flex justify-center gap-4 min-w-max">
              {availableCategories.map((category) => (
                <button
                  key={category}
                  className={`px-5 py-2 rounded-full font-semibold transition-all duration-300
                    ${
                      activeFilter === category
                        ? 'text-gray-800 backdrop-blur-md'
                        : 'bg-[#1a1a2e] text-gray-400 hover:bg-[#2a2a3e] hover:text-gray-300'
                    }
                  `}
                  style={
                    activeFilter === category
                      ? {
                          animation: 'silverShimmer 2s ease-in-out infinite',
                          background:
                            'linear-gradient(135deg, #c0c0c0, #e0e0e0, #a0a0a0, #d0d0d0)',
                          boxShadow:
                            '0 4px 15px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2), inset 0 -1px 0 rgba(0, 0, 0, 0.1)',
                        }
                      : {}
                  }
                  onClick={() => setActiveFilter(category)}
                >
                  {categoriesMap[category as keyof typeof categoriesMap]}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* 使用响应式网格布局，取消瀑布流 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredStyles.map((style) => (
            <motion.div
              key={style.id}
              className="rounded-2xl overflow-hidden border border-white/10 shadow-lg bg-[#1a1a2e]"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: style.id * 0.05 }}
            >
              <div className="relative z-10">
                {/* 卡片标题和分类标签 */}
                <div className="p-4 pb-2">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-semibold text-white">
                      {style.title}
                    </h3>
                    <span className="px-3 py-1 bg-transparent border border-purple-400 text-purple-300 text-xs rounded-full font-medium">
                      {(() => {
                        // 特殊处理某些图片的标签
                        if (
                          style.title === 'Remove Woman BG' ||
                          style.title === 'Remove Dog BG'
                        ) {
                          return 'Remove'
                        }
                        return categoriesMap[
                          style.category as keyof typeof categoriesMap
                        ]
                      })()}
                    </span>
                  </div>
                </div>

                {/* 图片比较容器 - 使用 3:4 比例盒，内部内容绝对填充 */}
                <div className="w-full">
                  <div className="aspect-3-4">
                    <div className="aspect-content relative">
                      {loadingStates[style.id] ? (
                        <div className="w-full h-full bg-[#1a1a2e] animate-pulse flex items-center justify-center">
                          <div className="w-8 h-8 border-4 border-blue-400 border-t-transparent rounded-full animate-spin"></div>
                        </div>
                      ) : (
                        <motion.div
                          className="w-full h-full"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ duration: 0.5 }}
                        >
                          <div className="h-full compare-container">
                            <ReactCompareImage
                              leftImage={style.originalImage}
                              rightImage={style.generatedImage}
                              sliderLineWidth={2}
                              sliderLineColor="#8b5cf6"
                              sliderPositionPercentage={0.5}
                              handle={
                                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-fuchsia-600 via-purple-500 to-indigo-500 flex items-center justify-center shadow-md rotate-90">
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                    className="w-5 h-5 text-white"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M8 9l4-4 4 4m0 6l-4 4-4-4"
                                    />
                                  </svg>
                                </div>
                              }
                              hover={false}
                            />
                          </div>
                        </motion.div>
                      )}

                      {/* 悬浮在图片上的按钮 */}
                      {style.showButton && !hideCreateButton && (
                        <div className="absolute bottom-4 left-1/2 -translate-x-1/2 z-20">
                          <div className="relative group">
                            <button
                              onClick={() => handleStyleSelect(style)}
                              className="relative inline-block p-px font-semibold leading-6 text-white bg-neutral-900 shadow-2xl cursor-pointer rounded-2xl shadow-purple-950 transition-all duration-300 ease-in-out hover:scale-105 active:scale-95 hover:shadow-purple-800"
                            >
                              <span className="absolute inset-0 rounded-2xl bg-gradient-to-r from-purple-500 via-pink-500 to-purple-600 p-[2px] opacity-0 transition-opacity duration-500 group-hover:opacity-100"></span>
                              <span className="relative z-10 block px-6 py-3 rounded-2xl bg-neutral-950">
                                <div className="relative z-10 flex items-center space-x-3">
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-4 w-4"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M13 10V3L4 14h7v7l9-11h-7z"
                                    />
                                  </svg>
                                  <span className="transition-all duration-500 group-hover:translate-x-1.5 group-hover:text-purple-300 whitespace-nowrap">
                                    {t('pricingBottomArea.createButton')}
                                  </span>
                                </div>
                              </span>
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default ImageComparison
