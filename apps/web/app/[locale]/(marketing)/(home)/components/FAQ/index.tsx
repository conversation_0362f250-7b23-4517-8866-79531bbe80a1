const faqData = [
  {
    question: 'What is an AI image generator?',
    answer:
      'An AI image generator is a tool that uses artificial intelligence to create images based on text descriptions or existing images. It interprets your input and produces visuals accordingly.',
  },
  {
    question: 'How does text-to-image AI generation work?',
    answer:
      'Text-to-image AI generation involves inputting a descriptive prompt, which the AI processes to generate an image that matches the description. The more detailed your prompt, the more accurate the result.',
  },
  {
    question: 'Can I transform an existing image using AI?',
    answer:
      'Yes, with image-to-image AI generation, you can upload an existing image, and the AI will modify it based on your instructions, allowing for style changes or enhancements.',
  },
  {
    question: 'Is IMGGen free to use?',
    answer:
      'IMGGen offers a free plan with basic features and limited daily generations. For advanced features and unlimited access, you can upgrade to our Pro or Team plans.',
  },
  {
    question: 'Do I need to create an account to use IMGGen?',
    answer:
      'No, you can start generating images immediately without signing up. However, creating an account allows you to save your work and access additional features.',
  },
  {
    question: 'Are AI-generated images copyrighted?',
    answer:
      'This is the new open question for the whole world. There is still no answer to this question. Currently, IMGGen does not make any copyright claim over the output content that user generates and does not have the ability to license or release the use of that output content to user. Please note that this situation may change as the development of copyright laws in various jurisdictions.',
  },
  {
    question: 'What styles can I generate with IMGGen?',
    answer:
      'IMGGen supports various styles, including photorealistic, anime, watercolor, cyberpunk, and more. You can choose a style that best fits your creative vision.',
  },
  {
    question: 'Can I use IMGGen for commercial purposes?',
    answer:
      'If you want to use our AI text-to-image generator for any legal personal or commercial purposes, you have to follow our terms.',
  },
  {
    question: 'How can I improve the quality of generated images?',
    answer:
      'To enhance image quality, provide detailed prompts, select higher resolution settings, and choose appropriate styles. Experimenting with different inputs can yield better results.',
  },
  {
    question: 'Is there a limit to the number of images I can generate?',
    answer:
      'The free plan has a daily limit on image generations. Upgrading to a Pro or Team plan removes these limitations and offers additional benefits.',
  },
]

const FAQ = () => {
  return (
    <div className="max-w-4xl mx-auto px-4 py-16 bg-white">
      <h1 className="text-center text-3xl font-bold text-gray-800 mb-4">
        Frequently Asked Questions
      </h1>
      <p className="text-center text-gray-600 mb-12">
        Everything you need to know about our AI image generation plans,
        credits, and usage rights.
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {faqData.map((faq, index) => (
          <div key={index} className="bg-gray-50 rounded-lg shadow-sm p-6">
            <h3 className="text-gray-800 font-medium text-lg mb-3">
              {faq.question}
            </h3>
            <p className="text-gray-600 text-base">{faq.answer}</p>
          </div>
        ))}
      </div>

      <div className="text-center text-gray-500 text-sm mt-10">
        If you have further questions or need assistance, feel free to contact
        our support team.
      </div>
    </div>
  )
}

export default FAQ
