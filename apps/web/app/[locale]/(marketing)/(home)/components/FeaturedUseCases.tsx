'use client'

import { useTranslations } from 'next-intl'
import { Users, TrendingUp, Heart, Camera } from 'lucide-react'

export default function FeaturedUseCases() {
  const t = useTranslations('featuredUseCases')

  const iconMap: Record<string, JSX.Element> = {
    'Content Creators': <Camera className="w-6 h-6 text-purple-400" />,
    'Marketing Teams': <TrendingUp className="w-6 h-6 text-blue-400" />,
    'Social Media Influencers': <Heart className="w-6 h-6 text-pink-400" />,
    'Family Users': <Users className="w-6 h-6 text-green-400" />,
  }

  // Get categories data from translations
  const categories = []
  for (let i = 0; i < 4; i++) {
    try {
      const userType = t(`categories.${i}.userType`)
      const result = t(`categories.${i}.result`)
      const testimonial = t(`categories.${i}.testimonial`)

      // Get tools array by accessing individual tool items
      const tools = []
      for (let j = 0; j < 2; j++) {
        try {
          const tool = t(`categories.${i}.tools.${j}`)
          // Check if translation exists (if it's the same as the key, it means no translation found)
          if (tool && tool !== `categories.${i}.tools.${j}`) {
            tools.push(tool)
          } else {
            break // No more tools
          }
        } catch {
          break // No more tools
        }
      }

      categories.push({
        userType,
        tools,
        result,
        testimonial,
      })
    } catch (error) {
      continue
    }
  }

  return (
    <section className="py-16 bg-gray-900">
      <div className="container mx-auto px-4 max-w-6xl">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            {t('title')}
          </h2>
          <p className="text-lg text-gray-300 max-w-3xl mx-auto">
            {t('subtitle')}
          </p>
        </div>

        {/* Use Cases Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
          {categories.map((category, index) => {
            const userTypeKey = category.userType.replace(/\s+/g, ' ')
            const icon = iconMap[userTypeKey] || (
              <Users className="w-6 h-6 text-gray-400" />
            )

            return (
              <div
                key={index}
                className="bg-gray-800 rounded-lg p-6 border border-gray-700 hover:border-gray-600 transition-colors"
              >
                {/* Header */}
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-10 h-10 bg-gray-700 rounded-lg flex items-center justify-center">
                    {icon}
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white">
                      {category.userType}
                    </h3>
                    <span className="text-green-400 text-sm font-medium">
                      {category.result}
                    </span>
                  </div>
                </div>

                {/* Tools */}
                <div className="mb-4">
                  <div className="flex flex-wrap gap-2">
                    {category.tools.map((tool, toolIndex) => (
                      <span
                        key={toolIndex}
                        className="px-3 py-1 bg-gray-700 text-gray-300 rounded text-sm"
                      >
                        {tool}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Testimonial */}
                <blockquote className="text-gray-400 italic">
                  "{category.testimonial}"
                </blockquote>
              </div>
            )
          })}
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
          <div className="text-center">
            <div className="text-2xl md:text-3xl font-bold text-white mb-1">
              {t('statistics.users.number')}
            </div>
            <div className="text-gray-400 text-sm">
              {t('statistics.users.label')}
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl md:text-3xl font-bold text-white mb-1">
              {t('statistics.conversions.number')}
            </div>
            <div className="text-gray-400 text-sm">
              {t('statistics.conversions.label')}
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl md:text-3xl font-bold text-white mb-1">
              {t('statistics.faceSwap.number')}
            </div>
            <div className="text-gray-400 text-sm">
              {t('statistics.faceSwap.label')}
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl md:text-3xl font-bold text-white mb-1">
              {t('statistics.freeTools.number')}
            </div>
            <div className="text-gray-400 text-sm">
              {t('statistics.freeTools.label')}
            </div>
          </div>
        </div>

        {/* Simple CTA */}
        <div className="text-center">
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <h3 className="text-xl font-bold text-white mb-2">
              {t('cta.title')}
            </h3>
            <p className="text-gray-300 mb-4">{t('cta.description')}</p>
            <button className="bg-purple-600 hover:bg-purple-500 text-white font-semibold py-3 px-8 rounded-lg transition-colors">
              {t('cta.button')}
            </button>
          </div>
        </div>
      </div>
    </section>
  )
}
