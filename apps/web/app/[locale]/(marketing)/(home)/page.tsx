import { getTranslations } from 'next-intl/server'
import Image from 'next/image'
import TestimonialSection from './components/TestimonialSection'
import FaqSectionPro from './components/FaqSectionPro'
import CtaSection from '@/[locale]/components/CtaSection'
import ScrollToTopButton from './components/ScrollToTopButton'
import FeatureShowcase from './components/FeatureShowcase'
import MoreFeatures from './components/MoreFeatures'
import WhyUs from './components/WhyUs'
import UserCase from './components/UserCase'
import ForEveryoneSection from './components/ForEveryone/ForEveryoneSection'
import InterestRecommendationSection from './components/InterestRecommendationSection'
import ScrollAnimation from './components/ScrollAnimation/ScrollAnimation'
import PopularFeatures from './components/PopularFeatures'
import HeroButtons from './components/HeroButtons'
import HeroCarousel from './components/HeroCarousel'
import TrustLogo from './components/TrustLogo'
import VideoCreationShowcase from './components/VideoCreationShowcase'
import FeaturedUseCases from './components/FeaturedUseCases'
import './home-gradient-border.css'
import './scroll-animations.css'
// 

export async function generateMetadata() {
  const t = await getTranslations()
  return {
    title: t('home-image-t'),
    description: t('home-image-d'),
    keywords: t('home-image-k'),
  }
}

const Index = async () => {
  const t = await getTranslations()
  return (
    <div className="w-full bg-gradient-to-r from-[#1a0033] via-[#0a001a] to-[#1a0033] min-h-screen text-white">
      <div className="relative pt-24 bg-gradient-to-r from-[#1a0033] via-[#0a001a] to-[#1a0033]">
        {/* 平铺式标题区 */}
        <div className="relative w-full max-w-[1300px] mx-auto px-4 md:px-8 py-16 md:py-20">
          <div className="text-center space-y-6">
            {/* 顶部标签 */}
            <div className="relative inline-flex items-center gap-2 bg-white/5 backdrop-blur-lg border border-white/10 rounded-full px-4 py-2 text-sm shadow-lg shadow-purple-500/10">
              {/* 磨砂玻璃内层光效 */}
              <div className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-500/5 via-transparent to-pink-500/5"></div>

              {/* 边框高光效果 */}
              <div className="absolute inset-0 rounded-full border border-gradient-to-r from-purple-400/20 via-pink-400/20 to-purple-400/20"></div>

              <span className="relative w-2 h-2 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-pulse"></span>
              <span className="relative text-purple-200 font-medium">
                {t('hero.badge')}
              </span>
            </div>

            {/* 主标题 - 平铺显示 */}
            <h1 className=" text-3xl sm:text-4xl md:text-4xl lg:text-6xl font-bold text-white leading-loose max-w-5xl mx-auto">
              {t('hero.title')}
            </h1>

            {/* 描述文字 */}
            <p className="text-lg md:text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              {t('hero.description')}
            </p>

            {/* 按钮组 */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-8">
              <HeroButtons />
            </div>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="relative w-full max-w-[1300px] mx-auto px-4 md:px-8 pb-16">
          {/* 轮播图区域 - 可选显示 */}
          <div className="flex justify-center mb-16">
            <div className="w-full max-w-4xl">
              <div className="relative rounded-2xl overflow-hidden bg-gray-800/40 backdrop-blur-sm p-6 border border-purple-500/20">
                <HeroCarousel />
              </div>
            </div>
          </div>

          {/* Popular Features Section */}
          <PopularFeatures />
          <TrustLogo />
        </div>
      </div>

      {/* Video Creation Showcase */}
      <ScrollAnimation
        direction="up"
        delay={100}
        duration={1000}
        className="scroll-animation-container"
      >
        <VideoCreationShowcase />
      </ScrollAnimation>

      <ScrollAnimation
        direction="up"
        delay={200}
        duration={1000}
        className="scroll-animation-container"
      >
        <FeatureShowcase />
      </ScrollAnimation>

      <ScrollAnimation
        direction="left"
        delay={300}
        duration={1000}
        className="scroll-animation-container"
      >
        <MoreFeatures />
      </ScrollAnimation>

      {/* Featured Use Cases */}
      <ScrollAnimation
        direction="fade"
        delay={150}
        duration={1200}
        className="scroll-animation-container"
      >
        <FeaturedUseCases />
      </ScrollAnimation>

      <ScrollAnimation
        direction="right"
        delay={200}
        duration={1000}
        className="scroll-animation-container"
      >
        <WhyUs />
      </ScrollAnimation>

      <ScrollAnimation
        direction="up"
        delay={300}
        duration={1000}
        className="scroll-animation-container"
      >
        <UserCase />
      </ScrollAnimation>

      <ScrollAnimation
        direction="fade"
        delay={200}
        duration={1200}
        className="scroll-animation-container"
      >
        <ForEveryoneSection />
      </ScrollAnimation>

      <ScrollAnimation
        direction="up"
        delay={300}
        duration={1000}
        className="scroll-animation-container"
      >
        <TestimonialSection />
      </ScrollAnimation>

      <ScrollAnimation
        direction="left"
        delay={200}
        duration={1000}
        className="scroll-animation-container"
      >
        <FaqSectionPro />
      </ScrollAnimation>

      <ScrollAnimation
        direction="right"
        delay={300}
        duration={1000}
        className="scroll-animation-container"
      >
        <InterestRecommendationSection />
      </ScrollAnimation>

      <ScrollAnimation
        direction="up"
        delay={200}
        duration={1000}
        className="scroll-animation-container"
      >
        <CtaSection />
      </ScrollAnimation>

      <ScrollToTopButton />
    </div>
  )
}

export default Index
