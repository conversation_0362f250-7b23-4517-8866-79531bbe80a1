import { BaseApiAdapter } from './base'
import {
  GenerationRequest,
  GenerationResponse,
  UnifiedTaskDetail,
  TaskType,
} from '../types'
import { API_ENDPOINTS } from '../config'

// KieAI 适配器
export class KieAiAdapter extends BaseApiAdapter {
  readonly provider = 'kieai' as const

  async generate(request: GenerationRequest): Promise<GenerationResponse> {
    try {
      const { taskType, input } = request

      // 验证输入
      const validation = this.validateInput(input, taskType)
      if (!validation.isValid) {
        return {
          success: false,
          error: validation.error || 'Invalid input parameters',
        }
      }

      // 根据任务类型选择端点
      let endpoint: string
      switch (taskType) {
        case 'aismile':
          endpoint = API_ENDPOINTS.kieai.aismile
          break
        case 'imagetovideo':
          endpoint = API_ENDPOINTS.kieai.imagetovideo
          break
        case 'photo-to-anime':
          endpoint = API_ENDPOINTS.kieai['photo-to-anime']
          break
        case 'image-extender':
          endpoint = API_ENDPOINTS.kieai['image-extender']
          break
        default:
          return {
            success: false,
            error: `Unsupported task type for KieAI: ${taskType}`,
          }
      }

      const response = await this.makeRequest(endpoint, {
        method: 'POST',
        body: JSON.stringify(input),
      })

      const data = await response.json()

      if (data.code === 200 && data.data?.taskId) {
        return {
          success: true,
          taskId: data.data.taskId,
          message: data.msg,
          data: data.data,
        }
      } else {
        return {
          success: false,
          error: data.msg || 'Failed to create task',
        }
      }
    } catch (error) {
      return this.handleError(error, 'generate')
    }
  }

  async getTaskStatus(
    taskId: string,
    signal?: AbortSignal,
    taskType?: TaskType
  ): Promise<UnifiedTaskDetail> {
    try {
      // 根据任务类型选择状态查询端点
      let statusEndpoint: string
      if (['photo-to-anime', 'image-extender'].includes(taskType || '')) {
        statusEndpoint = `${API_ENDPOINTS.kieai.imageStatus}?taskId=${taskId}`
      } else {
        statusEndpoint = `${API_ENDPOINTS.kieai.status}?taskId=${taskId}`
      }

      const response = await this.makeRequest(statusEndpoint, { signal })
      const data = await response.json()

      if (data.code === 200 && data.data) {
        // 如果没有传入taskType，尝试推断
        const inferredTaskType = taskType || this.inferTaskType(data.data)
        return this.formatToUnified(data.data, inferredTaskType)
      } else {
        throw new Error(data.msg || 'Failed to get task status')
      }
    } catch (error) {
      // 如果是取消错误，直接抛出
      if (error instanceof Error && error.name === 'AbortError') {
        throw error
      }

      throw new Error(
        `Failed to get task status: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      )
    }
  }

  formatToUnified(apiResponse: any, taskType: TaskType): UnifiedTaskDetail {
    const status = this.mapStatus(apiResponse.state || apiResponse.status)

    // 统一输入数据访问，优先使用generateParam，回退到inputInfo以保持兼容性
    const inputData = apiResponse.generateParam || apiResponse.inputInfo

    // 根据任务类型格式化输出数据
    let output: any = undefined
    if (['photo-to-anime', 'image-extender'].includes(taskType || '')) {
      // 图像任务返回图像数据
      if (apiResponse.imageInfo) {
        // 处理原有格式：imageInfo
        output = {
          images:
            apiResponse.imageInfo.images ||
            (apiResponse.imageInfo.imageUrl
              ? [apiResponse.imageInfo.imageUrl]
              : []) ||
            (apiResponse.imageInfo.image_url
              ? [apiResponse.imageInfo.image_url]
              : []),
          imageUrl:
            apiResponse.imageInfo.imageUrl || apiResponse.imageInfo.image_url,
          ...apiResponse.imageInfo,
        }
      } else if (apiResponse.response?.resultUrls) {
        // 处理新格式：response.resultUrls
        const resultUrls = apiResponse.response.resultUrls
        output = {
          images: Array.isArray(resultUrls) ? resultUrls : [resultUrls],
          imageUrl: Array.isArray(resultUrls) ? resultUrls[0] : resultUrls,
          resultUrls: resultUrls, // 保留原始字段以便调试
          response: apiResponse.response, // 保留完整的response对象
        }
      }
    } else {
      // 视频任务返回视频数据
      if (apiResponse.videoInfo) {
        output = {
          videoUrl: apiResponse.videoInfo.videoUrl,
          imageUrl: apiResponse.videoInfo.imageUrl,
          videoId: apiResponse.videoInfo.videoId,
          duration: apiResponse.videoInfo.duration,
          quality: apiResponse.videoInfo.quality,
          aspectRatio: apiResponse.videoInfo.aspectRatio,
          ...apiResponse.videoInfo,
        }
      }
    }

    return {
      taskId: apiResponse.taskId,
      taskType: taskType,
      status: {
        status,
        message: apiResponse.failMsg || apiResponse.error?.message || undefined,
      },
      input: inputData
        ? {
            imageUrl: inputData.imageUrl || inputData.filesUrl,
            prompt: inputData.prompt,
            styleId: inputData.styleId,
            customPrompt: inputData.customPrompt,
            aspectRatio: inputData.aspectRatio,
            ...inputData,
          }
        : {},
      output,
      metadata: {
        provider: 'kieai',
        createdAt: apiResponse.generateTime,
        expireFlag: apiResponse.expireFlag,
      },
      error:
        apiResponse.state === 'fail' || apiResponse.error
          ? {
              code: apiResponse.failCode,
              message:
                apiResponse.failMsg ||
                apiResponse.error?.message ||
                'Unknown error',
            }
          : undefined,
    }
  }

  validateInput(
    input: Record<string, any>,
    taskType: TaskType
  ): { isValid: boolean; error?: string } {
    const baseValidation = super.validateInput(input, taskType)
    if (!baseValidation.isValid) {
      return baseValidation
    }

    // KieAI 特定验证
    switch (taskType) {
      case 'aismile':
      case 'image-extender':
        if (!input.imageUrl && !input.filesUrl) {
          return { isValid: false, error: 'requires imageUrl' }
        }
        break

      case 'imagetovideo':
        if (!input.imageUrl || !input.prompt) {
          return {
            isValid: false,
            error: 'Image to video requires both imageUrl and prompt',
          }
        }
        // 验证时长和质量兼容性
        if (input.duration === 8 && input.quality === '1080p') {
          return {
            isValid: false,
            error:
              'Cannot generate 8-second video with 1080p quality. Please use 720p for 8-second videos or 5-second duration for 1080p.',
          }
        }
        break

      case 'photo-to-anime':
        if (!input.imageUrl && !input.filesUrl) {
          return {
            isValid: false,
            error: 'Photo to anime requires imageUrl or filesUrl',
          }
        }
        if (!input.styleId) {
          return { isValid: false, error: 'Photo to anime requires styleId' }
        }
        break

      default:
        return {
          isValid: false,
          error: `Unsupported task type for KieAI: ${taskType}`,
        }
    }

    return { isValid: true }
  }

  // 从响应推断任务类型（用于状态查询）
  private inferTaskType(apiResponse: any): TaskType {
    // 基于响应数据的特征推断任务类型
    const inputInfo = apiResponse.inputInfo || apiResponse.generateParam

    // 检查是否是photo-to-anime（有styleId特征）
    if (inputInfo?.styleId) {
      return 'photo-to-anime'
    }

    // 检查是否是aismile（有imageUrl但没有prompt）
    if (inputInfo?.imageUrl && !inputInfo?.prompt) {
      return 'aismile'
    }

    // 检查是否是imagetovideo（有imageUrl和prompt）
    if (inputInfo?.imageUrl && inputInfo?.prompt) {
      return 'imagetovideo'
    }

    // 默认返回 imagetovideo
    return 'imagetovideo'
  }
}
