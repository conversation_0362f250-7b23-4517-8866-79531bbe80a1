import { useState, useEffect, useCallback, useRef } from 'react'

export interface UseGenerationProgressReturn {
  progress: number
  isActive: boolean
  startProgress: () => void
  stopProgress: () => void
  resetProgress: () => void
  setProgress: (progress: number) => void
}

/**
 * 管理生成进度的Hook
 * @param maxProgress 最大进度值，默认99%
 * @param duration 达到最大进度的时间（毫秒），默认5分钟
 * @returns 进度状态和控制方法
 */
export function useGenerationProgress(
  maxProgress: number = 99,
  duration: number = 300000 // 5分钟
): UseGenerationProgressReturn {
  const [progress, setProgressState] = useState(0)
  const [isActive, setIsActive] = useState(false)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const startTimeRef = useRef<number | null>(null)

  const startProgress = useCallback(() => {
    // 清除现有计时器
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }

    setProgressState(0)
    setIsActive(true)
    startTimeRef.current = Date.now()

    // 启动自适应进度计时器
    intervalRef.current = setInterval(() => {
      setProgressState((prev) => {
        // 如果已经到达最大进度，停止
        if (prev >= maxProgress) {
          return prev
        }

        // 计算已用时间和目标进度
        const currentTime = Date.now()
        const elapsedTime = currentTime - (startTimeRef.current || currentTime)
        const timeProgress = elapsedTime / duration // 时间进度 0-1
        const targetProgress = Math.min(timeProgress * maxProgress, maxProgress) // 基于时间的目标进度
        const remainingTime = duration - elapsedTime
        const remainingProgress = maxProgress - prev

        // 计算自适应增长速度
        let progressGap = targetProgress - prev

        // 如果落后于目标进度，需要加速追赶
        if (progressGap > 0) {
          // 根据落后程度调整增长速度
          const baseIncrement = Math.max(1, Math.ceil(progressGap / 3))
          const randomFactor = 0.5 + Math.random() * 0.5 // 0.5-1.0的随机因子
          const adaptiveIncrement = Math.ceil(baseIncrement * randomFactor)
          const finalIncrement = Math.min(
            adaptiveIncrement,
            10,
            remainingProgress
          )

          return Math.min(prev + finalIncrement, maxProgress)
        }

        // 如果接近或达到目标进度，保持自然的随机跳跃
        else if (progressGap >= -5) {
          // 80%概率小幅跳跃，20%概率停顿
          if (Math.random() < 0.8) {
            const smallIncrement = Math.floor(Math.random() * 3) + 1 // 1-3的小增量
            return Math.min(prev + smallIncrement, maxProgress)
          }
          return prev // 停顿
        }

        // 如果超前太多，只有30%概率小幅增长
        else {
          if (Math.random() < 0.3) {
            const tinyIncrement = Math.floor(Math.random() * 2) + 1 // 1-2的微小增量
            return Math.min(prev + tinyIncrement, maxProgress)
          }
          return prev // 大概率停顿，等待时间追上
        }
      })
    }, 1500) // 每1500ms检查一次，提高时间控制精度
  }, [maxProgress])

  const stopProgress = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
    setIsActive(false)
    startTimeRef.current = null
  }, [])

  const resetProgress = useCallback(() => {
    stopProgress()
    setProgressState(0)
    startTimeRef.current = null
  }, [stopProgress])

  const setProgress = useCallback((newProgress: number) => {
    setProgressState(Math.min(Math.max(newProgress, 0), 100))
  }, [])

  // 组件卸载时清理计时器
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [])

  return {
    progress,
    isActive,
    startProgress,
    stopProgress,
    resetProgress,
    setProgress,
  }
}
