// 通用类型定义

// API 提供商类型
export type ApiProvider = 'piapi' | 'kieai' | 'kling'

// 任务类型
export type TaskType =
  | 'face-swap'
  | 'ai_try_on'
  | 'ai_hug'
  | 'aismile'
  | 'imagetovideo'
  | 'photo-to-anime'
  | 'memory_video'
  | 'image-extender'

// 媒体类型
export type MediaType = 'image' | 'video'

// 任务状态 - 统一格式
export interface TaskStatus {
  status: 'pending' | 'processing' | 'success' | 'failed'
  progress?: number
  message?: string
}

// 通用任务详情接口
export interface UnifiedTaskDetail {
  taskId: string
  taskType: TaskType
  status: TaskStatus
  input: {
    imageUrl?: string
    targetImage?: string
    prompt?: string
    duration?: number
    quality?: string
    aspectRatio?: string
    [key: string]: any
  }
  output?: {
    imageUrl?: string
    videoUrl?: string
    videoId?: string
    [key: string]: any
  }
  metadata?: {
    provider: ApiProvider
    model?: string
    points?: number
    createdAt?: string
    completedAt?: string
    [key: string]: any
  }
  error?: {
    code?: string | number
    message: string
  }
}

// 生成请求参数接口
export interface GenerationRequest {
  taskType: TaskType
  input: Record<string, any>
  options?: {
    skipValidation?: boolean
    timeout?: number
  }
}

// 生成响应接口
export interface GenerationResponse {
  success: boolean
  taskId?: string
  message?: string
  error?: string
  data?: any
}

// 轮询配置
export interface PollingConfig {
  interval: number // 轮询间隔(ms)
  timeout: number // 超时时间(ms)
  maxRetries?: number // 最大重试次数
}

// 字段映射配置 - 用于兼容不同API的字段差异
export interface FieldMapping {
  // 输入字段映射
  input: {
    firstImage?: string
    secondImage?: string
    prompt?: string
    imageUrl?: string
    [key: string]: string | undefined
  }
  // 输出字段映射
  output: {
    imageUrl?: string
    videoUrl?: string
    [key: string]: string | undefined
  }
  // UI标签映射
  labels: {
    firstImage?: string
    secondImage?: string
    result?: string
    [key: string]: string | undefined
  }
}

// 任务配置接口
export interface TaskConfig {
  taskType: TaskType
  apiProvider: ApiProvider
  mediaType: MediaType
  fieldMapping: FieldMapping
  pollingConfig: PollingConfig
  validation?: {
    required: string[]
    optional?: string[]
  }
  defaultValues?: Record<string, any>
}

// 历史记录项接口 (统一格式)
export interface UnifiedHistoryItem {
  id: string
  userId: string
  taskId: string
  taskType: TaskType
  status: 'SUCCESS' | 'FAILED' | 'PROCESSING' | 'PENDING'
  inputParams: Record<string, any>
  resultData?: Record<string, any>
  metadata?: Record<string, any>
  errorMessage?: string
  createdAt: string
  updatedAt?: string
  completedAt?: string
}

// API 适配器接口
export interface ApiAdapter {
  readonly provider: ApiProvider

  // 生成任务
  generate(request: GenerationRequest): Promise<GenerationResponse>

  // 查询任务状态
  getTaskStatus(
    taskId: string,
    signal?: AbortSignal,
    taskType?: TaskType
  ): Promise<UnifiedTaskDetail>

  // 格式化为统一格式
  formatToUnified(apiResponse: any, taskType: TaskType): UnifiedTaskDetail

  // 验证输入参数
  validateInput(
    input: Record<string, any>,
    taskType: TaskType
  ): { isValid: boolean; error?: string }
}
