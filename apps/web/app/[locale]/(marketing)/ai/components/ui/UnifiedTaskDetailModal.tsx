'use client'

import React, { useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alog<PERSON>eader,
  <PERSON>alog<PERSON><PERSON>le,
  DialogFooter,
} from '@ui/components/dialog'
import { Button } from '@ui/components/button'
import { Badge } from '@ui/components/badge'
import { Card, CardContent } from '@ui/components/card'
import { Skeleton } from '@ui/components/skeleton'
import {
  Download,
  RefreshCw,
  Share2,
  ZoomIn,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Play,
  ArrowRight,
} from 'lucide-react'
import toast from 'react-hot-toast'
import { UnifiedHistoryItem, TaskType } from '../types'
import { getTaskConfig } from '../config'

interface UnifiedTaskDetailModalProps {
  isOpen: boolean
  onClose: () => void
  task: UnifiedHistoryItem | null
  onRegenerate?: (input: Record<string, any>) => void
}

interface MediaPreviewProps {
  src: string
  alt: string
  title: string
  type: 'image' | 'video'
  onZoomIn: (src: string) => void
  className?: string
}

// 可复用的媒体预览组件
const MediaPreview: React.FC<MediaPreviewProps> = ({
  src,
  alt,
  title,
  type,
  onZoomIn,
  className = '',
}) => {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)

  const handleLoad = () => {
    setIsLoading(false)
    setHasError(false)
  }

  const handleError = () => {
    setIsLoading(false)
    setHasError(true)
  }

  return (
    <div className={`space-y-2 ${className}`}>
      <h4 className="text-sm font-medium text-gray-700">{title}</h4>
      <Card className="overflow-hidden">
        <CardContent className="p-0 relative aspect-video">
          {isLoading && <Skeleton className="absolute inset-0 w-full h-full" />}

          {hasError ? (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
              <div className="text-center">
                <XCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-500">Failed to load {type}</p>
              </div>
            </div>
          ) : (
            <>
              {type === 'image' ? (
                <img
                  src={src}
                  alt={alt}
                  className="w-full h-full object-contain"
                  onLoad={handleLoad}
                  onError={handleError}
                />
              ) : (
                <video
                  src={src}
                  className="w-full h-full object-contain"
                  controls
                  onLoadedData={handleLoad}
                  onError={handleError}
                >
                  Your browser does not support the video tag.
                </video>
              )}

              {!isLoading && (
                <button
                  onClick={() => onZoomIn(src)}
                  className="absolute inset-0 bg-black/0 hover:bg-black/20 transition-colors duration-200 flex items-center justify-center opacity-0 hover:opacity-100"
                >
                  <div className="bg-white/90 rounded-full p-2">
                    {type === 'image' ? (
                      <ZoomIn className="h-4 w-4 text-gray-700" />
                    ) : (
                      <Play className="h-4 w-4 text-gray-700" />
                    )}
                  </div>
                </button>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

// 状态徽章组件
const StatusBadge: React.FC<{ status: UnifiedHistoryItem['status'] }> = ({
  status,
}) => {
  const statusConfig = {
    SUCCESS: {
      label: 'Completed',
      badgeStatus: 'success' as const,
      icon: CheckCircle,
    },
    FAILED: {
      label: 'Failed',
      badgeStatus: 'error' as const,
      icon: XCircle,
    },
    PROCESSING: {
      label: 'Processing',
      badgeStatus: 'info' as const,
      icon: Clock,
    },
    PENDING: {
      label: 'Pending',
      badgeStatus: 'warning' as const,
      icon: AlertCircle,
    },
  }

  const config = statusConfig[status]
  const Icon = config.icon

  return (
    <Badge status={config.badgeStatus} className="text-xs flex">
      <Icon className="h-3 w-3 mr-1" />
      {config.label}
    </Badge>
  )
}

// 任务信息组件
const TaskInfo: React.FC<{ task: UnifiedHistoryItem }> = ({ task }) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    })
  }

  const getDuration = () => {
    if (!task.completedAt) return null
    const start = new Date(task.createdAt).getTime()
    const end = new Date(task.completedAt).getTime()
    const duration = Math.round((end - start) / 1000)
    return `${duration}s`
  }

  return (
    <Card>
      <CardContent className="p-4 space-y-3">
        <div className="flex items-center justify-between">
          <h4 className="font-medium">Task Details</h4>
          <StatusBadge status={task.status} />
        </div>

        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600">Task ID:</span>
            <span className="font-mono text-xs">{task.taskId}</span>
          </div>

          <div className="flex justify-between">
            <span className="text-gray-600">Type:</span>
            <span className="capitalize">
              {task.taskType.replace('_', ' ')}
            </span>
          </div>

          <div className="flex justify-between">
            <span className="text-gray-600">Created:</span>
            <span>{formatDate(task.createdAt)}</span>
          </div>

          {task.completedAt && (
            <div className="flex justify-between">
              <span className="text-gray-600">Completed:</span>
              <span>{formatDate(task.completedAt)}</span>
            </div>
          )}

          {getDuration() && (
            <div className="flex justify-between">
              <span className="text-gray-600">Duration:</span>
              <span>{getDuration()}</span>
            </div>
          )}
        </div>

        {task.status === 'FAILED' && task.errorMessage && (
          <div className="mt-3 p-3 bg-red-50 rounded-lg">
            <p className="text-sm text-red-600">Error: {task.errorMessage}</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export const UnifiedTaskDetailModal: React.FC<UnifiedTaskDetailModalProps> = ({
  isOpen,
  onClose,
  task,
  onRegenerate,
}) => {
  const [enlargedMedia, setEnlargedMedia] = useState<string | null>(null)

  if (!task) return null

  const config = getTaskConfig(task.taskType as TaskType)
  const fieldMapping = config.fieldMapping

  // 获取媒体URL的辅助函数
  const getMediaUrl = (
    mediaType: 'first' | 'second' | 'result'
  ): { url: string | null; type: 'image' | 'video' } => {
    let url: string | null = null

    switch (mediaType) {
      case 'first':
        url =
          task.inputParams?.[fieldMapping.input.firstImage || 'imageUrl'] ||
          task.inputParams?.[fieldMapping.input.imageUrl || 'imageUrl'] ||
          null
        break
      case 'second':
        url = fieldMapping.input.secondImage
          ? task.inputParams?.[fieldMapping.input.secondImage] || null
          : null
        break
      case 'result':
        url =
          task.resultData?.[fieldMapping.output.imageUrl || 'imageUrl'] ||
          task.resultData?.[fieldMapping.output.videoUrl || 'videoUrl'] ||
          null
        break
    }

    // 判断媒体类型
    const isVideo = config.mediaType === 'video' && mediaType === 'result'
    return { url, type: isVideo ? 'video' : 'image' }
  }

  // 获取媒体标签的辅助函数
  const getMediaLabel = (mediaType: 'first' | 'second' | 'result'): string => {
    switch (mediaType) {
      case 'first':
        return fieldMapping.labels?.firstImage || 'Input Image'
      case 'second':
        return fieldMapping.labels?.secondImage || 'Second Image'
      case 'result':
        return fieldMapping.labels?.result || `Generated ${config.mediaType}`
      default:
        return mediaType
    }
  }

  const firstMedia = getMediaUrl('first')
  const secondMedia = getMediaUrl('second')
  const resultMedia = getMediaUrl('result')

  const resultMediaList = Array.isArray(resultMedia.url)
    ? resultMedia.url
    : [resultMedia.url]

  return (
    <>
      {/* 主弹窗 */}
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto bg-white">
          <DialogHeader>
            <DialogTitle>{getMediaLabel('result')} Details</DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            {/* 媒体展示区域 */}
            <div className="flex items-center gap-4 overflow-x-auto">
              {/* 第一个媒体 */}
              {firstMedia.url && (
                <div className="w-48 flex-shrink-0">
                  <MediaPreview
                    src={firstMedia.url}
                    alt={getMediaLabel('first')}
                    title={getMediaLabel('first')}
                    type={firstMedia.type}
                    onZoomIn={setEnlargedMedia}
                  />
                </div>
              )}

              {/* 第二个媒体（如果存在） */}
              {secondMedia.url && (
                <div className="w-48 flex-shrink-0">
                  <MediaPreview
                    src={secondMedia.url}
                    alt={getMediaLabel('second')}
                    title={getMediaLabel('second')}
                    type={secondMedia.type}
                    onZoomIn={setEnlargedMedia}
                  />
                </div>
              )}

              {/* 箭头指示 */}
              {resultMedia.url && (
                <div className="flex-shrink-0">
                  <ArrowRight className="h-6 w-6 text-gray-400" />
                </div>
              )}

              {/* 结果媒体 */}
              {resultMediaList.map((item, index) => (
                <div className="w-48 flex-shrink-0" key={index}>
                  <MediaPreview
                    src={item}
                    alt={getMediaLabel('result')}
                    title={getMediaLabel('result')}
                    type={resultMedia.type}
                    onZoomIn={setEnlargedMedia}
                  />
                </div>
              ))}
            </div>

            {/* 任务信息 */}
            <TaskInfo task={task} />
          </div>
        </DialogContent>
      </Dialog>

      {/* 媒体放大查看弹窗 */}
      <Dialog
        open={!!enlargedMedia}
        onOpenChange={() => setEnlargedMedia(null)}
      >
        <DialogContent className="max-w-5xl max-h-[95vh] p-2 bg-white">
          <div className="relative">
            {enlargedMedia?.includes('.mp4') ||
            enlargedMedia?.includes('video') ? (
              <video
                src={enlargedMedia || ''}
                controls
                className="w-full h-auto max-h-[90vh] object-contain"
              >
                Your browser does not support the video tag.
              </video>
            ) : (
              <img
                src={enlargedMedia || ''}
                alt="Enlarged view"
                className="w-full h-auto max-h-[90vh] object-contain"
              />
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
