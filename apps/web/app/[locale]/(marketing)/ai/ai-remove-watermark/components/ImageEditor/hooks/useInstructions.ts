'use client'

import { useState, useEffect } from 'react'

const INSTRUCTIONS_STORAGE_KEY = 'magic-eraser-instructions-seen'

export const useInstructions = () => {
  const [showInstructions, setShowInstructions] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Just initialize, don't auto-show instructions
    setIsLoading(false)
  }, [])

  const hideInstructions = () => {
    setShowInstructions(false)
    localStorage.setItem(INSTRUCTIONS_STORAGE_KEY, 'true')
  }

  const showInstructionsAgain = () => {
    setShowInstructions(true)
  }

  const showInstructionsIfFirstTime = () => {
    const hasSeenInstructions = localStorage.getItem(INSTRUCTIONS_STORAGE_KEY)
    if (!hasSeenInstructions) {
      setShowInstructions(true)
    }
  }

  const resetInstructions = () => {
    localStorage.removeItem(INSTRUCTIONS_STORAGE_KEY)
    setShowInstructions(true)
  }

  return {
    showInstructions,
    hideInstructions,
    showInstructionsAgain,
    showInstructionsIfFirstTime,
    resetInstructions,
    isLoading,
  }
}

/**
 * Debug mode hook - checks if debug=1 is present in URL search params
 */
export const useDebugMode = () => {
  const [isDebug, setIsDebug] = useState(false)

  useEffect(() => {
    // Check URL search params for debug=1
    const urlParams = new URLSearchParams(window.location.search)
    const debugParam = urlParams.get('debug')
    setIsDebug(debugParam === '1')
  }, [])

  return isDebug
}
