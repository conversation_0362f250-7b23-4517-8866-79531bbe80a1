'use client'

import { useState, useCallback } from 'react'

export interface TransferImageData {
  file: File
  url: string
  name: string
  width: number
  height: number
  shouldShowProgress?: boolean // 是否应该显示进度条动画
}

// Global state for image transfer between pages
let globalImageTransferData: TransferImageData[] = []
let globalImageTransferCallbacks: ((images: TransferImageData[]) => void)[] = []

/**
 * Hook for transferring images between pages
 * Used in HeroSection to prepare images and in ImageEditor to receive them
 */
export const useImageTransfer = () => {
  const [transferImages, setTransferImages] = useState<TransferImageData[]>([])

  // Set images to transfer (used in HeroSection)
  const setImagesToTransfer = useCallback((images: TransferImageData[]) => {
    globalImageTransferData = images
    setTransferImages(images)

    // Notify all listeners
    globalImageTransferCallbacks.forEach((callback) => callback(images))
  }, [])

  // Get and consume transferred images (used in ImageEditor)
  const getTransferredImages = useCallback((): TransferImageData[] => {
    const images = [...globalImageTransferData]
    globalImageTransferData = [] // Clear after consumption
    return images
  }, [])

  // Subscribe to image transfer updates
  const subscribeToImageTransfer = useCallback(
    (callback: (images: TransferImageData[]) => void) => {
      globalImageTransferCallbacks.push(callback)

      // Return unsubscribe function
      return () => {
        const index = globalImageTransferCallbacks.indexOf(callback)
        if (index > -1) {
          globalImageTransferCallbacks.splice(index, 1)
        }
      }
    },
    []
  )

  // Check if there are images waiting to be transferred
  const hasTransferredImages = useCallback((): boolean => {
    return globalImageTransferData.length > 0
  }, [])

  // Helper function to convert File to TransferImageData
  const fileToTransferData = useCallback(
    (file: File): Promise<TransferImageData> => {
      return new Promise((resolve, reject) => {
        const url = URL.createObjectURL(file)
        const img = new Image()

        img.onload = () => {
          resolve({
            file,
            url,
            name: file.name,
            width: img.naturalWidth,
            height: img.naturalHeight,
          })
        }

        img.onerror = () => {
          URL.revokeObjectURL(url)
          reject(new Error(`Failed to load image: ${file.name}`))
        }

        img.src = url
      })
    },
    []
  )

  // Helper function to convert multiple files
  const filesToTransferData = useCallback(
    async (files: File[]): Promise<TransferImageData[]> => {
      const promises = files.map((file) => fileToTransferData(file))
      return Promise.all(promises)
    },
    [fileToTransferData]
  )

  // Helper function to load image from URL (for preset images)
  const urlToTransferData = useCallback(
    (imageUrl: string, name?: string): Promise<TransferImageData> => {
      return new Promise((resolve, reject) => {
        // First fetch the image to convert to File
        fetch(imageUrl)
          .then((response) => response.blob())
          .then((blob) => {
            const file = new File([blob], name || 'preset-image.jpg', {
              type: blob.type,
            })
            return fileToTransferData(file)
          })
          .then(resolve)
          .catch(reject)
      })
    },
    [fileToTransferData]
  )

  return {
    transferImages,
    setImagesToTransfer,
    getTransferredImages,
    subscribeToImageTransfer,
    hasTransferredImages,
    fileToTransferData,
    filesToTransferData,
    urlToTransferData,
  }
}
