'use client'

import React, { useState, useCallback } from 'react'
import { Minus, Plus } from 'lucide-react'

interface ZoomControllerProps {
  zoom: number
  onZoomChange: (zoom: number) => void
  minZoom?: number
  maxZoom?: number
  step?: number
  className?: string
}

export const ZoomController: React.FC<ZoomControllerProps> = ({
  zoom,
  onZoomChange,
  minZoom = 0.1,
  maxZoom = 3.0,
  step = 0.1,
  className = '',
}) => {
  const [isDragging, setIsDragging] = useState(false)

  // 计算滑块位置百分比
  const getSliderPercentage = useCallback(() => {
    return ((zoom - minZoom) / (maxZoom - minZoom)) * 100
  }, [zoom, minZoom, maxZoom])

  // 根据百分比计算缩放值
  const getZoomFromPercentage = useCallback(
    (percentage: number) => {
      const value = minZoom + (percentage / 100) * (maxZoom - minZoom)
      return Math.max(minZoom, Math.min(maxZoom, value))
    },
    [minZoom, maxZoom]
  )

  // 处理缩小
  const handleZoomOut = useCallback(() => {
    const newZoom = Math.max(minZoom, zoom - step)
    onZoomChange(newZoom)
  }, [zoom, minZoom, step, onZoomChange])

  // 处理放大
  const handleZoomIn = useCallback(() => {
    const newZoom = Math.min(maxZoom, zoom + step)
    onZoomChange(newZoom)
  }, [zoom, maxZoom, step, onZoomChange])

  // 处理适应窗口
  const handleFit = useCallback(() => {
    onZoomChange(1.0)
  }, [onZoomChange])

  // 处理滑块拖拽
  const handleSliderMouseDown = useCallback(
    (event: React.MouseEvent) => {
      setIsDragging(true)

      const slider = event.currentTarget as HTMLElement
      const rect = slider.getBoundingClientRect()
      const percentage = ((event.clientX - rect.left) / rect.width) * 100
      const newZoom = getZoomFromPercentage(
        Math.max(0, Math.min(100, percentage))
      )
      onZoomChange(newZoom)

      const handleMouseMove = (e: MouseEvent) => {
        const percentage = ((e.clientX - rect.left) / rect.width) * 100
        const newZoom = getZoomFromPercentage(
          Math.max(0, Math.min(100, percentage))
        )
        onZoomChange(newZoom)
      }

      const handleMouseUp = () => {
        setIsDragging(false)
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }

      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
    },
    [getZoomFromPercentage, onZoomChange]
  )

  const sliderPercentage = getSliderPercentage()

  return (
    <div
      className={`flex items-center space-x-3 bg-white rounded-lg shadow-sm border border-gray-200 px-4 py-2 ${className}`}
    >
      {/* 缩小按钮 */}
      <button
        onClick={handleZoomOut}
        disabled={zoom <= minZoom}
        className={`w-8 h-8 flex items-center justify-center rounded-md transition-all ${
          zoom <= minZoom
            ? 'text-gray-300 cursor-not-allowed'
            : 'text-gray-600 hover:bg-gray-100 hover:text-gray-800'
        }`}
        aria-label="Zoom Out"
        title="Zoom Out"
      >
        <Minus className="w-4 h-4" />
      </button>

      {/* 滑动条 */}
      <div className="flex-1 relative h-4 flex items-center min-w-[120px]">
        <div
          className="w-full h-1 bg-gray-200 rounded-full cursor-pointer relative"
          onMouseDown={handleSliderMouseDown}
        >
          {/* 滑动条轨道 */}
          <div
            className="h-full bg-black rounded-full transition-all"
            style={{ width: `${sliderPercentage}%` }}
          />

          {/* 滑块 */}
          <div
            className={`absolute top-1/2 transform -translate-y-1/2 -translate-x-1/2 w-4 h-4 bg-white border-2 border-black rounded-full cursor-grab transition-all ${
              isDragging ? 'cursor-grabbing scale-110' : 'hover:scale-105'
            }`}
            style={{ left: `${sliderPercentage}%` }}
          />
        </div>
      </div>

      {/* 放大按钮 */}
      <button
        onClick={handleZoomIn}
        disabled={zoom >= maxZoom}
        className={`w-8 h-8 flex items-center justify-center rounded-md transition-all ${
          zoom >= maxZoom
            ? 'text-gray-300 cursor-not-allowed'
            : 'text-gray-600 hover:bg-gray-100 hover:text-gray-800'
        }`}
        aria-label="Zoom In"
        title="Zoom In"
      >
        <Plus className="w-4 h-4" />
      </button>

      {/* Fit按钮 */}
      <button
        onClick={handleFit}
        disabled={Math.abs(zoom - 1.0) < 0.01}
        className={`px-3 py-1 text-sm font-medium rounded-md border transition-all ${
          Math.abs(zoom - 1.0) < 0.01
            ? 'text-gray-400 border-gray-200 cursor-not-allowed bg-gray-50'
            : 'text-gray-700 border-gray-300 hover:border-gray-400 hover:bg-gray-50'
        }`}
        title="Fit to window"
      >
        Fit
      </button>

      {/* 缩放比例显示 */}
      <div className="text-sm text-gray-600 font-mono min-w-[45px] text-right">
        {Math.round(zoom * 100)}%
      </div>
    </div>
  )
}

export default ZoomController
