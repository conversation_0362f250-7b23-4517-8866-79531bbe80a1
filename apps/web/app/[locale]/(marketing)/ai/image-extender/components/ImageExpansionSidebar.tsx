'use client'

import React, { useState, useRef, useCallback, useMemo } from 'react'
import { Button } from '@ui/components/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@ui/components/select'
import { RotateCcw } from 'lucide-react'
import { useToast } from '@ui/hooks/use-toast'
import { UploadImage } from '../../face-swap/components/UploadImage'
import { GenerateButton } from '../../face-swap/components/GenerateButton'
import {
  IMAGE_EXPANSION_CONFIG,
  buildExpansionPrompt,
  validateImageDimensions,
  getImageDimensions,
} from '../config'

// 图片扩图生成参数接口
export interface ImageExpansionGenerationParams {
  originalFile: File
  aspectRatio: string
  imageCount: number
  uploadedImageUrl: string
}

// 组件 props 接口
interface ImageExpansionSidebarProps {
  onGenerate: (params: ImageExpansionGenerationParams) => void
  onPreviewUpdate?: (imageUrl: string | null, aspectRatio: string) => void
  isGenerating?: boolean
  onReset?: () => void
  className?: string
}

// 比例视觉组件
const RatioVisual: React.FC<{ ratio: string; isSelected: boolean }> = ({
  ratio,
  isSelected,
}) => {
  // 计算长方形尺寸
  const getRectangleDimensions = (ratio: string) => {
    if (ratio === '1.2x') {
      // 1.2x用稍大的正方形，添加小点表示放大
      return { width: 20, height: 20, isScale: true }
    }

    const [w, h] = ratio.split(':').map(Number)
    const maxSize = 28 // 增大最大尺寸以便更好显示比例
    const minSize = 6 // 减小最小尺寸以便显示极端比例
    const aspectRatio = w / h

    let width: number
    let height: number
    if (aspectRatio > 1) {
      // 横向比例
      width = maxSize
      height = Math.round(maxSize / aspectRatio)
      height = Math.max(height, minSize)
    } else {
      // 纵向比例
      height = maxSize
      width = Math.round(maxSize * aspectRatio)
      width = Math.max(width, minSize)
    }

    return { width, height, isScale: false }
  }

  const { width, height, isScale } = getRectangleDimensions(ratio)

  return (
    <div className="relative">
      <div
        className={`border-2 rounded-[1px] ${
          isSelected
            ? 'border-blue-500 bg-blue-100'
            : 'border-gray-400 bg-gray-100'
        }`}
        style={{ width, height }}
      />
      {isScale && (
        <div
          className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-1 h-1 rounded-full ${
            isSelected ? 'bg-blue-600' : 'bg-gray-600'
          }`}
        />
      )}
    </div>
  )
}

export const ImageExpansionSidebar = React.forwardRef<
  { getOriginalFile: () => File | null },
  ImageExpansionSidebarProps
>(
  (
    {
      onGenerate,
      onPreviewUpdate,
      isGenerating = false,
      onReset,
      className = '',
    },
    ref
  ) => {
    const { toast } = useToast()

    // 内部状态管理
    const [originalFile, setOriginalFile] = useState<File | null>(null)
    const [imageUrl, setImageUrl] = useState<string | null>(null)
    const [uploadedOssUrl, setUploadedOssUrl] = useState<string | null>(null) // 缓存已上传的OSS URL
    const [isUploading, setIsUploading] = useState(false)
    const [uploadProgress, setUploadProgress] = useState(0)
    const [uploadError, setUploadError] = useState<string | null>(null)

    // 图片扩图特有状态
    const [aspectRatio, setAspectRatio] = useState<string>(
      IMAGE_EXPANSION_CONFIG.generation.defaults.aspectRatio
    )
    const [imageCount, setImageCount] = useState<number>(1)

    // 生成唯一文件名的函数
    const generateUniqueFileName = useCallback((blob: Blob): string => {
      const timestamp = Date.now()
      const randomStr = Math.random().toString(36).substring(2, 8)

      let extension = 'jpg'
      if (blob.type === 'image/png') {
        extension = 'png'
      } else if (blob.type === 'image/webp') {
        extension = 'webp'
      } else if (blob.type === 'image/jpeg') {
        extension = 'jpg'
      }

      return `image-extender-${timestamp}-${randomStr}.${extension}`
    }, [])

    // 上传图片到OSS的函数
    const uploadImageToOSS = useCallback(
      async (blob: Blob): Promise<string> => {
        try {
          setIsUploading(true)
          setUploadError(null)

          const fileName = generateUniqueFileName(blob)

          const formData = new FormData()
          formData.append('file', blob, fileName)

          const response = await fetch('/api/upload', {
            method: 'POST',
            body: formData,
          })

          if (!response.ok) {
            throw new Error(`Upload failed: ${response.status}`)
          }

          const result = await response.json()

          if (result.code !== 200) {
            throw new Error(result.msg || 'Upload failed')
          }

          return result.data.url
        } catch (error) {
          console.error('Image upload failed:', error)
          const errorMessage =
            error instanceof Error ? error.message : 'Upload failed'
          setUploadError(errorMessage)
          toast({
            title: 'Upload Failed',
            description: errorMessage,
            variant: 'error',
          })
          throw error
        } finally {
          setIsUploading(false)
          setUploadProgress(0)
        }
      },
      [toast, generateUniqueFileName]
    )

    // 处理图片选择 - 适配 UploadImage 接口
    const handleImageUpload = useCallback(
      async (file: File, url?: string) => {
        try {
          setUploadError(null)
          // 清除之前的OSS URL缓存，因为选择了新文件
          setUploadedOssUrl(null)

          // 验证图片尺寸
          const dimensions = await getImageDimensions(file)
          const validation = validateImageDimensions(
            dimensions.width,
            dimensions.height
          )

          if (!validation.isValid) {
            setUploadError(validation.error || '图片尺寸不符合要求')
            toast({
              title: '图片尺寸错误',
              description: validation.error,
              variant: 'error',
            })
            return
          }

          setOriginalFile(file)

          if (url) {
            setImageUrl(url)
          }
        } catch (error) {
          const errorMessage =
            error instanceof Error ? error.message : '图片加载失败'
          setUploadError(errorMessage)
          toast({
            title: '图片加载失败',
            description: errorMessage,
            variant: 'error',
          })
        }
      },
      [toast]
    )

    // 处理图片移除
    const handleImageRemove = useCallback(() => {
      setOriginalFile(null)
      setUploadError(null)
      setUploadedOssUrl(null) // 清除OSS URL缓存
      if (imageUrl) {
        URL.revokeObjectURL(imageUrl)
        setImageUrl(null)
      }
    }, [imageUrl])

    // 暴露给外部的 getOriginalFile 方法
    React.useImperativeHandle(
      ref,
      () => ({
        getOriginalFile: () => originalFile,
      }),
      [originalFile]
    )

    // 检查是否可以生成
    const canGenerate =
      originalFile && !isGenerating && !isUploading && !uploadError

    // 处理生成按钮点击
    const handleGenerate = useCallback(async () => {
      if (!canGenerate) {
        toast({
          title: 'Cannot generate',
          description: 'Please ensure image is uploaded',
          variant: 'error',
        })
        return
      }

      if (!originalFile) {
        toast({
          title: 'Cannot generate',
          description: 'No image file available',
          variant: 'error',
        })
        return
      }

      try {
        let ossUrl = uploadedOssUrl

        // 如果没有缓存的OSS URL，则重新上传
        if (!ossUrl) {
          // 直接使用原始文件，转换为 Blob
          const blob = new Blob([originalFile], { type: originalFile.type })

          // 上传到OSS
          ossUrl = await uploadImageToOSS(blob)

          // 缓存OSS URL
          setUploadedOssUrl(ossUrl)
        }

        // 构建生成参数
        const params = getGenerationParams(ossUrl)
        console.log('params', params)
        if (params) {
          onGenerate(params)
        }
      } catch (error) {
        console.error('Generation failed:', error)
      }
    }, [
      canGenerate,
      uploadImageToOSS,
      onGenerate,
      toast,
      originalFile,
      uploadedOssUrl,
    ])

    // 构建生成参数 - 移除裁剪相关逻辑
    function getGenerationParams(
      ossUrl: string
    ): ImageExpansionGenerationParams | null {
      if (!originalFile || !ossUrl) return null
      return {
        originalFile,
        aspectRatio,
        imageCount,
        uploadedImageUrl: ossUrl,
      }
    }

    // 重置所有设置
    const handleReset = useCallback(() => {
      setOriginalFile(null)
      setAspectRatio(IMAGE_EXPANSION_CONFIG.generation.defaults.aspectRatio)
      setImageCount(1)
      setUploadError(null)
      setUploadedOssUrl(null) // 清除OSS URL缓存
      if (imageUrl) {
        URL.revokeObjectURL(imageUrl)
        setImageUrl(null)
      }
      onReset?.()
    }, [imageUrl, onReset])

    // 预览参数变化时通知父组件
    React.useEffect(() => {
      if (onPreviewUpdate) {
        onPreviewUpdate(imageUrl, aspectRatio)
      }
    }, [imageUrl, aspectRatio, onPreviewUpdate])

    return (
      <>
        {/* 标题栏 */}
        <div className="p-4 border-b border-gray-200 flex-shrink-0">
          <div className="flex items-center justify-between">
            <h2 className="text-md font-semibold text-gray-900">
              AI Image Expansion
            </h2>
            <Button
              variant="outline"
              size="sm"
              onClick={handleReset}
              disabled={isGenerating}
              className="flex items-center justify-center gap-2 bg-[#4B6BFB] hover:bg-[#4B6BFB]/80 px-2 py-1 text-white font-semibold text-sm rounded-md transition-all duration-200"
            >
              <RotateCcw className="w-4 h-4" />
              <span>Reset All</span>
            </Button>
          </div>
        </div>

        <div className="flex-1 overflow-y-auto min-h-0">
          {/* 内容区域 */}
          <div className="p-4 space-y-4">
            {/* 图片上传区域 */}
            <div>
              <label className="text-sm font-medium text-gray-900 mb-2 block">
                Upload Image
              </label>
              <UploadImage
                title="Upload Image"
                imageUrl={imageUrl}
                isUploading={isUploading}
                progress={uploadProgress}
                onUpload={handleImageUpload}
                onRemove={handleImageRemove}
                className="h-28"
                featureType="face-swap"
                acceptedTypes={IMAGE_EXPANSION_CONFIG.upload.allowedTypes}
                maxSizeInMB={
                  IMAGE_EXPANSION_CONFIG.upload.maxFileSize / (1024 * 1024)
                }
              />
            </div>

            {/* 分割线 */}
            <hr className="border-gray-200 my-4" />

            {/* Ratio区域 */}
            <div className="space-y-3">
              <label className="text-sm font-medium text-gray-900">
                Target Ratio
              </label>
              <div className="grid grid-cols-3 gap-3">
                {IMAGE_EXPANSION_CONFIG.generation.aspectRatios.map((ratio) => (
                  <button
                    key={ratio.value}
                    onClick={() => setAspectRatio(ratio.value)}
                    className={`h-16 p-3 text-xs font-medium border rounded-lg transition-colors flex flex-col items-center justify-center gap-2 ${
                      aspectRatio === ratio.value
                        ? 'border-blue-500 bg-blue-50 text-blue-700'
                        : 'border-gray-300 text-gray-700 hover:border-gray-400'
                    }`}
                    title={ratio.description}
                  >
                    <RatioVisual
                      ratio={ratio.value}
                      isSelected={aspectRatio === ratio.value}
                    />
                    <span className="text-xs whitespace-nowrap">
                      {ratio.label}
                    </span>
                  </button>
                ))}
              </div>
            </div>

            {/* 分割线 */}
            <hr className="border-gray-200 my-4" />

            {/* 图片数量区域 */}
            <div className="space-y-3">
              <label className="text-sm font-medium text-gray-900">
                Number of Images
              </label>
              <Select
                value={imageCount.toString()}
                onValueChange={(value) => setImageCount(parseInt(value))}
                disabled={isGenerating}
              >
                <SelectTrigger className="w-full h-10">
                  <SelectValue placeholder="Select number of images" />
                </SelectTrigger>
                <SelectContent>
                  {IMAGE_EXPANSION_CONFIG.generation.variants.map((variant) => (
                    <SelectItem key={variant.value} value={variant.value}>
                      {variant.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* 固定生成按钮 */}
        <div className="flex-shrink-0 border-t border-gray-200">
          <GenerateButton
            swapImageUrl={imageUrl}
            requireTwoImages={false}
            isGenerating={isGenerating || isUploading}
            onGenerate={handleGenerate}
            generateError={uploadError}
            pointParams={{
              nVariants: imageCount,
            }}
          />
        </div>
      </>
    )
  }
)

ImageExpansionSidebar.displayName = 'ImageExpansionSidebar'

export default ImageExpansionSidebar
