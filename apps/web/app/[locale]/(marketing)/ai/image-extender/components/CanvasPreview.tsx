'use client'

import React, { useRef, useEffect, useState, useCallback, useMemo } from 'react'
import { calculateTargetDimensions } from '../config'

interface CanvasPreviewProps {
  imageUrl: string | null
  aspectRatio: string
  zoom: number
  onZoomChange?: (zoom: number) => void
  className?: string
}

export const CanvasPreview: React.FC<CanvasPreviewProps> = ({
  imageUrl,
  aspectRatio,
  zoom = 1,
  onZoomChange,
  className = '',
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  const [originalDimensions, setOriginalDimensions] = useState<{
    width: number
    height: number
  } | null>(null)

  const [canvasSize, setCanvasSize] = useState({ width: 800, height: 600 })

  // 获取原图的实际尺寸
  useEffect(() => {
    if (!imageUrl) {
      setOriginalDimensions(null)
      return
    }

    const img = new Image()
    img.onload = () => {
      setOriginalDimensions({
        width: img.naturalWidth,
        height: img.naturalHeight,
      })
    }
    img.src = imageUrl
  }, [imageUrl])

  // 计算预览数据
  const previewData = useMemo(() => {
    if (!originalDimensions || !imageUrl) {
      return null
    }

    // 计算扩图后的目标尺寸
    const targetDimensions = calculateTargetDimensions(
      originalDimensions.width,
      originalDimensions.height,
      aspectRatio
    )

    // 容器最大尺寸
    const maxWidth = canvasSize.width
    const maxHeight = canvasSize.height

    // 计算适应容器的缩放比例
    const scaleToFit = Math.min(
      maxWidth / targetDimensions.width,
      maxHeight / targetDimensions.height,
      1
    )

    // 应用用户缩放
    const finalScale = scaleToFit * zoom

    // 画布上的目标尺寸
    const canvasTargetWidth = targetDimensions.width * finalScale
    const canvasTargetHeight = targetDimensions.height * finalScale

    // 画布上的原图尺寸
    const canvasOriginalWidth = originalDimensions.width * finalScale
    const canvasOriginalHeight = originalDimensions.height * finalScale

    // 原图在扩图区域中的居中位置
    const originalX = (canvasTargetWidth - canvasOriginalWidth) / 2
    const originalY = (canvasTargetHeight - canvasOriginalHeight) / 2

    return {
      targetDimensions,
      originalDimensions,
      canvasTargetWidth,
      canvasTargetHeight,
      canvasOriginalWidth,
      canvasOriginalHeight,
      originalX,
      originalY,
      scale: finalScale,
    }
  }, [originalDimensions, aspectRatio, imageUrl, zoom, canvasSize])

  // 绘制棋盘格背景
  const drawCheckerboard = useCallback(
    (
      ctx: CanvasRenderingContext2D,
      width: number,
      height: number,
      cellSize: number = 15
    ) => {
      // 先填充纯白色背景
      ctx.fillStyle = '#ffffff'
      ctx.fillRect(0, 0, width, height)

      // 绘制灰色方格
      ctx.fillStyle = '#cccccc'
      for (let x = 0; x < width; x += cellSize) {
        for (let y = 0; y < height; y += cellSize) {
          if ((Math.floor(x / cellSize) + Math.floor(y / cellSize)) % 2 === 0) {
            ctx.fillRect(x, y, cellSize, cellSize)
          }
        }
      }
    },
    []
  )

  // 绘制预览内容
  const drawPreview = useCallback(async () => {
    const canvas = canvasRef.current
    if (!canvas || !previewData) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // 设置画布尺寸
    canvas.width = canvasSize.width
    canvas.height = canvasSize.height

    // 清空画布
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // 计算居中位置
    const centerX = (canvas.width - previewData.canvasTargetWidth) / 2
    const centerY = (canvas.height - previewData.canvasTargetHeight) / 2

    // 绘制扩图区域的棋盘格背景
    ctx.save()
    ctx.translate(centerX, centerY)

    // 创建裁剪区域
    ctx.beginPath()
    ctx.rect(
      0,
      0,
      previewData.canvasTargetWidth,
      previewData.canvasTargetHeight
    )
    ctx.clip()

    // 绘制棋盘格
    drawCheckerboard(
      ctx,
      previewData.canvasTargetWidth,
      previewData.canvasTargetHeight
    )

    ctx.restore()

    // 绘制扩图区域边框
    ctx.strokeStyle = '#d1d5db'
    ctx.lineWidth = 2
    ctx.strokeRect(
      centerX,
      centerY,
      previewData.canvasTargetWidth,
      previewData.canvasTargetHeight
    )

    // 加载并绘制原图
    if (imageUrl) {
      try {
        const img = new Image()
        img.crossOrigin = 'anonymous'

        await new Promise((resolve, reject) => {
          img.onload = resolve
          img.onerror = reject
          img.src = imageUrl
        })

        // 绘制原图
        const scaleFactor = 0.95
        const scaledWidth = previewData.canvasOriginalWidth * scaleFactor
        const scaledHeight = previewData.canvasOriginalHeight * scaleFactor

        // 计算居中偏移量
        const offsetX = (previewData.canvasOriginalWidth - scaledWidth) / 2
        const offsetY = (previewData.canvasOriginalHeight - scaledHeight) / 2

        const imgX = centerX + previewData.originalX + offsetX
        const imgY = centerY + previewData.originalY + offsetY

        ctx.drawImage(img, imgX, imgY, scaledWidth, scaledHeight)

        // 绘制原图边框
        ctx.strokeStyle = '#3b82f6'
        ctx.lineWidth = 2
        ctx.strokeRect(imgX, imgY, scaledWidth, scaledHeight)
      } catch (error) {
        console.error('Failed to load image:', error)
      }
    }
  }, [previewData, imageUrl, canvasSize, drawCheckerboard])

  // 重新绘制画布
  useEffect(() => {
    drawPreview()
  }, [drawPreview])

  // 监听容器尺寸变化
  useEffect(() => {
    const updateCanvasSize = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect()
        setCanvasSize({
          width: Math.max(400, rect.width - 32), // 减去padding
          height: Math.max(300, rect.height - 100), // 减去其他UI元素的高度
        })
      }
    }

    updateCanvasSize()

    const resizeObserver = new ResizeObserver(updateCanvasSize)
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current)
    }

    return () => {
      resizeObserver.disconnect()
    }
  }, [])

  return (
    <div
      ref={containerRef}
      className={`w-full h-full flex flex-col items-center justify-center ${className}`}
    >
      {!imageUrl ? (
        <div className="text-center space-y-4">
          <div className="w-16 h-16 mx-auto bg-gray-100 rounded-lg flex items-center justify-center">
            <span className="text-2xl">🖼️</span>
          </div>
          <div className="space-y-2">
            <h3 className="text-lg font-medium text-gray-900">
              Canvas Preview
            </h3>
            <p className="text-sm text-gray-600">
              Upload an image to see the expansion preview on canvas
            </p>
          </div>
        </div>
      ) : (
        <div className="w-full h-full flex flex-col">
          {/* 画布容器 */}
          <div className="flex-1 flex items-center justify-center p-8">
            <canvas
              ref={canvasRef}
              style={{
                maxWidth: '100%',
                maxHeight: '100%',
              }}
            />
          </div>
        </div>
      )}
    </div>
  )
}

export default CanvasPreview
