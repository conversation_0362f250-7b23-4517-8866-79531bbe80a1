'use client'

import React, { useState, useCallback } from 'react'
import { <PERSON><PERSON>, <PERSON>bsList, TabsTrigger, TabsContent } from '@ui/components/tabs'
import { useAuth } from '../../../../../modules/ui/hooks/use-auth'
import { HistoryIcon, RotateCcw, Sparkles, Eye } from 'lucide-react'

// 导入统一组件和hooks
import {
  useUnifiedGeneration,
  useUnifiedHistory,
  UnifiedHistoryTab,
  TaskType,
  GenerationProgress,
  useGenerationProgress,
} from '../components'

// 导入重构后的图片扩图组件
import {
  ImageExpansionSidebar,
  type ImageExpansionGenerationParams,
} from './components/ImageExpansionSidebar'
import ExpansionPreview from './components/ExpansionPreview'
import CanvasPreview from './components/CanvasPreview'
import ZoomController from './components/ZoomController'
import {
  buildApiRequestData,
  downloadFile,
  downloadMultipleFiles,
} from './lib/expansion-utils'

// 使用 image-extender 作为任务类型
const TASK_TYPE: TaskType = 'image-extender'

export default function ImageExpansionPage() {
  const [activeTab, setActiveTab] = useState('preview')

  // 预览状态
  const [previewImageUrl, setPreviewImageUrl] = useState<string | null>(null)
  const [previewAspectRatio, setPreviewAspectRatio] = useState<string>('1.2x')

  // Canvas预览专用状态
  const [canvasZoom, setCanvasZoom] = useState<number>(1.0)

  // 使用进度管理hook
  const {
    progress: generationProgress,
    startProgress,
    stopProgress,
    resetProgress,
    setProgress: setGenerationProgress,
  } = useGenerationProgress(99, 600000)

  const { isLoggedIn } = useAuth()

  // 使用统一的生成hook
  const {
    isGenerating,
    error: generateError,
    taskDetail,
    isPolling,
    pollingError,
    generate,
    reset: resetGeneration,
    stopPolling,
  } = useUnifiedGeneration(TASK_TYPE)

  // 使用统一的历史记录hook
  const {
    items: historyItems,
    isLoading: isLoadingHistory,
    error: historyError,
    refreshHistory,
  } = useUnifiedHistory(TASK_TYPE)

  // 新的生成函数，处理从侧边栏传来的参数
  const handleGenerateFromSidebar = useCallback(
    async (params: ImageExpansionGenerationParams) => {
      try {
        // 切换到 creations tab
        setActiveTab('creations')

        // 开始进度计时器
        startProgress()
        console.log('Image expansion generation params:', params)

        // 构建API请求数据
        const apiData = buildApiRequestData(
          params.uploadedImageUrl,
          params.aspectRatio,
          params.imageCount
        )

        // 使用统一的生成接口
        await generate(apiData)
      } catch (error) {
        console.error('Failed to generate image expansion:', error)
        // 生成失败时重置进度
        resetProgress()
      }
    },
    [generate, startProgress, resetProgress, setActiveTab]
  )

  // 处理侧边栏参数变化（用于实时预览）
  const handlePreviewUpdate = useCallback(
    (imageUrl: string | null, aspectRatio: string) => {
      setPreviewImageUrl(imageUrl)
      setPreviewAspectRatio(aspectRatio)
      // 当图片上传后，自动切换到预览tab
      if (imageUrl && activeTab !== 'preview') {
        setActiveTab('preview')
      }
    },
    []
  )

  const handleReset = useCallback(() => {
    resetGeneration()
    stopPolling()
    // 重置进度状态
    resetProgress()
    // 清理预览状态
    setPreviewImageUrl(null)
    setPreviewAspectRatio('1.2x')
    // 重置canvas缩放
    setCanvasZoom(1.0)
  }, [resetGeneration, stopPolling, resetProgress])

  const handleRegenerateFromHistory = useCallback(
    async (input: Record<string, any>) => {
      const imageUrl =
        input.imageUrl ||
        (input.originalImageUrls && input.originalImageUrls[0])

      if (!imageUrl) return

      try {
        // 开始进度计时器
        startProgress()

        // 构建API请求数据
        const apiData = buildApiRequestData(
          imageUrl,
          input.aspectRatio || '1:1',
          parseInt(input.nVariants) || 1
        )

        await generate(apiData)
      } catch (error) {
        console.error('Failed to regenerate image expansion:', error)
        // 生成失败时重置进度
        resetProgress()
      }
    },
    [generate, startProgress, resetProgress]
  )

  React.useEffect(() => {
    if (isLoggedIn && historyItems.length === 0) {
      refreshHistory()
    }
  }, [isLoggedIn, historyItems.length, refreshHistory])

  React.useEffect(() => {
    if (
      taskDetail &&
      (taskDetail.status.status === 'success' ||
        taskDetail.status.status === 'failed')
    ) {
      setTimeout(() => {
        refreshHistory()
      }, 1000)
    }
  }, [taskDetail?.status.status, refreshHistory])

  // 监听任务完成状态，更新进度到100%
  React.useEffect(() => {
    if (taskDetail?.status.status === 'success') {
      // 任务成功完成，将进度设置为100%并停止计时器
      setGenerationProgress(100)
      stopProgress()
    } else if (taskDetail?.status.status === 'failed') {
      // 任务失败，重置进度
      resetProgress()
    }
  }, [
    taskDetail?.status.status,
    setGenerationProgress,
    stopProgress,
    resetProgress,
  ])

  // 适配现有的结果显示组件
  const adaptedTaskDetail = taskDetail
    ? {
        taskId: taskDetail.taskId,
        state:
          taskDetail.status.status === 'success'
            ? 'success'
            : taskDetail.status.status === 'failed'
            ? 'fail'
            : taskDetail.status.status === 'processing'
            ? 'generating'
            : 'wait',
        inputInfo: taskDetail.input,
        imageInfo: taskDetail.output,
        error: taskDetail.error,
        expireFlag: false,
      }
    : null

  // 安全获取图片数组
  const getImages = () => {
    if (!adaptedTaskDetail?.imageInfo) return []

    // 调试日志：输出完整的imageInfo结构
    console.log('imageInfo structure:', adaptedTaskDetail.imageInfo)

    // KieAiAdapter已经处理了格式转换，优先使用统一的images字段
    if (
      adaptedTaskDetail.imageInfo.images &&
      Array.isArray(adaptedTaskDetail.imageInfo.images)
    ) {
      console.log(
        'Using adapter-processed images:',
        adaptedTaskDetail.imageInfo.images
      )
      return adaptedTaskDetail.imageInfo.images
    }

    // 兼容单张图片的情况
    if (adaptedTaskDetail.imageInfo.imageUrl) {
      console.log(
        'Using single imageUrl:',
        adaptedTaskDetail.imageInfo.imageUrl
      )
      return [adaptedTaskDetail.imageInfo.imageUrl]
    }

    console.log('No images found in expected format')
    return []
  }

  const images = getImages()

  return (
    <main className="h-screen pt-[68px] bg-gray-50">
      <div className="h-full flex bg-white border-t border-gray-200">
        <div className="w-[480px] border-r border-gray-200 bg-white flex flex-col">
          {/* 重构后的图片扩图侧边栏 */}
          <ImageExpansionSidebar
            onGenerate={handleGenerateFromSidebar}
            onPreviewUpdate={handlePreviewUpdate}
            isGenerating={isGenerating}
            onReset={handleReset}
            className="h-full"
          />
        </div>

        <div className="flex-1 flex flex-col bg-gray-50">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="h-full flex flex-col"
          >
            <div className="bg-white flex justify-center">
              <TabsList className="bg-transparent border-b border-gray-200 rounded-none">
                <TabsTrigger
                  value="preview"
                  className="px-8 py-4 gap-2 bg-transparent data-[state=active]:bg-transparent"
                >
                  <Eye className="h-4 w-4" />
                  <span>Preview</span>
                </TabsTrigger>
                <TabsTrigger
                  value="creations"
                  className="px-8 py-4 gap-2 bg-transparent data-[state=active]:bg-transparent"
                >
                  <Sparkles className="h-4 w-4" />
                  <span>Create Expansion</span>
                </TabsTrigger>

                <TabsTrigger
                  value="history"
                  className="px-8 py-4 gap-2 bg-transparent data-[state=active]:bg-transparent"
                >
                  <HistoryIcon className="h-4 w-4" />
                  <span>History</span>
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContent
              value="preview"
              className="flex-1 overflow-hidden mt-0 p-4"
            >
              <div className="flex flex-col flex-1 h-full">
                <div className="flex-1 overflow-hidden relative">
                  <CanvasPreview
                    imageUrl={previewImageUrl}
                    aspectRatio={previewAspectRatio}
                    zoom={canvasZoom}
                    onZoomChange={setCanvasZoom}
                    className="w-full h-full"
                  />
                </div>

                {/* 缩放控制器 */}
                {previewImageUrl && (
                  <div className="flex-shrink-0 p-4 bg-white border-t border-gray-200">
                    <div className="flex justify-center">
                      <ZoomController
                        zoom={canvasZoom}
                        onZoomChange={setCanvasZoom}
                        minZoom={0.1}
                        maxZoom={3.0}
                        step={0.1}
                      />
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent
              value="creations"
              className="flex-1 overflow-hidden p-4 mt-0"
            >
              <div className="h-full overflow-y-auto p-4 relative">
                <div className="min-h-full flex items-start justify-center">
                  <div className="w-full">
                    <div className="text-center mb-12">
                      <h1 className="text-4xl font-bold text-gray-900 mb-4">
                        AI Image Expansion
                      </h1>
                    </div>

                    <div className="flex justify-center">
                      <div className="w-full max-w-2xl">
                        {/* 使用统一的生成进度组件 */}
                        {isGenerating || isPolling ? (
                          <GenerationProgress
                            progress={generationProgress}
                            isGenerating={isGenerating || isPolling}
                            title="Expanding Image"
                            description="AI is expanding your image with seamless style matching..."
                            size="md"
                          />
                        ) : generateError || pollingError ? (
                          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 p-8">
                            <div className="text-center space-y-4">
                              <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                                <RotateCcw className="w-8 h-8 text-red-500" />
                              </div>
                              <div className="space-y-2">
                                <h3 className="text-lg font-semibold text-gray-900">
                                  Generation Failed
                                </h3>
                                <p className="text-sm text-red-600">
                                  {generateError || pollingError}
                                </p>
                              </div>
                              <button
                                onClick={() => {
                                  resetGeneration()
                                  stopPolling()
                                  resetProgress()
                                }}
                                className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200"
                              >
                                Try Again
                              </button>
                            </div>
                          </div>
                        ) : adaptedTaskDetail?.state === 'success' &&
                          images.length > 0 ? (
                          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 p-8">
                            <div className="space-y-6">
                              {/* 显示生成的结果 */}
                              <div
                                className={`grid gap-4 ${
                                  images.length === 1
                                    ? 'grid-cols-1 max-w-md mx-auto'
                                    : images.length === 2
                                    ? 'grid-cols-1 sm:grid-cols-2'
                                    : 'grid-cols-2'
                                }`}
                              >
                                {images.map((url: string, index: number) => (
                                  <div key={index} className="relative group">
                                    <div className="relative overflow-hidden rounded-lg shadow-md bg-gray-100">
                                      <img
                                        src={url}
                                        alt={`Expanded image ${index + 1}`}
                                        className="w-full h-auto object-contain transition-transform group-hover:scale-105"
                                      />

                                      {/* 覆盖层和下载按钮 */}
                                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-opacity">
                                        <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity">
                                          <button
                                            onClick={() =>
                                              downloadFile(
                                                url,
                                                `expanded-image-${Date.now()}-${
                                                  index + 1
                                                }`
                                              )
                                            }
                                            className="bg-white bg-opacity-90 hover:bg-opacity-100 text-gray-700 p-2 rounded-full shadow-lg transition-all hover:scale-110"
                                            title="Download image"
                                          >
                                            <svg
                                              className="w-5 h-5"
                                              fill="none"
                                              stroke="currentColor"
                                              viewBox="0 0 24 24"
                                            >
                                              <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                                              />
                                            </svg>
                                          </button>
                                        </div>
                                      </div>
                                    </div>

                                    <div className="mt-2 text-center">
                                      <p className="text-sm text-gray-600">
                                        Expanded Image #{index + 1}
                                      </p>
                                    </div>
                                  </div>
                                ))}
                              </div>

                              {/* 批量下载按钮 */}
                              {images.length > 1 && (
                                <div className="flex justify-center space-x-4">
                                  <button
                                    onClick={() => {
                                      downloadMultipleFiles(
                                        images,
                                        `expanded-image-${Date.now()}`
                                      )
                                    }}
                                    className="flex items-center space-x-2 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 hover:shadow-lg"
                                  >
                                    <svg
                                      className="w-5 h-5"
                                      fill="none"
                                      stroke="currentColor"
                                      viewBox="0 0 24 24"
                                    >
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                      />
                                    </svg>
                                    <span>Download All Images</span>
                                  </button>
                                </div>
                              )}
                            </div>
                          </div>
                        ) : (
                          // 默认状态：显示预览或空状态
                          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 p-8">
                            <ExpansionPreview
                              imageUrl={previewImageUrl}
                              aspectRatio={previewAspectRatio}
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent
              value="history"
              className="flex-1 overflow-hidden p-4 mt-0"
            >
              <div className="w-full h-full">
                {/* 使用新的统一历史记录组件 */}
                <UnifiedHistoryTab
                  taskType={TASK_TYPE}
                  onRegenerate={handleRegenerateFromHistory}
                />
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </main>
  )
}
