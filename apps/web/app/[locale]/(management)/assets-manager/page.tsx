/**
 * 素材管理主页面
 */

'use client'

import { useState } from 'react'
import { AssetsTable } from './components/AssetsTable'
import { SearchFilters } from './components/SearchFilters'
import { AssetModal } from './components/AssetModal'
import { BatchActions } from './components/BatchActions'
import { PasswordVerificationModal } from './components/PasswordVerificationModal'
import { AddAssetForm } from './components/AddAssetForm'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@ui/components/tabs'

import { useAssets } from './hooks/useAssets'
import { useAssetModal } from './hooks/useAssetModal'
import { usePasswordVerification } from './hooks/usePasswordVerification'
import { AssetRecord } from '../../../api/assets-manager/lib/types'

export default function AssetsManagerPage() {
  const [selectedAssets, setSelectedAssets] = useState<string[]>([])

  // 密码验证状态
  const {
    isVerified,
    isModalOpen,
    password,
    error,
    isLoading: isVerifying,
    setPassword,
    verifyPassword,
    resetVerification,
  } = usePasswordVerification()

  const {
    assets,
    loading,
    pagination,
    filters,
    setFilters,
    refreshAssets,
    deleteAssets,
    fetchAssetsWithParams,
  } = useAssets()

  const { isOpen, mode, asset, openEditModal, openViewModal, closeModal } =
    useAssetModal()

  // 处理表格行选择
  const handleRowSelection = (assetIds: string[]) => {
    setSelectedAssets(assetIds)
  }

  // 处理单个素材操作
  const handleAssetAction = async (
    action: 'view' | 'edit' | 'delete',
    asset: AssetRecord
  ) => {
    switch (action) {
      case 'view':
        openViewModal(asset)
        break
      case 'edit':
        openEditModal(asset)
        break
      case 'delete':
        // 删除逻辑现在在AssetsTable的DeleteConfirmDialog中处理
        await deleteAssets([asset.id])
        await refreshAssets()
        break
    }
  }

  // 处理批量删除
  const handleBatchDelete = async () => {
    if (selectedAssets.length === 0) return

    try {
      await deleteAssets(selectedAssets)
      setSelectedAssets([])
      await refreshAssets()
    } catch (error) {
      console.error('批量删除失败:', error)
    }
  }

  // 处理新增素材表单提交
  const handleAddAssetSubmit = async (data: any) => {
    try {
      console.log('=== page.tsx handleAddAssetSubmit 开始 ===')

      const endpoint = '/api/assets-manager/create'
      const method = 'POST'

      console.log('调用API:', { endpoint, method })
      console.log('提交数据:', data)

      const response = await fetch(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      console.log('API响应状态:', response.status)

      if (!response.ok) {
        const error = await response.json()
        console.error('API返回错误:', error)
        throw new Error(error.message || '操作失败')
      }

      const result = await response.json()
      console.log('API调用成功:', result)

      // 创建成功后不自动刷新列表，用户需要手动点击搜索
      // await refreshAssets()
      console.log('=== page.tsx handleAddAssetSubmit 完成 ===')
    } catch (error) {
      console.error('提交失败:', error)
      throw error // 让表单处理错误显示
    }
  }

  // 处理模态框提交（仅用于编辑）
  const handleModalSubmit = async (data: any) => {
    try {
      console.log('=== page.tsx handleModalSubmit 开始 ===')
      console.log('模式:', mode)
      console.log('asset ID:', asset?.id)

      const endpoint = `/api/assets-manager/update/${asset?.id}`
      const method = 'PUT'

      console.log('调用API:', { endpoint, method })
      console.log('提交数据:', data)

      const response = await fetch(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      console.log('API响应状态:', response.status)

      if (!response.ok) {
        const error = await response.json()
        console.error('API返回错误:', error)
        throw new Error(error.message || '操作失败')
      }

      const result = await response.json()
      console.log('API调用成功:', result)

      closeModal()
      // 编辑成功后不自动刷新列表，用户需要手动点击搜索
      // await refreshAssets()
      console.log('=== page.tsx handleModalSubmit 完成 ===')
    } catch (error) {
      console.error('提交失败:', error)
      throw error // 让模态框处理错误显示
    }
  }

  // 如果未验证，只显示密码验证弹窗
  if (!isVerified) {
    return (
      <div className="min-h-screen bg-gray-50 pt-24">
        <PasswordVerificationModal
          isOpen={isModalOpen}
          password={password}
          error={error}
          isLoading={isVerifying}
          onPasswordChange={setPassword}
          onVerify={verifyPassword}
        />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 pt-6 px-4 flex flex-col h-full">
      <div className="text-xl text-black font-bold mb-4">素材管理</div>

      <Tabs defaultValue="add" className="flex-1 flex flex-col">
        <TabsList className="w-fit mb-4">
          <TabsTrigger value="add">增加素材</TabsTrigger>
          <TabsTrigger value="list">素材列表</TabsTrigger>
        </TabsList>

        <TabsContent value="add" className="flex-1">
          <AddAssetForm onSubmit={handleAddAssetSubmit} />
        </TabsContent>

        <TabsContent value="list" className="flex-1 flex flex-col space-y-4">
          {/* 搜索过滤器 */}
          <SearchFilters
            filters={filters}
            onFiltersChange={setFilters}
            onSearch={refreshAssets}
            onReset={async () => {
              const defaultFilters = {
                page: 1,
                pageSize: 20,
                sortBy: 'created_at' as const,
                sortOrder: 'desc' as const,
              }
              setFilters(defaultFilters)
              await fetchAssetsWithParams(defaultFilters)
            }}
            onCreateAsset={() => {}} // 不再需要，因为现在在Tab中
            isLoading={loading}
          />

          {/* 批量操作工具栏 */}
          <BatchActions
            selectedCount={selectedAssets.length}
            onBatchDelete={handleBatchDelete}
            onClearSelection={() => setSelectedAssets([])}
            disabled={loading}
          />

          {/* 素材表格 */}
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm flex-1">
            <AssetsTable
              assets={assets}
              selectedAssets={selectedAssets}
              onAssetSelection={setSelectedAssets}
              onAssetAction={handleAssetAction}
              currentPage={pagination.page}
              pageSize={pagination.pageSize}
              total={pagination.total}
              totalPages={pagination.totalPages}
              onParamsChange={fetchAssetsWithParams}
              isLoading={loading}
            />
          </div>
        </TabsContent>
      </Tabs>

      {/* 素材弹窗（仅用于编辑和查看） */}
      <AssetModal
        isOpen={isOpen}
        mode={mode}
        asset={asset}
        onClose={closeModal}
        onSubmit={handleModalSubmit}
      />
    </div>
  )
}
