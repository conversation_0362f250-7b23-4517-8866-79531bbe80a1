/**
 * 资产模态框状态管理 Hook
 */

import { useState } from 'react'
import { AssetRecord } from '../../../../api/assets-manager/lib/types'

export type ModalMode = 'edit' | 'view'

interface UseAssetModalReturn {
  isOpen: boolean
  mode: ModalMode
  asset?: AssetRecord
  openEditModal: (asset: AssetRecord) => void
  openViewModal: (asset: AssetRecord) => void
  closeModal: () => void
}

export function useAssetModal(): UseAssetModalReturn {
  const [isOpen, setIsOpen] = useState(false)
  const [mode, setMode] = useState<ModalMode>('edit')
  const [asset, setAsset] = useState<AssetRecord | undefined>()

  const openEditModal = (asset: AssetRecord) => {
    setMode('edit')
    setAsset(asset)
    setIsOpen(true)
  }

  const openViewModal = (asset: AssetRecord) => {
    setMode('view')
    setAsset(asset)
    setIsOpen(true)
  }

  const closeModal = () => {
    setIsOpen(false)
    setAsset(undefined)
  }

  return {
    isOpen,
    mode,
    asset,
    openEditModal,
    openViewModal,
    closeModal,
  }
}
