/**
 * 素材数据管理 Hook
 */

import { useState, useCallback } from 'react'
import {
  AssetRecord,
  ListAssetsRequest,
  ListAssetsResponse,
} from '../../../../api/assets-manager/lib/types'

interface UseAssetsReturn {
  assets: AssetRecord[]
  loading: boolean
  error: string | null
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
  filters: ListAssetsRequest
  setFilters: (filters: ListAssetsRequest) => void
  refreshAssets: () => Promise<void>
  deleteAssets: (ids: string[]) => Promise<void>
  fetchAssetsWithParams: (params: ListAssetsRequest) => Promise<void>
}

// 默认筛选条件
const defaultFilters: ListAssetsRequest = {
  page: 1,
  pageSize: 20,
  sortBy: 'created_at',
  sortOrder: 'desc',
}

export function useAssets(): UseAssetsReturn {
  const [assets, setAssets] = useState<AssetRecord[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0,
  })
  const [filters, setFilters] = useState<ListAssetsRequest>(defaultFilters)

  // 获取素材列表
  const fetchAssets = useCallback(async (searchFilters: ListAssetsRequest) => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/assets-manager/list', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(searchFilters),
      })

      const result = await response.json()

      if (result.code === 200) {
        const data: ListAssetsResponse = result.data
        console.log('🔄 刷新素材数据:', {
          count: data.data.length,
          pagination: data.pagination,
          firstAsset: data.data[0]?.name || 'N/A',
        })
        setAssets(data.data)
        setPagination(data.pagination)
      } else {
        throw new Error(result.message || '查询失败')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '未知错误'
      setError(errorMessage)
      console.error('获取素材列表失败:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  // 刷新数据
  const refreshAssets = useCallback(async () => {
    console.log('🔄 开始刷新素材数据，当前筛选条件:', filters)
    await fetchAssets(filters)
  }, [fetchAssets, filters])

  // 删除素材
  const deleteAssets = useCallback(async (ids: string[]) => {
    if (ids.length === 0) return

    try {
      if (ids.length === 1) {
        // 单个删除
        const response = await fetch(`/api/assets-manager/delete/${ids[0]}`, {
          method: 'DELETE',
        })

        const result = await response.json()
        if (result.code !== 200) {
          throw new Error(result.message || '删除失败')
        }
      } else {
        // 批量删除
        const response = await fetch('/api/assets-manager/batch-delete', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ ids }),
        })

        const result = await response.json()
        if (result.code !== 200) {
          throw new Error(result.message || '批量删除失败')
        }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '删除失败'
      throw new Error(errorMessage)
    }
  }, [])

  // 移除自动刷新，只在手动调用时请求数据
  // useEffect(() => {
  //   fetchAssets(filters)
  // }, [fetchAssets, filters])

  const fetchAssetsWithParams = useCallback(
    async (params: ListAssetsRequest) => {
      setFilters({ ...filters, ...params })
      await fetchAssets(params)
    },
    [fetchAssets, filters]
  )

  return {
    assets,
    loading,
    error,
    pagination,
    filters,
    setFilters,
    refreshAssets,
    deleteAssets,
    fetchAssetsWithParams,
  }
}
