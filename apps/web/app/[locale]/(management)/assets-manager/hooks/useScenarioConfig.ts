/**
 * 场景配置管理 Hook
 */

import { useMemo } from 'react'
import {
  TaskType,
  taskTypePointConfig,
} from '@shared/lib/point/page-point-config'
import { Category, MediaType } from '../../../../api/assets-manager/lib/types'
import { getTaskTypePointConfigByTaskType } from '@shared/lib/point/point-config-service'

export interface ScenarioOption {
  taskType: TaskType
  name: string
  category: Category
  type: MediaType
  apiProvider: string
  pathname: string
}

interface GroupedScenarioOptions {
  text2image: ScenarioOption[]
  image2image: ScenarioOption[]
  text2video: ScenarioOption[]
  image2video: ScenarioOption[]
}

interface UseScenarioConfigReturn {
  scenarioOptions: ScenarioOption[]
  groupedOptions: GroupedScenarioOptions
  getCategoryByTaskType: (taskType: TaskType) => Category
  getMediaTypeByTaskType: (taskType: TaskType) => MediaType
  getFilteredOptions: (category?: Category) => ScenarioOption[]
}

// 根据 TaskType 获取业务分类（直接从配置中读取）
function getCategoryByTaskType(taskType: TaskType): Category {
  const config = getTaskTypePointConfigByTaskType(taskType)
  return config?.category || 'image2image'
}

// 根据 TaskType 推断媒体类型
function getMediaTypeByTaskType(taskType: TaskType): MediaType {
  const config = getTaskTypePointConfigByTaskType(taskType)
  return config?.mediaType === 'video' ? 'video' : 'image'
}

export function useScenarioConfig(): UseScenarioConfigReturn {
  // 生成场景选项
  const scenarioOptions = useMemo((): ScenarioOption[] => {
    return Object.entries(taskTypePointConfig).map(([key, config]) => ({
      taskType: config.taskType,
      name: config.description,
      category: getCategoryByTaskType(config.taskType),
      type: getMediaTypeByTaskType(config.taskType),
      apiProvider: config.apiProvider,
      pathname: config.pathname,
    }))
  }, [])

  // 按业务分类分组
  const groupedOptions = useMemo((): GroupedScenarioOptions => {
    return {
      text2image: scenarioOptions.filter(
        (opt) => opt.category === 'text2image'
      ),
      image2image: scenarioOptions.filter(
        (opt) => opt.category === 'image2image'
      ),
      text2video: scenarioOptions.filter(
        (opt) => opt.category === 'text2video'
      ),
      image2video: scenarioOptions.filter(
        (opt) => opt.category === 'image2video'
      ),
    }
  }, [scenarioOptions])

  // 根据分类过滤选项
  const getFilteredOptions = useMemo(() => {
    return (category?: Category): ScenarioOption[] => {
      if (!category) return scenarioOptions
      return scenarioOptions.filter((opt) => opt.category === category)
    }
  }, [scenarioOptions])

  return {
    scenarioOptions,
    groupedOptions,
    getCategoryByTaskType,
    getMediaTypeByTaskType,
    getFilteredOptions,
  }
}
