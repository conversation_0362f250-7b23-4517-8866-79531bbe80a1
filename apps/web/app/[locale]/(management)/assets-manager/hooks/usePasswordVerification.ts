/**
 * 密码验证 Hook
 * 管理素材管理页面的密码验证状态
 */

import { useState, useEffect } from 'react'

interface UsePasswordVerificationReturn {
  isVerified: boolean
  isModalOpen: boolean
  password: string
  error: string | null
  isLoading: boolean
  setPassword: (password: string) => void
  verifyPassword: () => Promise<void>
  resetVerification: () => void
}

// 默认密码，生产环境应该通过环境变量设置
const DEFAULT_PASSWORD = 'admin123'

export function usePasswordVerification(): UsePasswordVerificationReturn {
  const [isVerified, setIsVerified] = useState(false)
  const [isModalOpen, setIsModalOpen] = useState(true)
  const [password, setPassword] = useState('')
  const [error, setError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  // 从localStorage检查是否已经验证过
  useEffect(() => {
    const verified = localStorage.getItem('assets-manager-verified')
    if (verified === 'true') {
      setIsVerified(true)
      setIsModalOpen(false)
    }
  }, [])

  const verifyPassword = async () => {
    if (!password.trim()) {
      setError('请输入密码')
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      // 模拟API调用延迟
      await new Promise((resolve) => setTimeout(resolve, 500))

      // 获取环境变量中的密码，如果没有则使用默认密码
      const expectedPassword =
        process.env.NEXT_PUBLIC_ASSETS_MANAGER_PASSWORD || DEFAULT_PASSWORD

      if (password === expectedPassword) {
        setIsVerified(true)
        setIsModalOpen(false)
        // 保存验证状态到localStorage，有效期24小时
        localStorage.setItem('assets-manager-verified', 'true')
        localStorage.setItem(
          'assets-manager-verified-time',
          Date.now().toString()
        )
      } else {
        setError('密码错误，请重试')
        setPassword('')
      }
    } catch (error) {
      setError('验证失败，请重试')
    } finally {
      setIsLoading(false)
    }
  }

  const resetVerification = () => {
    setIsVerified(false)
    setIsModalOpen(true)
    setPassword('')
    setError(null)
    localStorage.removeItem('assets-manager-verified')
    localStorage.removeItem('assets-manager-verified-time')
  }

  // 检查验证状态是否过期（24小时）
  useEffect(() => {
    const verifiedTime = localStorage.getItem('assets-manager-verified-time')
    if (verifiedTime) {
      const timeDiff = Date.now() - parseInt(verifiedTime)
      const hoursDiff = timeDiff / (1000 * 60 * 60)

      if (hoursDiff > 24) {
        resetVerification()
      }
    }
  }, [])

  return {
    isVerified,
    isModalOpen,
    password,
    error,
    isLoading,
    setPassword,
    verifyPassword,
    resetVerification,
  }
}
