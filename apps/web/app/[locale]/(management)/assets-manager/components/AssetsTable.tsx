'use client'

import { useState } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@ui/components/table'
import { Button } from '@ui/components/button'
import { Checkbox } from '@ui/components/checkbox'
import { Badge } from '@ui/components/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@ui/components/select'
import {
  Eye,
  Edit,
  Trash2,
  Star,
  Heart,
  Calendar,
  User,
  Tag,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Copy,
} from 'lucide-react'
import { format } from 'date-fns'
import {
  AssetRecord,
  Category,
  ListAssetsRequest,
} from '../../../../api/assets-manager/lib/types'
import { useScenarioConfig } from '../hooks/useScenarioConfig'
import { DeleteConfirmDialog } from './DeleteConfirmDialog'

interface AssetsTableProps {
  assets: AssetRecord[]
  selectedAssets: string[]
  onAssetSelection: (assetIds: string[]) => void
  onAssetAction: (
    action: 'view' | 'edit' | 'delete',
    asset: AssetRecord
  ) => void
  // 分页信息
  currentPage: number
  pageSize: number
  total: number
  totalPages: number
  onParamsChange: (params: ListAssetsRequest) => void
  // 加载状态
  isLoading?: boolean
}

// 分类标签配置
const categoryLabels: Record<Category, { label: string; color: string }> = {
  text2image: {
    label: '文生图',
    color: 'bg-blue-500 text-white border-blue-300 font-semibold',
  },
  image2image: {
    label: '图生图',
    color: 'bg-green-500 text-white border-green-300 font-semibold',
  },
  text2video: {
    label: '文生视频',
    color: 'bg-purple-500 text-white border-purple-300 font-semibold',
  },
  image2video: {
    label: '图生视频',
    color: 'bg-orange-500 text-white border-orange-300 font-semibold',
  },
}

// 媒体类型标签配置
const typeLabels = {
  image: {
    label: '图片',
    color: 'bg-cyan-500 text-white border-cyan-300 font-semibold',
  },
  video: {
    label: '视频',
    color: 'bg-red-500 text-white border-red-300 font-semibold',
  },
}

// 页面大小选项
const pageSizeOptions = [10, 20, 50, 100]

export function AssetsTable({
  assets,
  selectedAssets,
  onAssetSelection,
  onAssetAction,
  currentPage,
  pageSize,
  total,
  totalPages,
  onParamsChange,
  isLoading = false,
}: AssetsTableProps) {
  const { scenarioOptions } = useScenarioConfig()

  // 全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      onAssetSelection(assets.map((asset) => asset.id))
    } else {
      onAssetSelection([])
    }
  }

  // 单个选择
  const handleSelectAsset = (assetId: string, checked: boolean) => {
    if (checked) {
      onAssetSelection([...selectedAssets, assetId])
    } else {
      onAssetSelection(selectedAssets.filter((id) => id !== assetId))
    }
  }

  // 获取场景名称
  const getScenarioName = (scenario: string) => {
    const option = scenarioOptions.find((opt) => opt.taskType === scenario)
    return option?.pathname || scenario
  }

  // 渲染评分
  const renderRating = (rating: number) => {
    return (
      <div className="flex items-center space-x-1">
        <span className="text-sm font-medium text-gray-900">
          {rating.toFixed(1)}
        </span>
      </div>
    )
  }

  // 渲染点赞数
  const renderLikesCount = (likesCount: number) => {
    return (
      <div className="flex items-center space-x-1">
        <span className="text-sm text-gray-900 font-medium">{likesCount}</span>
      </div>
    )
  }

  // 复制文本到剪贴板
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      // 可以添加toast提示，这里暂时用console.log
      console.log('已复制到剪贴板:', text)
    } catch (err) {
      console.error('复制失败:', err)
    }
  }

  // 渲染标签
  const renderTags = (tags?: string) => {
    if (!tags) return null

    const tagList = tags
      .split(',')
      .map((tag) => tag.trim())
      .filter(Boolean)
    if (tagList.length === 0) return null

    return (
      <div className="flex flex-wrap gap-1">
        {tagList.slice(0, 3).map((tag, index) => (
          <Badge
            key={index}
            className="text-xs bg-blue-100 text-white border-blue-200 font-medium"
          >
            {tag}
          </Badge>
        ))}
        {tagList.length > 3 && (
          <Badge className="text-xs bg-gray-100 text-white border-gray-200 font-medium">
            +{tagList.length - 3}
          </Badge>
        )}
      </div>
    )
  }

  // 渲染媒体预览
  const renderMediaPreview = (asset: AssetRecord) => {
    const previewUrl = asset.target_image || asset.video || asset.source_image
    if (!previewUrl) return null

    return (
      <div className="relative w-16 h-16 rounded-md overflow-hidden bg-gray-100">
        {asset.type === 'image' ? (
          <img
            src={`${previewUrl}?v=${asset.updated_at || asset.id}`}
            alt={asset.name}
            className="w-full h-full object-cover"
            onError={(e) => {
              const target = e.target as HTMLImageElement
              target.style.display = 'none'
            }}
          />
        ) : (
          <video
            src={`${previewUrl}?v=${asset.updated_at || asset.id}`}
            className="w-full h-full object-cover"
            muted
            onError={(e) => {
              const target = e.target as HTMLVideoElement
              target.style.display = 'none'
            }}
          />
        )}
      </div>
    )
  }

  const isAllSelected =
    assets.length > 0 && selectedAssets.length === assets.length
  const isIndeterminate =
    selectedAssets.length > 0 && selectedAssets.length < assets.length

  return (
    <div className="space-y-4  h-full">
      {/* 表格 */}
      <div className="border rounded-lg h-full">
        <div className="h-[calc(100vh-22rem)] overflow-auto">
          <Table className="h-full">
            <TableHeader className="sticky top-0 bg-gray-100 z-10">
              <TableRow>
                <TableHead className="w-12 bg-gray-100 font-semibold text-gray-900">
                  <Checkbox
                    checked={isAllSelected}
                    indeterminate={isIndeterminate}
                    onCheckedChange={handleSelectAll}
                  />
                </TableHead>
                <TableHead className="w-20 bg-gray-100 font-semibold text-gray-900">
                  预览
                </TableHead>
                <TableHead className="bg-gray-100 font-semibold text-gray-900">
                  素材名称
                </TableHead>
                <TableHead className="bg-gray-100 font-semibold text-gray-900">
                  分类
                </TableHead>
                <TableHead className="bg-gray-100 font-semibold text-gray-900">
                  资源类型
                </TableHead>
                <TableHead className="bg-gray-100 font-semibold text-gray-900">
                  应用场景
                </TableHead>
                <TableHead className="bg-gray-100 font-semibold text-gray-900">
                  <div className="flex items-center space-x-1 gap-1">
                    <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
                    评分
                  </div>
                </TableHead>
                <TableHead className="bg-gray-100 font-semibold text-gray-900">
                  <div className="flex items-center space-x-1 gap-1">
                    <Heart className="h-4 w-4 fill-red-500 text-red-500" />
                    点赞
                  </div>
                </TableHead>
                <TableHead className="bg-gray-100 font-semibold text-gray-900">
                  <div className="flex items-center space-x-1 gap-1">
                    <Tag className="h-4 w-4 text-gray-500" />
                    标签
                  </div>
                </TableHead>
                <TableHead className="bg-gray-100 font-semibold text-gray-900">
                  原创性
                </TableHead>
                <TableHead className="bg-gray-100 font-semibold text-gray-900">
                  <div className="flex items-center space-x-1 gap-1">
                    <User className="h-4 w-4 text-gray-500" />
                    用户ID
                  </div>
                </TableHead>
                <TableHead className="bg-gray-100 font-semibold text-gray-900">
                  <div className="flex items-center space-x-1 gap-1">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    创建时间
                  </div>
                </TableHead>
                <TableHead className="w-32 bg-gray-100 font-semibold text-gray-900">
                  操作
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody className="h-full">
              {isLoading ? (
                <TableRow>
                  <TableCell
                    colSpan={13}
                    className="text-center py-8 text-gray-500"
                  >
                    <div className="flex items-center justify-center space-x-2">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                      <span>加载中...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : assets.length === 0 ? (
                <TableRow>
                  <TableCell
                    colSpan={13}
                    className="text-center py-8 text-gray-500"
                  >
                    暂无数据
                  </TableCell>
                </TableRow>
              ) : (
                assets.map((asset) => (
                  <TableRow key={asset.id} className="hover:bg-gray-50">
                    <TableCell>
                      <Checkbox
                        checked={selectedAssets.includes(asset.id)}
                        onCheckedChange={(checked) =>
                          handleSelectAsset(asset.id, checked as boolean)
                        }
                      />
                    </TableCell>

                    <TableCell>{renderMediaPreview(asset)}</TableCell>

                    <TableCell>
                      <div className="max-w-40">
                        <div
                          className="font-medium text-sm truncate text-gray-900"
                          title={asset.name}
                        >
                          {asset.name}
                        </div>
                      </div>
                    </TableCell>

                    <TableCell>
                      <Badge
                        className={categoryLabels[asset.category].color}
                        variant={'custom'}
                      >
                        {asset.category}
                      </Badge>
                    </TableCell>

                    <TableCell>
                      <Badge
                        className={typeLabels[asset.type].color}
                        variant={'custom'}
                      >
                        {typeLabels[asset.type].label}
                      </Badge>
                    </TableCell>

                    <TableCell>
                      <span className="text-sm text-gray-900 font-medium">
                        {getScenarioName(asset.scenario)}
                      </span>
                    </TableCell>

                    <TableCell>{renderRating(asset.rating)}</TableCell>

                    <TableCell>{renderLikesCount(asset.likes_count)}</TableCell>

                    <TableCell>
                      <div className="max-w-32">{renderTags(asset.tags)}</div>
                    </TableCell>

                    <TableCell>
                      <Badge
                        className={
                          asset.is_original === 1
                            ? 'bg-green-100 text-green-800 border-green-300'
                            : 'bg-gray-100 text-gray-600 border-gray-300'
                        }
                        variant={'custom'}
                      >
                        {asset.is_original === 1 ? '原创' : '非原创'}
                      </Badge>
                    </TableCell>

                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <span
                          className="text-sm text-gray-900 truncate max-w-20 font-medium"
                          title={asset.user_id}
                        >
                          {asset.user_id}
                        </span>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => copyToClipboard(asset.user_id)}
                          title="复制用户ID"
                          className="h-6 w-6 p-0 hover:bg-gray-100"
                        >
                          <Copy className="h-3 w-3 text-gray-500 hover:text-gray-700" />
                        </Button>
                      </div>
                    </TableCell>

                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <span className="text-sm text-gray-900 font-medium">
                          {format(new Date(asset.created_at), 'yyyy-MM-dd')}
                        </span>
                      </div>
                    </TableCell>

                    <TableCell>
                      <div className="flex space-x-1">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => onAssetAction('view', asset)}
                          title="查看"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => onAssetAction('edit', asset)}
                          title="编辑"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <DeleteConfirmDialog
                          trigger={
                            <Button
                              size="sm"
                              variant="ghost"
                              className="text-red-600 hover:text-red-700"
                              title="删除"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          }
                          title="确认删除素材"
                          description={`您确定要删除素材"${asset.name}"吗？`}
                          count={1}
                          onConfirm={async () => {
                            await onAssetAction('delete', asset)
                          }}
                        />
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* 分页控件 */}
      {total > 0 && (
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-700 whitespace-nowrap">
              显示 {Math.min((currentPage - 1) * pageSize + 1, total)} 到{' '}
              {Math.min(currentPage * pageSize, total)} 条，共 {total} 条记录
            </span>

            <Select
              value={pageSize.toString()}
              onValueChange={(value) =>
                onParamsChange({ page: 1, pageSize: Number(value) })
              }
              disabled={isLoading}
            >
              <SelectTrigger className="bg-white border-gray-300 focus:border-blue-700 focus:ring-blue-700 text-gray-900">
                <SelectValue
                  placeholder="选择页面大小"
                  className="text-gray-500"
                />
              </SelectTrigger>
              <SelectContent className="bg-white border border-gray-200 shadow-lg">
                {pageSizeOptions.map((size) => (
                  <SelectItem
                    key={size}
                    value={size.toString()}
                    className="bg-white hover:bg-blue-50 focus:bg-blue-700 focus:text-white text-gray-900 cursor-pointer"
                  >
                    {size} 条/页
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onParamsChange({ page: 1, pageSize })}
              disabled={currentPage === 1 || isLoading}
            >
              <ChevronsLeft className="h-4 w-4" />
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                onParamsChange({ page: currentPage - 1, pageSize })
              }
              disabled={currentPage === 1 || isLoading}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>

            <div className="flex items-center space-x-1">
              {/* 显示页码 */}
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let pageNum
                if (totalPages <= 5) {
                  pageNum = i + 1
                } else if (currentPage <= 3) {
                  pageNum = i + 1
                } else if (currentPage >= totalPages - 2) {
                  pageNum = totalPages - 4 + i
                } else {
                  pageNum = currentPage - 2 + i
                }

                return (
                  <Button
                    key={pageNum}
                    variant={pageNum === currentPage ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => onParamsChange({ page: pageNum, pageSize })}
                    disabled={isLoading}
                    className="w-8 h-8"
                  >
                    {pageNum}
                  </Button>
                )
              })}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                onParamsChange({ page: currentPage + 1, pageSize })
              }
              disabled={currentPage === totalPages || isLoading}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => onParamsChange({ page: totalPages, pageSize })}
              disabled={currentPage === totalPages || isLoading}
            >
              <ChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
