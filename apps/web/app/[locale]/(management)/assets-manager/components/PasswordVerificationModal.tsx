/**
 * 密码验证弹窗组件
 */

'use client'

import { useEffect } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@ui/components/dialog'
import { Button } from '@ui/components/button'
import { PasswordInput } from '@ui/components/password-input'
import { Lock, AlertCircle } from 'lucide-react'

interface PasswordVerificationModalProps {
  isOpen: boolean
  password: string
  error: string | null
  isLoading: boolean
  onPasswordChange: (password: string) => void
  onVerify: () => Promise<void>
}

export function PasswordVerificationModal({
  isOpen,
  password,
  error,
  isLoading,
  onPasswordChange,
  onVerify,
}: PasswordVerificationModalProps) {
  // 处理回车键提交
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !isLoading) {
      onVerify()
    }
  }

  // 处理ESC键关闭（这里我们不关闭，因为需要验证才能访问）
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      e.preventDefault()
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={() => {}}>
      <DialogContent
        className="sm:max-w-md"
        onKeyPress={handleKeyPress}
        onKeyDown={handleKeyDown}
      >
        <DialogHeader>
          <div className="flex items-center justify-center mb-4">
            <div className="bg-blue-100 p-3 rounded-full">
              <Lock className="h-6 w-6 text-blue-600" />
            </div>
          </div>
          <DialogTitle className="text-center text-xl font-semibold">
            素材管理访问验证
          </DialogTitle>
          <DialogDescription className="text-center text-gray-600">
            请输入访问密码以进入素材管理系统
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-2">
            <label
              htmlFor="password"
              className="text-sm font-medium text-gray-700"
            >
              访问密码
            </label>
            <PasswordInput
              value={password}
              onChange={onPasswordChange}
              className="w-full"
            />
          </div>

          {error && (
            <div className="flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-md">
              <AlertCircle className="h-4 w-4 text-red-500 flex-shrink-0" />
              <span className="text-sm text-red-600">{error}</span>
            </div>
          )}

          <Button
            onClick={onVerify}
            disabled={isLoading || !password.trim()}
            className="w-full"
            size="lg"
          >
            {isLoading ? (
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>验证中...</span>
              </div>
            ) : (
              '验证密码'
            )}
          </Button>
        </div>

        <div className="text-center text-xs text-gray-500 mt-4">
          <p>请输入正确的访问密码以继续</p>
        </div>
      </DialogContent>
    </Dialog>
  )
}
