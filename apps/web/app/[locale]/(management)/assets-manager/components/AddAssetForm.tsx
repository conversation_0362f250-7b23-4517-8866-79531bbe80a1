'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@ui/components/button'
import { Input } from '@ui/components/input'
import { Textarea } from '@ui/components/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@ui/components/select'
import { Badge } from '@ui/components/badge'
import { Label } from '@ui/components/label'
import { Switch } from '@ui/components/switch'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@ui/components/form'
import { Star, Heart, Tag, X, Eye, Download } from 'lucide-react'
import { z } from 'zod'
import {
  AssetRecord,
  Category,
  MediaType,
} from '../../../../api/assets-manager/lib/types'
import { useScenarioConfig } from '../hooks/useScenarioConfig'
import { FileUpload } from './FileUpload'

interface AddAssetFormProps {
  onSubmit: (data: any) => Promise<void> // 改为接收 JSON 数据
}

// 常用标签选项 (只有英文会被添加到标签字段)
const commonTags = [
  { en: 'Ghibli', zh: '吉卜力' },
  { en: 'Portrait', zh: '艺术照' },
  { en: 'Beautiful Woman', zh: '美女' },
  { en: 'Handsome Man', zh: '帅哥' },
  { en: 'Anime', zh: '动漫' },
  { en: 'Cyberpunk', zh: '赛博朋克' },
  { en: 'Fantasy', zh: '奇幻' },
  { en: 'Realistic', zh: '写实' },
  { en: 'Cute', zh: '可爱' },
  { en: 'Cool', zh: '酷' },
  { en: 'Vintage', zh: '复古' },
  { en: 'Modern', zh: '现代' },
  { en: 'Nature', zh: '自然' },
  { en: 'Abstract', zh: '抽象' },
  { en: 'Minimalist', zh: '简约' },
  { en: 'Colorful', zh: '彩色' },
  { en: 'Futuristic', zh: '未来感' },
  { en: 'Romantic', zh: '浪漫' },
  { en: 'Art', zh: '艺术' },
  { en: 'Creative', zh: '创意' },
  { en: 'High Quality', zh: '高质量' },
  { en: 'Digital Art', zh: '数字艺术' },
  { en: 'Concept Art', zh: '概念艺术' },
  { en: '4K', zh: '4K高清' },
  { en: '8K', zh: '8K超清' },
  { en: 'Ultra HD', zh: '超高清' },
  { en: 'Photorealistic', zh: '照片级真实' },
  { en: 'Masterpiece', zh: '杰作' },
  { en: 'Professional', zh: '专业级' },
  { en: 'Studio Lighting', zh: '影棚打光' },
  { en: 'Cinematic', zh: '电影感' },
  { en: 'Epic', zh: '史诗级' },
  { en: 'Trending', zh: '热门' },
  { en: 'Viral', zh: '爆款' },
  { en: 'Instagram', zh: 'Instagram风' },
  { en: 'TikTok', zh: 'TikTok风' },
  { en: 'Kawaii', zh: '可爱风' },
  { en: 'Aesthetic', zh: '美学' },
  { en: 'Vaporwave', zh: '蒸汽波' },
  { en: 'Synthwave', zh: '合成波' },
  { en: 'Neon', zh: '霓虹' },
  { en: 'Glitch', zh: '故障风' },
  { en: 'Retro', zh: '复古' },
  { en: '90s', zh: '90年代' },
  { en: 'Y2K', zh: 'Y2K风格' },
  { en: 'Cottagecore', zh: '田园风' },
  { en: 'Dark Academia', zh: '暗黑学院风' },
  { en: 'Minimalism', zh: '极简主义' },
  { en: 'Maximalism', zh: '极繁主义' },
  { en: 'Street Art', zh: '街头艺术' },
  { en: 'Pop Art', zh: '波普艺术' },
  { en: 'Surreal', zh: '超现实' },
  { en: 'Dreamy', zh: '梦幻' },
  { en: 'Ethereal', zh: '空灵' },
  { en: 'Moody', zh: '情绪化' },
  { en: 'Dramatic', zh: '戏剧化' },
  { en: 'Elegant', zh: '优雅' },
  { en: 'Luxury', zh: '奢华' },
  { en: 'Premium', zh: '高端' },
  { en: 'Fashion', zh: '时尚' },
  { en: 'Trendy', zh: '潮流' },
  { en: 'Stylish', zh: '时髦' },
]

export function AddAssetForm({ onSubmit }: AddAssetFormProps) {
  const {
    scenarioOptions,
    getCategoryByTaskType,
    getMediaTypeByTaskType,
    getFilteredOptions,
  } = useScenarioConfig()
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [customTag, setCustomTag] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitError, setSubmitError] = useState<string | null>(null)

  // 文件URL状态管理
  const [fileUrls, setFileUrls] = useState({
    source_image: '',
    target_image: '',
    video: '',
  })

  // 获取当前表单的验证架构
  const getFormSchema = (category?: Category) => {
    // 基础验证字段
    const baseSchema = {
      name: z.string().min(1, '素材名称不能为空'),
      category: z.string().optional(),
      scenario: z.string().min(1, '请选择应用场景'),
      tags: z.string().optional(),
      comments: z.string().optional(),
      rating: z.number().optional(),
      likes_count: z.number().optional(),
      is_original: z.number().int().min(0).max(1).default(0),
      aspect_ratio: z.enum(['16:9', '9:16']).default('16:9'),
    }

    // 根据不同分类设置字段验证规则
    switch (category) {
      case 'text2image':
        return z.object({
          ...baseSchema,
          prompt: z.string().min(1, '提示词不能为空'),
          target_image: z.union([
            z.instanceof(File),
            z.string().url().min(1, '请上传生成图片'),
          ]),
        })

      case 'image2image':
        return z.object({
          ...baseSchema,
          source_image: z.union([
            z.instanceof(File),
            z.string().url().min(1, '请上传源图片'),
          ]),
          target_image: z.union([
            z.instanceof(File),
            z.string().url().min(1, '请上传生成图片'),
          ]),
        })

      case 'text2video':
        return z.object({
          ...baseSchema,
          prompt: z.string().min(1, '提示词不能为空'),
          video: z.union([
            z.instanceof(File),
            z.string().url().min(1, '请上传生成视频'),
          ]),
        })

      case 'image2video':
        return z.object({
          ...baseSchema,
          source_image: z.union([
            z.instanceof(File),
            z.string().url().min(1, '请上传源图片'),
          ]),
          video: z.union([
            z.instanceof(File),
            z.string().url().min(1, '请上传生成视频'),
          ]),
        })

      default:
        // 默认情况：所有字段都是可选的
        return z.object({
          ...baseSchema,
          prompt: z.string().optional(),
          source_image: z
            .union([
              z.instanceof(File),
              z.string().url(),
              z.literal(''),
              z.undefined(),
            ])
            .optional(),
          target_image: z
            .union([
              z.instanceof(File),
              z.string().url(),
              z.literal(''),
              z.undefined(),
            ])
            .optional(),
          video: z
            .union([
              z.instanceof(File),
              z.string().url(),
              z.literal(''),
              z.undefined(),
            ])
            .optional(),
        })
    }
  }

  // 监听 scenario 变化，自动设置 category 和 type
  const [currentCategory, setCurrentCategory] = useState<Category | undefined>(
    'text2image'
  )
  const [currentType, setCurrentType] = useState<MediaType | undefined>('image')

  const form = useForm<any>({
    resolver: zodResolver(getFormSchema(currentCategory)),
    defaultValues: {
      name: '',
      category: 'text2image',
      scenario: scenarioOptions[0]?.taskType || '',
      prompt: '',
      tags: '',
      comments: '由AI智能生成的高质量作品',
      rating: Math.round((Math.random() * 2 + 3) * 10) / 10, // 3.0-5.0，保留一位小数
      likes_count: Math.floor(Math.random() * 1701) + 300, // 300-2000
      is_original: 0, // 默认为非原创
      aspect_ratio: '16:9', // 默认为16:9横屏
    },
    mode: 'onSubmit', // 只有在提交表单时才显示验证错误
    reValidateMode: 'onSubmit', // 重新验证也只在提交时进行
    shouldFocusError: false, // 不自动聚焦到错误字段
    criteriaMode: 'firstError', // 只显示第一个错误
    shouldUseNativeValidation: false, // 不使用浏览器原生验证
  })

  // 组件挂载时清除所有错误，确保初始状态干净
  useEffect(() => {
    form.clearErrors()
  }, [form])

  // 监听 scenario 变化
  const watchedScenario = form.watch('scenario')
  const watchedCategory = form.watch('category')

  useEffect(() => {
    if (watchedScenario) {
      const category = getCategoryByTaskType(watchedScenario)
      const type = getMediaTypeByTaskType(watchedScenario)
      setCurrentCategory(category)
      setCurrentType(type)

      // 清除表单错误，验证器会在下次验证时使用新的 schema
      form.clearErrors()
    }
  }, [watchedScenario, getCategoryByTaskType, getMediaTypeByTaskType, form])

  // 监听分类变化
  useEffect(() => {
    if (watchedCategory) {
      setCurrentCategory(watchedCategory as Category)
    }
  }, [watchedCategory])

  // 当分类变化时重新设置表单验证器
  useEffect(() => {
    if (currentCategory) {
      const newSchema = getFormSchema(currentCategory)
      // 移除手动触发验证，避免初始化时显示错误
      // form.trigger()
    }
  }, [currentCategory, form])

  // 处理标签添加
  const handleAddTag = (tag: string) => {
    if (tag && !selectedTags.includes(tag)) {
      const newTags = [...selectedTags, tag]
      setSelectedTags(newTags)
      form.setValue('tags', newTags.join(','))
    }
    setCustomTag('')
  }

  // 处理标签移除
  const handleRemoveTag = (tagToRemove: string) => {
    const newTags = selectedTags.filter((tag) => tag !== tagToRemove)
    setSelectedTags(newTags)
    form.setValue('tags', newTags.join(','))
  }

  // 文件上传成功回调
  const handleFileUpload = (fileType: string, url: string) => {
    setFileUrls((prev) => ({
      ...prev,
      [fileType]: url,
    }))
  }

  // 处理表单提交
  const handleSubmit = async (data: any) => {
    setSubmitError(null)
    setIsSubmitting(true)
    try {
      // 构建JSON数据
      const submitData: any = {
        name: data.name,
        category: data.category || currentCategory, // 确保包含分类信息
        scenario: data.scenario,
        prompt: data.prompt,
        tags: data.tags,
        comments: data.comments,
        rating: data.rating,
        likes_count: data.likes_count,
        is_original: data.is_original || 0,
        aspect_ratio: data.aspect_ratio || '16:9',
        user_id: '00000000-0000-0000-0000-000000000000', // 模拟用户ID
      }

      // 根据分类只添加需要的文件字段
      const category = data.category || currentCategory
      if (category === 'text2image') {
        // 文生图：只需要 target_image
        if (fileUrls.target_image)
          submitData.target_image = fileUrls.target_image
      } else if (category === 'image2image') {
        // 图生图：需要 source_image + target_image
        if (fileUrls.source_image)
          submitData.source_image = fileUrls.source_image
        if (fileUrls.target_image)
          submitData.target_image = fileUrls.target_image
      } else if (category === 'text2video') {
        // 文生视频：只需要 video
        if (fileUrls.video) submitData.video = fileUrls.video
      } else if (category === 'image2video') {
        // 图生视频：需要 source_image + video
        if (fileUrls.source_image)
          submitData.source_image = fileUrls.source_image
        if (fileUrls.video) submitData.video = fileUrls.video
      }

      await onSubmit(submitData)

      // 重置表单和文件
      form.reset({
        name: '',
        category: 'text2image',
        scenario: scenarioOptions[0]?.taskType || '',
        prompt: '',
        tags: '',
        comments: '由AI智能生成的高质量作品',
        rating: Math.round((Math.random() * 2 + 3) * 10) / 10, // 3.0-5.0，保留一位小数
        likes_count: Math.floor(Math.random() * 1701) + 300, // 300-2000
        aspect_ratio: '16:9',
        is_original: 0,
      })
      setSelectedTags([])

      // 清空文件状态
      setFileUrls({
        source_image: '',
        target_image: '',
        video: '',
      })

      // 重置分类状态
      setCurrentCategory('text2image')

      console.log('✅ 表单和文件已清空')
    } catch (error) {
      console.error('提交失败:', error)
      setSubmitError(
        error instanceof Error ? error.message : '提交失败，请重试'
      )
    } finally {
      setIsSubmitting(false)
    }
  }

  // 获取当前分类对应的场景选项
  const currentScenarioOptions = getFilteredOptions(currentCategory)

  // 检查字段是否必填
  const isFieldRequired = (fieldName: string) => {
    if (!currentCategory) return false

    const requiredFields = {
      text2image: ['prompt', 'target_image'],
      image2image: ['target_image', 'source_image'],
      text2video: ['prompt', 'video'],
      image2video: ['source_image', 'video'],
    }

    return requiredFields[currentCategory]?.includes(fieldName) || false
  }

  // 渲染文件预览
  const renderFilePreview = (
    file: File | string | undefined,
    type: 'image' | 'video'
  ) => {
    if (!file) return null

    const isUrl = typeof file === 'string'
    const src = isUrl ? file : URL.createObjectURL(file)

    return (
      <div className="relative w-full h-40 bg-gray-100 rounded-lg overflow-hidden">
        {type === 'image' ? (
          <img
            src={src}
            alt="预览"
            className="w-full h-full object-cover"
            onError={(e) => {
              const target = e.target as HTMLImageElement
              target.style.display = 'none'
            }}
          />
        ) : (
          <video
            src={src}
            className="w-full h-full object-cover"
            controls
            muted
            onError={(e) => {
              const target = e.target as HTMLVideoElement
              target.style.display = 'none'
            }}
          />
        )}
      </div>
    )
  }

  return (
    <div className="max-w-[1600px] mx-auto p-4">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* 左侧：基本信息 */}
            <div className="space-y-3 bg-white p-4 rounded-lg border border-gray-200">
              <div className="grid grid-cols-2 gap-6">
                <div>
                  {/* 素材名称 */}
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem className="flex items-center space-x-4">
                        <FormLabel className="flex whitespace-nowrap items-center text-gray-800 font-semibold text-sm w-20 flex-shrink-0">
                          素材名称
                          <span className="text-red-600 ml-1 font-bold">*</span>
                        </FormLabel>
                        <div className="flex-1">
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="请输入素材名称"
                              className="h-8 bg-white border border-gray-300 focus:border-blue-700 focus:ring-1 focus:ring-blue-700 text-gray-900 placeholder-gray-500"
                            />
                          </FormControl>
                          <FormMessage />
                        </div>
                      </FormItem>
                    )}
                  />

                  {/* 分类选择 */}
                  <FormField
                    control={form.control}
                    name="category"
                    render={({ field }) => (
                      <FormItem className="flex items-center space-x-4">
                        <FormLabel className="flex items-center text-gray-800 font-semibold text-sm w-20 flex-shrink-0">
                          分类
                        </FormLabel>
                        <div className="flex-1">
                          <Select
                            onValueChange={(value) => {
                              const categoryValue =
                                value === 'all' ? undefined : value
                              field.onChange(categoryValue)
                              // 更新当前分类状态
                              setCurrentCategory(categoryValue as Category)
                              // 清空应用场景选择
                              form.setValue('scenario', '')
                            }}
                            value={field.value || 'text2image'}
                          >
                            <FormControl>
                              <SelectTrigger className="h-8 bg-white border-gray-300 focus:border-blue-700 focus:ring-blue-700 text-gray-900">
                                <SelectValue
                                  placeholder="请选择分类"
                                  className="text-gray-500"
                                />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent className="bg-white border border-gray-200 shadow-lg">
                              <SelectItem
                                value="all"
                                className="bg-white hover:bg-blue-50 focus:bg-blue-700 focus:text-white text-gray-900 cursor-pointer"
                              >
                                全部分类
                              </SelectItem>
                              <SelectItem
                                value="text2image"
                                className="bg-white hover:bg-blue-50 focus:bg-blue-700 focus:text-white text-gray-900 cursor-pointer"
                              >
                                text2image
                              </SelectItem>
                              <SelectItem
                                value="image2image"
                                className="bg-white hover:bg-blue-50 focus:bg-blue-700 focus:text-white text-gray-900 cursor-pointer"
                              >
                                image2image
                              </SelectItem>
                              <SelectItem
                                value="text2video"
                                className="bg-white hover:bg-blue-50 focus:bg-blue-700 focus:text-white text-gray-900 cursor-pointer"
                              >
                                text2video
                              </SelectItem>
                              <SelectItem
                                value="image2video"
                                className="bg-white hover:bg-blue-50 focus:bg-blue-700 focus:text-white text-gray-900 cursor-pointer"
                              >
                                image2video
                              </SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </div>
                      </FormItem>
                    )}
                  />

                  {/* 应用场景 */}
                  <FormField
                    control={form.control}
                    name="scenario"
                    render={({ field }) => (
                      <FormItem className="flex items-center space-x-4">
                        <FormLabel className="flex items-center text-gray-800 font-semibold text-sm w-20 flex-shrink-0">
                          应用场景
                          <span className="text-red-600 ml-1 font-bold">*</span>
                        </FormLabel>
                        <div className="flex-1">
                          <Select
                            onValueChange={(value) => {
                              field.onChange(value)
                              // 根据应用场景自动设置分类
                              const selectedScenario = scenarioOptions.find(
                                (opt) => opt.taskType === value
                              )
                              if (selectedScenario) {
                                form.setValue(
                                  'category',
                                  selectedScenario.category
                                )
                                setCurrentCategory(selectedScenario.category)
                              }
                            }}
                            value={field.value || ''}
                          >
                            <FormControl>
                              <SelectTrigger className="h-8 bg-white border-gray-300 focus:border-blue-700 focus:ring-blue-700 text-gray-900">
                                <SelectValue
                                  placeholder="请选择应用场景"
                                  className=" text-gray-500"
                                />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent className=" bg-white border border-gray-200 shadow-lg">
                              {(() => {
                                const selectedCategory =
                                  form.getValues('category')
                                const filteredOptions = selectedCategory
                                  ? scenarioOptions.filter(
                                      (opt) => opt.category === selectedCategory
                                    )
                                  : scenarioOptions
                                return filteredOptions.map((option) => (
                                  <SelectItem
                                    key={option.taskType}
                                    value={option.taskType}
                                    className="bg-white hover:bg-blue-50 focus:bg-blue-700 focus:text-white text-gray-900 cursor-pointer"
                                  >
                                    {option.pathname}
                                  </SelectItem>
                                ))
                              })()}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </div>
                      </FormItem>
                    )}
                  />
                </div>

                <div>
                  {/* 素材比例选择 */}
                  <FormField
                    control={form.control}
                    name="aspect_ratio"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-gray-800 font-semibold text-sm">
                          素材比例
                        </FormLabel>
                        <div className="flex space-x-4 justify-center pt-4">
                          {/* 16:9 横屏 */}
                          <div
                            className={`cursor-pointer p-4 border-2 rounded-lg transition-all ${
                              field.value === '16:9'
                                ? 'border-blue-500 bg-blue-50'
                                : 'border-gray-300 hover:border-gray-400'
                            }`}
                            onClick={() => field.onChange('16:9')}
                          >
                            <div className="flex flex-col items-center space-y-3">
                              <div className="w-16 h-9 bg-gray-400 rounded border"></div>
                              <span className="text-sm text-gray-600 font-medium">
                                16:9 横屏
                              </span>
                            </div>
                          </div>

                          {/* 9:16 竖屏 */}
                          <div
                            className={`cursor-pointer p-4 border-2 rounded-lg transition-all ${
                              field.value === '9:16'
                                ? 'border-blue-500 bg-blue-50'
                                : 'border-gray-300 hover:border-gray-400'
                            }`}
                            onClick={() => field.onChange('9:16')}
                          >
                            <div className="flex flex-col items-center space-y-3">
                              <div className="w-9 h-16 bg-gray-400 rounded border"></div>
                              <span className="text-sm text-gray-600 font-medium">
                                9:16 竖屏
                              </span>
                            </div>
                          </div>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* 提示词 */}
              <FormField
                control={form.control}
                name="prompt"
                render={({ field }) => (
                  <FormItem className="flex items-start space-x-4">
                    <FormLabel className="flex items-center text-gray-800 font-semibold text-sm w-20 flex-shrink-0 pt-2">
                      提示词{' '}
                      {(currentCategory === 'text2image' ||
                        currentCategory === 'text2video') && (
                        <span className="text-red-600 ml-1 font-bold">*</span>
                      )}
                    </FormLabel>
                    <div className="flex-1">
                      <FormControl>
                        <Textarea
                          {...field}
                          placeholder="请输入AI生成的提示词"
                          rows={2}
                          className="bg-white border border-gray-300 focus:border-blue-700 focus:ring-blue-700 text-gray-900 placeholder:text-gray-500 resize-none"
                        />
                      </FormControl>
                      <FormMessage />
                    </div>
                  </FormItem>
                )}
              />

              {/* 评分 */}
              <FormField
                control={form.control}
                name="rating"
                render={({ field }) => (
                  <FormItem className="flex items-center space-x-4">
                    <FormLabel className="flex items-center text-gray-800 font-semibold text-sm w-20 flex-shrink-0">
                      <Star className="h-4 w-4 mr-1 text-yellow-500 fill-yellow-500" />
                      评分
                    </FormLabel>
                    <div className="flex-1">
                      <FormControl>
                        <Input
                          {...field}
                          type="number"
                          min="0"
                          max="5"
                          step="0.1"
                          placeholder="4.8"
                          className="h-8 bg-white border border-gray-300 focus:border-blue-700 focus:ring-1 focus:ring-blue-700 text-gray-900 placeholder-gray-500"
                          onChange={(e) => {
                            const value = parseFloat(e.target.value)
                            if (value >= 0 && value <= 5) {
                              field.onChange(value)
                            }
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </div>
                  </FormItem>
                )}
              />

              {/* 点赞数 */}
              <FormField
                control={form.control}
                name="likes_count"
                render={({ field }) => (
                  <FormItem className="flex items-center space-x-4">
                    <FormLabel className="flex items-center text-gray-800 font-semibold text-sm w-20 flex-shrink-0">
                      <Heart className="h-4 w-4 mr-1 text-red-500 fill-red-500" />
                      点赞数
                    </FormLabel>
                    <div className="flex-1">
                      <FormControl>
                        <Input
                          {...field}
                          type="number"
                          min="0"
                          placeholder="100"
                          className="h-8 bg-white border border-gray-300 focus:border-blue-700 focus:ring-1 focus:ring-blue-700 text-gray-900 placeholder-gray-500"
                          onChange={(e) =>
                            field.onChange(Number(e.target.value))
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </div>
                  </FormItem>
                )}
              />

              {/* 原创性选择 */}
              <FormField
                control={form.control}
                name="is_original"
                render={({ field }) => (
                  <FormItem className="flex items-center space-x-4">
                    <FormLabel className="text-gray-800 font-semibold text-sm w-20 flex-shrink-0">
                      原创作品
                    </FormLabel>
                    <div className="flex-1 flex items-center justify-between rounded-lg border border-gray-300 p-3 bg-white">
                      <div className="text-xs text-gray-600">
                        标记此作品是否为原创内容
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value === 1}
                          onCheckedChange={(checked) =>
                            field.onChange(checked ? 1 : 0)
                          }
                        />
                      </FormControl>
                    </div>
                  </FormItem>
                )}
              />

              {/* 标签 */}
              <div className="flex items-start space-x-4">
                <Label className="flex items-center text-gray-800 font-semibold text-sm w-20 flex-shrink-0 pt-1">
                  <Tag className="h-4 w-4 mr-1 text-blue-600" />
                  标签
                </Label>

                <div className="flex-1 space-y-2">
                  {/* 已选标签 */}
                  {selectedTags.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {selectedTags.map((tag) => (
                        <Badge
                          key={tag}
                          className="text-xs text-white bg-blue-700 cursor-pointer font-medium border border-blue-300"
                        >
                          {tag}
                          <X
                            className="h-3 w-3 ml-1 hover:text-red-600"
                            onClick={() => handleRemoveTag(tag)}
                          />
                        </Badge>
                      ))}
                    </div>
                  )}

                  {/* 常用标签选择 */}
                  <div className="flex flex-wrap gap-1">
                    {commonTags
                      .filter((tag) => !selectedTags.includes(tag.en))
                      .map((tag) => (
                        <Badge
                          key={tag.en}
                          className="text-xs bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-700 cursor-pointer font-medium border border-gray-300 hover:border-blue-400"
                          onClick={() => handleAddTag(tag.en)}
                        >
                          + {tag.en} ({tag.zh})
                        </Badge>
                      ))}
                  </div>

                  {/* 自定义标签输入 */}
                  <div className="flex space-x-2">
                    <Input
                      placeholder="输入自定义标签"
                      value={customTag}
                      onChange={(e) => setCustomTag(e.target.value)}
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault()
                          handleAddTag(customTag)
                        }
                      }}
                      className="h-8 bg-white border border-gray-300 focus:border-blue-700 focus:ring-1 focus:ring-blue-700 text-gray-900 placeholder-gray-500 resize-none"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => handleAddTag(customTag)}
                      disabled={!customTag.trim()}
                      className="h-8 bg-blue-600 text-white hover:bg-blue-700 border-blue-600 font-medium text-sm px-3"
                    >
                      添加
                    </Button>
                  </div>
                </div>
              </div>

              {/* 备注 */}
              <FormField
                control={form.control}
                name="comments"
                render={({ field }) => (
                  <FormItem className="flex items-start space-x-4">
                    <FormLabel className="text-gray-800 font-semibold text-sm w-20 flex-shrink-0 pt-2">
                      评论
                    </FormLabel>
                    <div className="flex-1">
                      <FormControl>
                        <Textarea
                          {...field}
                          placeholder="添加备注信息"
                          rows={1}
                          className="bg-white border border-gray-300 focus:border-blue-700 focus:ring-1 focus:ring-blue-700 text-gray-900 placeholder-gray-500 resize-none"
                        />
                      </FormControl>
                      <FormMessage />
                    </div>
                  </FormItem>
                )}
              />
            </div>

            {/* 右侧：文件上传 */}
            <div className="space-y-3 bg-white p-4 rounded-lg border border-gray-200">
              {/* 源图片上传 */}
              {(currentCategory === 'image2image' ||
                currentCategory === 'image2video') && (
                <div>
                  <Label className="flex items-center text-gray-800 font-semibold text-sm">
                    源图片
                    {isFieldRequired('source_image') && (
                      <span className="text-red-600 ml-1 font-bold">*</span>
                    )}
                  </Label>
                  <FormField
                    control={form.control}
                    name="source_image"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <FileUpload
                            accept="image/*"
                            multiple={false}
                            maxSize={10 * 1024 * 1024} // 10MB
                            category={currentCategory}
                            fileType="source_image"
                            immediateUpload={true}
                            onUploadSuccess={(url) =>
                              handleFileUpload('source_image', url)
                            }
                            onUploadError={(error) =>
                              console.error('上传失败:', error)
                            }
                            onFileChange={(files: File[]) =>
                              field.onChange(files[0])
                            }
                            className="mt-2"
                          />
                        </FormControl>
                        <FormMessage />
                        <div className="text-xs text-gray-600 mt-1 font-medium">
                          支持拖拽、粘贴(Ctrl+V)、点击选择等方式上传，最大10MB
                        </div>
                        {/* 源图片预览 */}
                        {field.value && (
                          <div className="mt-3">
                            <p className="text-sm text-gray-600 mb-2">
                              源图片预览：
                            </p>
                            {renderFilePreview(field.value, 'image')}
                          </div>
                        )}
                      </FormItem>
                    )}
                  />
                </div>
              )}

              {/* 目标图片上传 */}
              {(currentCategory === 'text2image' ||
                currentCategory === 'image2image') && (
                <div>
                  <Label className="flex items-center text-gray-800 font-semibold text-sm">
                    生成图片
                    {isFieldRequired('target_image') && (
                      <span className="text-red-600 ml-1 font-bold">*</span>
                    )}
                  </Label>
                  <FormField
                    control={form.control}
                    name="target_image"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <FileUpload
                            accept="image/*"
                            multiple={false}
                            maxSize={10 * 1024 * 1024} // 10MB
                            category={currentCategory}
                            fileType="target_image"
                            immediateUpload={true}
                            onUploadSuccess={(url) =>
                              handleFileUpload('target_image', url)
                            }
                            onUploadError={(error) =>
                              console.error('上传失败:', error)
                            }
                            onFileChange={(files: File[]) =>
                              field.onChange(files[0])
                            }
                            className="mt-2"
                          />
                        </FormControl>
                        <FormMessage />
                        <div className="text-xs text-gray-600 mt-1 font-medium">
                          支持拖拽、粘贴(Ctrl+V)、点击选择等方式上传，最大10MB
                        </div>
                        {/* 生成图片预览 */}
                        {field.value && (
                          <div className="mt-3">
                            <p className="text-sm text-gray-600 mb-2">
                              生成图片预览：
                            </p>
                            {renderFilePreview(field.value, 'image')}
                          </div>
                        )}
                      </FormItem>
                    )}
                  />
                </div>
              )}

              {/* 视频上传 */}
              {(currentCategory === 'text2video' ||
                currentCategory === 'image2video') && (
                <div>
                  <Label className="flex items-center text-gray-800 font-semibold text-sm">
                    生成视频{' '}
                    {isFieldRequired('video') && (
                      <span className="text-red-600 ml-1 font-bold">*</span>
                    )}
                  </Label>
                  <FormField
                    control={form.control}
                    name="video"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <FileUpload
                            accept="video/*"
                            multiple={false}
                            maxSize={100 * 1024 * 1024} // 100MB
                            category={currentCategory}
                            fileType="video"
                            immediateUpload={true}
                            onUploadSuccess={(url) =>
                              handleFileUpload('video', url)
                            }
                            onUploadError={(error) =>
                              console.error('上传失败:', error)
                            }
                            onFileChange={(files: File[]) =>
                              field.onChange(files[0])
                            }
                            className="mt-2"
                          />
                        </FormControl>
                        <FormMessage />
                        <div className="text-xs text-gray-600 mt-1 font-medium">
                          支持拖拽、粘贴(Ctrl+V)、点击选择等方式上传，最大100MB
                        </div>
                        {/* 视频预览 */}
                        {field.value && (
                          <div className="mt-3">
                            <p className="text-sm text-gray-600 mb-2">
                              视频预览：
                            </p>
                            {renderFilePreview(field.value, 'video')}
                          </div>
                        )}
                      </FormItem>
                    )}
                  />
                </div>
              )}
            </div>
          </div>

          {/* 错误显示 */}
          {submitError && (
            <div className="bg-red-50 border border-red-200 rounded-md p-2">
              <p className="text-red-800 text-sm">{submitError}</p>
            </div>
          )}

          {/* 提交按钮 */}
          <div className="flex justify-end pt-2">
            <Button
              type="submit"
              variant="primary"
              size="sm"
              disabled={isSubmitting}
              className="px-6"
            >
              {isSubmitting ? '创建中...' : '创建素材'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}
