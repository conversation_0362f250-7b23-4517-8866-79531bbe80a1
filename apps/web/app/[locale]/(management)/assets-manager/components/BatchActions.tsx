'use client'

import { Button } from '@ui/components/button'
import { Badge } from '@ui/components/badge'
import { Trash2, Download, Archive, Tag, X } from 'lucide-react'
import { DeleteConfirmDialog } from './DeleteConfirmDialog'

interface BatchActionsProps {
  selectedCount: number
  onBatchDelete: () => Promise<void>
  onClearSelection: () => void
  disabled?: boolean
}

export function BatchActions({
  selectedCount,
  onBatchDelete,
  onClearSelection,
  disabled = false,
}: BatchActionsProps) {
  // 如果没有选中项，不显示工具栏
  if (selectedCount === 0) {
    return null
  }

  return (
    <div className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
      <div className="flex items-center space-x-3">
        {/* 选中数量 */}
        <Badge variant="secondary" className="bg-blue-100 text-blue-800">
          已选择 {selectedCount} 项
        </Badge>

        {/* 清除选择 */}
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={onClearSelection}
          disabled={disabled}
        >
          <X className="h-4 w-4 mr-1" />
          清除选择
        </Button>
      </div>

      <div className="flex items-center space-x-2">
        {/* 批量操作按钮 */}

        {/* 批量下载 */}
        {/* <Button
          type="button"
          variant="outline"
          size="sm"
          disabled={disabled}
          onClick={() => {
            // TODO: 实现批量下载功能
            console.log('批量下载功能待实现')
          }}
        >
          <Download className="h-4 w-4 mr-1" />
          下载
        </Button> */}

        {/* 批量标签 */}
        {/* <Button
          type="button"
          variant="outline"
          size="sm"
          disabled={disabled}
          onClick={() => {
            // TODO: 实现批量标签功能
            console.log('批量标签功能待实现')
          }}
        >
          <Tag className="h-4 w-4 mr-1" />
          标签
        </Button> */}

        {/* 批量归档 */}
        {/* <Button
          type="button"
          variant="outline"
          size="sm"
          disabled={disabled}
          onClick={() => {
            // TODO: 实现批量归档功能
            console.log('批量归档功能待实现')
          }}
        >
          <Archive className="h-4 w-4 mr-1" />
          归档
        </Button> */}

        {/* 批量删除 */}
        <DeleteConfirmDialog
          trigger={
            <Button type="button" variant="error" size="sm" disabled={disabled}>
              <Trash2 className="h-4 w-4 mr-1" />
              删除
            </Button>
          }
          title="确认批量删除"
          count={selectedCount}
          onConfirm={onBatchDelete}
          disabled={disabled}
        />
      </div>
    </div>
  )
}
