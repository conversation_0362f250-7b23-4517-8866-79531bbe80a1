'use client'

import { useState, useCallback, useRef } from 'react'
import { useDropzone } from 'react-dropzone'
import { Button } from '@ui/components/button'
import {
  Upload,
  Image,
  Video,
  File,
  X,
  AlertCircle,
  Copy,
  FolderOpen,
} from 'lucide-react'
import { cn } from '@ui/lib'

interface FileUploadProps {
  accept?: string
  multiple?: boolean
  maxSize?: number
  className?: string
  onFileChange?: (files: File[]) => void // 保持向后兼容
  disabled?: boolean
  // 新增属性
  category?: 'text2image' | 'image2image' | 'text2video' | 'image2video'
  fileType?: 'source_image' | 'target_image' | 'video'
  onUploadSuccess?: (url: string) => void
  onUploadError?: (error: string) => void
  immediateUpload?: boolean // 是否立即上传
}

// 格式描述函数
const getFormatDescription = (accept: string): string => {
  if (accept.includes('image/')) {
    return '图片文件 (JPG, PNG, GIF, WebP)'
  }
  if (accept.includes('video/')) {
    return '视频文件 (MP4, AVI, MOV, WebM)'
  }
  if (accept.includes('image/') && accept.includes('video/')) {
    return '图片或视频文件'
  }
  return accept
}

export function FileUpload({
  accept = '*/*',
  multiple = false,
  maxSize = 10 * 1024 * 1024, // 10MB
  className,
  onFileChange,
  disabled = false,
  category,
  fileType,
  onUploadSuccess,
  onUploadError,
  immediateUpload = false,
}: FileUploadProps) {
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([])
  const [isDragOver, setIsDragOver] = useState(false)
  const [error, setError] = useState<string>('')
  const [isProcessingPaste, setIsProcessingPaste] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadedUrl, setUploadedUrl] = useState<string>('')
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 上传文件到服务器
  const uploadFileToServer = useCallback(
    async (file: File): Promise<string> => {
      if (!category || !fileType) {
        throw new Error('缺少必要的上传参数：category 和 fileType')
      }

      const formData = new FormData()
      formData.append('file', file)
      formData.append('category', category)
      formData.append('fileType', fileType)

      const response = await fetch('/api/assets-manager/upload', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || '上传失败')
      }

      const result = await response.json()
      return result.data.url
    },
    [category, fileType]
  )

  // 验证文件
  const validateFile = useCallback(
    (file: File): string | null => {
      if (file.size > maxSize) {
        return `文件大小不能超过 ${(maxSize / 1024 / 1024).toFixed(0)}MB`
      }

      // 检查文件类型
      if (accept !== '*/*') {
        const acceptedTypes = accept.split(',').map((type) => type.trim())
        const fileType = file.type
        const fileExtension = file.name.split('.').pop()?.toLowerCase()

        // 图片格式校验
        if (accept.includes('image/')) {
          const imageTypes = [
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/gif',
            'image/webp',
          ]
          const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp']

          if (
            !imageTypes.includes(fileType) &&
            !imageExtensions.includes(fileExtension || '')
          ) {
            return '请选择正确的图片格式 (JPG, PNG, GIF, WebP)'
          }
        }

        // 视频格式校验
        if (accept.includes('video/')) {
          const videoTypes = [
            'video/mp4',
            'video/avi',
            'video/mov',
            'video/webm',
            'video/quicktime',
          ]
          const videoExtensions = ['mp4', 'avi', 'mov', 'webm', 'qt']

          if (
            !videoTypes.includes(fileType) &&
            !videoExtensions.includes(fileExtension || '')
          ) {
            return '请选择正确的视频格式 (MP4, AVI, MOV, WebM)'
          }
        }

        // 通用类型检查
        const isAccepted = acceptedTypes.some((type) => {
          if (type.endsWith('/*')) {
            return fileType.startsWith(type.slice(0, -1))
          }
          return fileType === type
        })

        if (!isAccepted) {
          return `不支持的文件类型，支持：${getFormatDescription(accept)}`
        }
      }

      return null
    },
    [accept, maxSize]
  )

  // 处理文件添加
  const handleFilesAdd = useCallback(
    async (newFiles: File[]) => {
      const validFiles: File[] = []
      let errorMessage = ''

      for (const file of newFiles) {
        const validation = validateFile(file)
        if (validation) {
          errorMessage = validation
          break
        }
        validFiles.push(file)
      }

      if (errorMessage) {
        setError(errorMessage)
        return
      }

      setError('')

      // 如果启用立即上传且只有一个文件
      if (immediateUpload && validFiles.length === 1 && category && fileType) {
        setIsUploading(true)
        try {
          const url = await uploadFileToServer(validFiles[0])
          setUploadedUrl(url)
          onUploadSuccess?.(url)
        } catch (error) {
          const errorMsg = error instanceof Error ? error.message : '上传失败'
          setError(errorMsg)
          onUploadError?.(errorMsg)
          return
        } finally {
          setIsUploading(false)
        }
      }

      const finalFiles = multiple
        ? [...uploadedFiles, ...validFiles]
        : validFiles
      setUploadedFiles(finalFiles)
      onFileChange?.(finalFiles)
    },
    [uploadedFiles, multiple, validateFile, onFileChange]
  )

  // 处理文件移除
  const handleFileRemove = useCallback(
    (index: number) => {
      const newFiles = uploadedFiles.filter((_, i) => i !== index)
      setUploadedFiles(newFiles)
      onFileChange(newFiles)
    },
    [uploadedFiles, onFileChange]
  )

  // 拖拽上传
  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      setIsDragOver(false)
      if (!disabled) {
        handleFilesAdd(acceptedFiles)
      }
    },
    [handleFilesAdd, disabled]
  )

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: accept === '*/*' ? undefined : { [accept]: [] },
    multiple,
    disabled,
    onDragEnter: () => setIsDragOver(true),
    onDragLeave: () => setIsDragOver(false),
  })

  // 处理粘贴上传
  const handlePaste = useCallback(
    async (event: React.ClipboardEvent) => {
      if (disabled) return

      const items = event.clipboardData?.items
      if (!items) return

      setIsProcessingPaste(true)
      const files: File[] = []

      for (let i = 0; i < items.length; i++) {
        const item = items[i]
        if (item.kind === 'file') {
          const file = item.getAsFile()
          if (file) {
            files.push(file)
          }
        }
      }

      if (files.length > 0) {
        handleFilesAdd(files)
      }

      setIsProcessingPaste(false)
    },
    [handleFilesAdd, disabled]
  )

  // 处理复制上传
  const handleCopyUpload = useCallback(async () => {
    if (disabled) return

    try {
      setIsProcessingPaste(true)
      // 提示用户使用Ctrl+V
      alert('请使用Ctrl+V快捷键粘贴图片或视频文件')
    } catch (error) {
      console.error('复制上传失败:', error)
    } finally {
      setIsProcessingPaste(false)
    }
  }, [disabled])

  // 手动选择文件
  const handleFileSelect = useCallback(() => {
    if (disabled) return
    fileInputRef.current?.click()
  }, [disabled])

  const handleInputChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const files = Array.from(event.target.files || [])
      if (files.length > 0) {
        handleFilesAdd(files)
      }
      // 清空 input 以允许重复选择同一文件
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    },
    [handleFilesAdd]
  )

  // 获取文件图标
  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) {
      return <Image className="h-6 w-6 text-blue-500" />
    }
    if (file.type.startsWith('video/')) {
      return <Video className="h-6 w-6 text-purple-500" />
    }
    return <File className="h-6 w-6 text-gray-500" />
  }

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 生成文件预览
  const renderFilePreview = (file: File, index: number) => {
    const isImage = file.type.startsWith('image/')
    const isVideo = file.type.startsWith('video/')

    return (
      <div
        key={index}
        className="relative group bg-gray-50 rounded-lg p-3 border"
      >
        <div className="flex items-start space-x-3">
          {/* 文件预览或图标 */}
          <div className="flex-shrink-0">
            {isImage ? (
              <div className="w-12 h-12 rounded-md overflow-hidden bg-gray-200">
                <img
                  src={URL.createObjectURL(file)}
                  alt={file.name}
                  className="w-full h-full object-cover"
                  onLoad={(e) => {
                    // 释放对象URL以避免内存泄漏
                    setTimeout(() => {
                      URL.revokeObjectURL((e.target as HTMLImageElement).src)
                    }, 1000)
                  }}
                />
              </div>
            ) : isVideo ? (
              <div className="w-12 h-12 rounded-md overflow-hidden bg-gray-200 flex items-center justify-center">
                <Video className="h-6 w-6 text-purple-500" />
              </div>
            ) : (
              <div className="w-12 h-12 rounded-md bg-gray-200 flex items-center justify-center">
                {getFileIcon(file)}
              </div>
            )}
          </div>

          {/* 文件信息 */}
          <div className="flex-1 min-w-0">
            <p
              className="text-sm font-medium text-gray-900 truncate"
              title={file.name}
            >
              {file.name}
            </p>
            <p className="text-xs text-gray-500">{formatFileSize(file.size)}</p>
            <p className="text-xs text-gray-400">{file.type || '未知类型'}</p>
          </div>

          {/* 删除按钮 */}
          {!disabled && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={() => handleFileRemove(index)}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className={cn('space-y-4', className)}>
      {/* 上传区域 */}
      <div
        {...getRootProps()}
        className={cn(
          'border-2 border-dashed rounded-lg p-4 text-center cursor-pointer transition-all duration-200',
          isDragActive || isDragOver
            ? 'border-blue-600 bg-blue-50 shadow-md'
            : 'border-gray-300 hover:border-blue-400 hover:bg-gray-50',
          disabled && 'opacity-50 cursor-not-allowed'
        )}
        onPaste={handlePaste}
        tabIndex={0}
      >
        <input
          {...getInputProps()}
          ref={fileInputRef}
          onChange={handleInputChange}
        />

        <div className="space-y-2">
          <div className="space-y-2">
            <p className="text-lg font-semibold text-gray-900">
              {isDragActive ? '释放文件以上传' : '选择文件上传'}
            </p>
            <div className="text-sm text-gray-600 space-y-1">
              <p className="font-medium">选中区域后可直接复制粘贴文件</p>
              <p>
                支持格式：
                {accept === '*/*' ? '所有文件' : getFormatDescription(accept)}
                {maxSize && ` · 最大 ${(maxSize / 1024 / 1024).toFixed(0)}MB`}
              </p>
            </div>
          </div>

          <div className="flex justify-center gap-3">
            <Button
              type="button"
              variant="primary"
              size="default"
              onClick={handleFileSelect}
              disabled={disabled}
            >
              <FolderOpen className="h-4 w-4 mr-2" />
              选择文件
            </Button>

            <Button
              type="button"
              variant="outline"
              size="default"
              onClick={handleCopyUpload}
              disabled={disabled || isProcessingPaste}
              title="选中此区域后可直接Ctrl+V粘贴文件"
              className="border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              <Copy className="h-4 w-4 mr-2" />
              {isProcessingPaste ? '处理中...' : '粘贴文件'}
            </Button>
          </div>
        </div>
      </div>

      {/* 上传状态 */}
      {isUploading && (
        <div className="flex items-center space-x-2 text-blue-600 bg-blue-50 p-3 rounded-lg">
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
          <span className="text-sm">正在上传文件...</span>
        </div>
      )}

      {/* 上传成功提示 */}
      {uploadedUrl && !isUploading && (
        <div className="flex items-center space-x-2 text-green-600 bg-green-50 p-3 rounded-lg">
          <div className="h-5 w-5 rounded-full bg-green-600 flex items-center justify-center">
            <span className="text-white text-xs">✓</span>
          </div>
          <div className="flex-1">
            <span className="text-sm">文件上传成功</span>
            <div className="text-xs text-green-700 mt-1 break-all">
              {uploadedUrl}
            </div>
          </div>
        </div>
      )}

      {/* 错误提示 */}
      {error && (
        <div className="flex items-center space-x-2 text-red-600 bg-red-50 p-3 rounded-lg">
          <AlertCircle className="h-5 w-5" />
          <span className="text-sm">{error}</span>
        </div>
      )}

      {/* 已上传文件列表 */}
      {uploadedFiles.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-900">
            已选择文件 ({uploadedFiles.length})
          </h4>
          <div className="space-y-2 max-h-60 overflow-y-auto">
            {uploadedFiles.map((file, index) => renderFilePreview(file, index))}
          </div>
        </div>
      )}
    </div>
  )
}
