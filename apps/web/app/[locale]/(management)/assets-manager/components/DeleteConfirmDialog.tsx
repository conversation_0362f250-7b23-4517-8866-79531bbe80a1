'use client'

import { useState } from 'react'
import { Button } from '@ui/components/button'
import { AlertTriangle } from 'lucide-react'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@ui/components/alert-dialog'

interface DeleteConfirmDialogProps {
  trigger: React.ReactNode
  title?: string
  description?: string
  count?: number
  onConfirm: () => Promise<void>
  disabled?: boolean
}

export function DeleteConfirmDialog({
  trigger,
  title = '确认删除',
  description,
  count = 1,
  onConfirm,
  disabled = false,
}: DeleteConfirmDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false)

  const handleConfirm = async () => {
    if (disabled || isDeleting) return

    setIsDeleting(true)
    try {
      await onConfirm()
    } catch (error) {
      console.error('删除失败:', error)
    } finally {
      setIsDeleting(false)
    }
  }

  const defaultDescription =
    count === 1
      ? '您确定要删除这个素材吗？'
      : `您确定要删除选中的 ${count} 个素材吗？`

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>{trigger}</AlertDialogTrigger>
      <AlertDialogContent className="bg-white">
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center text-gray-900">
            <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
            {title}
          </AlertDialogTitle>
          <AlertDialogDescription className="text-gray-700">
            {description || defaultDescription}
            <br />
            <span className="text-red-600 font-medium">
              此操作不可撤销，将同时删除相关的文件资源。
            </span>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting} className="text-gray-700">
            取消
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={isDeleting}
            className="bg-red-600 hover:bg-red-700 text-white"
          >
            {isDeleting
              ? '删除中...'
              : count === 1
              ? '确认删除'
              : `确认删除 ${count} 项`}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
