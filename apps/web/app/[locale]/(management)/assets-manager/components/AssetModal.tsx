'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@ui/components/dialog'
import { Button } from '@ui/components/button'
import { Input } from '@ui/components/input'
import { Textarea } from '@ui/components/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@ui/components/select'
import { Badge } from '@ui/components/badge'
import { Label } from '@ui/components/label'
import { Switch } from '@ui/components/switch'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@ui/components/form'
import {
  Star,
  Heart,
  Tag,
  Upload,
  X,
  Eye,
  Download,
  X as XIcon,
} from 'lucide-react'
import { z } from 'zod'
import {
  AssetRecord,
  Category,
  MediaType,
} from '../../../../api/assets-manager/lib/types'
import { useScenarioConfig } from '../hooks/useScenarioConfig'
import { getValidationSchema } from '../../../../api/assets-manager/lib/validation'
import { FileUpload } from './FileUpload'

interface AssetModalProps {
  isOpen: boolean
  mode: 'edit' | 'view'
  asset?: AssetRecord
  onClose: () => void
  onSubmit: (data: any) => Promise<void> // 改为接收 JSON 数据
}

// 常用标签选项
const commonTags = [
  'AI生成',
  '高质量',
  '创意',
  '艺术',
  '抽象',
  '现实主义',
  '科技',
  '自然',
  '人物',
  '风景',
  '建筑',
  '动物',
  '卡通',
  '插画',
  '摄影',
  '设计',
  '概念艺术',
  '数字艺术',
]

export function AssetModal({
  isOpen,
  mode,
  asset,
  onClose,
  onSubmit,
}: AssetModalProps) {
  const {
    scenarioOptions,
    getCategoryByTaskType,
    getMediaTypeByTaskType,
    getFilteredOptions,
  } = useScenarioConfig()
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [customTag, setCustomTag] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitError, setSubmitError] = useState<string | null>(null)

  // 获取当前表单的验证架构
  const getFormSchema = (category?: Category) => {
    // 基础验证字段
    const baseSchema = {
      name: z.string().min(1, '素材名称不能为空'),
      category: z.string().optional(),
      scenario: z.string().min(1, '请选择应用场景'),
      tags: z.string().optional(),
      comments: z.string().optional(),
      rating: z.number().optional(),
      likes_count: z.number().optional(),
      is_original: z.number().int().min(0).max(1).default(0),
      aspect_ratio: z.enum(['16:9', '9:16']).default('16:9'),
    }

    // 根据不同分类设置字段验证规则
    switch (category) {
      case 'text2image':
        return z.object({
          ...baseSchema,
          prompt: z.string().min(1, '提示词不能为空'),
          target_image: z.union([
            z.instanceof(File),
            z.string().url().min(1, '请上传生成图片'),
          ]),
          source_image: z
            .union([
              z.instanceof(File),
              z.string().url(),
              z.literal(''),
              z.undefined(),
            ])
            .optional(),
          video: z
            .union([
              z.instanceof(File),
              z.string().url(),
              z.literal(''),
              z.undefined(),
            ])
            .optional(),
        })

      case 'image2image':
        return z.object({
          ...baseSchema,
          prompt: z.string().optional(),
          source_image: z.union([
            z.instanceof(File),
            z.string().url().min(1, '请上传源图片'),
          ]),
          target_image: z.union([
            z.instanceof(File),
            z.string().url().min(1, '请上传生成图片'),
          ]),
          video: z
            .union([
              z.instanceof(File),
              z.string().url(),
              z.literal(''),
              z.undefined(),
            ])
            .optional(),
        })

      case 'text2video':
        return z.object({
          ...baseSchema,
          prompt: z.string().min(1, '提示词不能为空'),
          video: z.union([
            z.instanceof(File),
            z.string().url().min(1, '请上传生成视频'),
          ]),
          source_image: z
            .union([
              z.instanceof(File),
              z.string().url(),
              z.literal(''),
              z.undefined(),
            ])
            .optional(),
          target_image: z
            .union([
              z.instanceof(File),
              z.string().url(),
              z.literal(''),
              z.undefined(),
            ])
            .optional(),
        })

      case 'image2video':
        return z.object({
          ...baseSchema,
          prompt: z.string().optional(),
          source_image: z.union([
            z.instanceof(File),
            z.string().url().min(1, '请上传源图片'),
          ]),
          video: z.union([
            z.instanceof(File),
            z.string().url().min(1, '请上传生成视频'),
          ]),
          target_image: z
            .union([
              z.instanceof(File),
              z.string().url(),
              z.literal(''),
              z.undefined(),
            ])
            .optional(),
        })

      default:
        // 默认情况：所有字段都是可选的
        return z.object({
          ...baseSchema,
          prompt: z.string().optional(),
          source_image: z
            .union([
              z.instanceof(File),
              z.string().url(),
              z.literal(''),
              z.undefined(),
            ])
            .optional(),
          target_image: z
            .union([
              z.instanceof(File),
              z.string().url(),
              z.literal(''),
              z.undefined(),
            ])
            .optional(),
          video: z
            .union([
              z.instanceof(File),
              z.string().url(),
              z.literal(''),
              z.undefined(),
            ])
            .optional(),
        })
    }
  }

  // 监听 scenario 变化，自动设置 category 和 type
  const [currentCategory, setCurrentCategory] = useState<Category | undefined>(
    asset?.category
  )
  const [currentType, setCurrentType] = useState<MediaType | undefined>(
    asset?.type
  )

  const form = useForm<any>({
    resolver: zodResolver(getFormSchema(currentCategory)),
    defaultValues: {
      name: asset?.name || '',
      category: asset?.category || '',
      scenario: asset?.scenario || '',
      prompt: asset?.prompt || '',
      tags: asset?.tags || '',
      comments: asset?.comments || '',
      rating: asset?.rating || 0,
      likes_count: asset?.likes_count || 0,
      is_original: asset?.is_original || 0,
      aspect_ratio: asset?.aspect_ratio || '16:9',
    },
    mode: 'onChange',
  })

  // 初始化标签
  useEffect(() => {
    if (asset?.tags) {
      const tags = asset.tags
        .split(',')
        .map((tag) => tag.trim())
        .filter(Boolean)
      setSelectedTags(tags)
    }
  }, [asset, isOpen])

  // 当asset变化时，重新设置表单数据
  useEffect(() => {
    if (isOpen && asset) {
      // 重新设置表单值
      form.reset({
        name: asset.name || '',
        category: asset.category || '',
        scenario: asset.scenario || '',
        prompt: asset.prompt || '',
        tags: asset.tags || '',
        comments: asset.comments || '',
        rating: asset.rating || 0,
        likes_count: asset.likes_count || 0,
        // 编辑模式下，设置现有文件的URL
        source_image: mode === 'edit' ? asset.source_image || '' : '',
        target_image: mode === 'edit' ? asset.target_image || '' : '',
        video: mode === 'edit' ? asset.video || '' : '',
      })

      // 设置分类和类型
      setCurrentCategory(asset.category)
      setCurrentType(asset.type)

      // 设置标签
      if (asset.tags) {
        const tags = asset.tags
          .split(',')
          .map((tag) => tag.trim())
          .filter(Boolean)
        setSelectedTags(tags)
      }
    }
  }, [isOpen, asset, mode, form])

  // 监听 scenario 变化
  const watchedScenario = form.watch('scenario')
  useEffect(() => {
    if (watchedScenario) {
      const category = getCategoryByTaskType(watchedScenario)
      const type = getMediaTypeByTaskType(watchedScenario)
      setCurrentCategory(category)
      setCurrentType(type)

      // 清除表单错误，验证器会在下次验证时使用新的 schema
      form.clearErrors()
    }
  }, [watchedScenario, getCategoryByTaskType, getMediaTypeByTaskType, form])

  // 当分类变化时重新设置表单验证器
  useEffect(() => {
    if (currentCategory) {
      const newSchema = getFormSchema(currentCategory)
      // 手动触发验证以应用新的 schema
      form.trigger()
    }
  }, [currentCategory, form])

  // 处理标签添加
  const handleAddTag = (tag: string) => {
    if (tag && !selectedTags.includes(tag)) {
      const newTags = [...selectedTags, tag]
      setSelectedTags(newTags)
      form.setValue('tags', newTags.join(','))
    }
    setCustomTag('')
  }

  // 处理标签移除
  const handleRemoveTag = (tagToRemove: string) => {
    const newTags = selectedTags.filter((tag) => tag !== tagToRemove)
    setSelectedTags(newTags)
    form.setValue('tags', newTags.join(','))
  }

  // 处理表单提交
  const handleSubmit = async (data: any) => {
    console.log('=== AssetModal 表单提交开始 ===')
    console.log('模式:', mode)
    console.log('当前分类:', currentCategory)
    console.log('当前类型:', currentType)
    console.log('表单数据:', data)
    console.log('表单状态:', {
      isValid: form.formState.isValid,
      isDirty: form.formState.isDirty,
      isSubmitting: form.formState.isSubmitting,
      isValidating: form.formState.isValidating,
      submitCount: form.formState.submitCount,
    })
    console.log('表单错误:', form.formState.errors)
    console.log('表单所有值:', form.getValues())

    if (mode === 'view') return

    setSubmitError(null)
    setIsSubmitting(true)
    try {
      // 构建JSON数据
      const submitData: any = {
        name: data.name,
        scenario: data.scenario,
        prompt: data.prompt,
        tags: data.tags,
        comments: data.comments,
        rating: data.rating,
        likes_count: data.likes_count,
        is_original: data.is_original || 0,
        aspect_ratio: data.aspect_ratio || '16:9',
        user_id: '00000000-0000-0000-0000-000000000000', // 模拟用户ID
        // 文件字段现在都是URL字符串
        source_image: data.source_image || null,
        target_image: data.target_image || null,
        video: data.video || null,
      }

      // 如果是编辑模式，添加ID
      if (mode === 'edit' && asset) {
        submitData.id = asset.id
      }

      console.log('提交数据:', submitData)

      await onSubmit(submitData)
      console.log('提交成功，关闭弹窗')
      // onClose() 现在在 page.tsx 的 handleModalSubmit 中调用
    } catch (error) {
      console.error('提交失败:', error)
      setSubmitError(
        error instanceof Error ? error.message : '提交失败，请重试'
      )
    } finally {
      setIsSubmitting(false)
    }
  }

  // 获取当前分类对应的场景选项
  const currentScenarioOptions = getFilteredOptions(currentCategory)

  // 获取表单标题
  const getModalTitle = () => {
    switch (mode) {
      case 'edit':
        return '编辑素材'
      case 'view':
        return '查看素材'
      default:
        return '素材详情'
    }
  }

  // 检查字段是否必填
  const isFieldRequired = (fieldName: string) => {
    if (!currentCategory) return false

    const requiredFields = {
      text2image: ['prompt', 'target_image'],
      image2image: ['target_image', 'source_image'],
      text2video: ['prompt', 'video'],
      image2video: ['source_image', 'video'],
    }

    return requiredFields[currentCategory]?.includes(fieldName) || false
  }

  // 渲染文件预览
  const renderFilePreview = (
    file: File | string | undefined,
    type: 'image' | 'video'
  ) => {
    if (!file) return null

    const isUrl = typeof file === 'string'
    const src = isUrl ? file : URL.createObjectURL(file)

    return (
      <div className="relative w-full h-40 bg-gray-100 rounded-lg overflow-hidden">
        {type === 'image' ? (
          <img
            src={src}
            alt="预览"
            className="w-full h-full object-cover"
            onError={(e) => {
              const target = e.target as HTMLImageElement
              target.style.display = 'none'
            }}
          />
        ) : (
          <video
            src={src}
            className="w-full h-full object-cover"
            controls
            muted
            onError={(e) => {
              const target = e.target as HTMLVideoElement
              target.style.display = 'none'
            }}
          />
        )}
        {mode === 'view' && (
          <div className="absolute top-2 right-2 flex space-x-1">
            <Button
              size="sm"
              variant="secondary"
              onClick={() => window.open(src, '_blank')}
            >
              <Eye className="h-4 w-4" />
            </Button>
            <Button
              size="sm"
              variant="secondary"
              onClick={() => {
                const a = document.createElement('a')
                a.href = src
                a.download = 'asset-file'
                a.click()
              }}
            >
              <Download className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={() => {}}>
      <DialogContent className="max-h-[95vh] bg-white flex flex-col max-w-7xl">
        <DialogHeader className="bg-white border-b pb-2 flex-shrink-0 relative">
          <DialogTitle className="text-xl font-semibold text-gray-900">
            {getModalTitle()}
          </DialogTitle>
          <DialogDescription className="text-gray-600">
            {mode === 'edit' && '修改素材信息'}
            {mode === 'view' && '查看素材详细信息'}
          </DialogDescription>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="absolute top-2 right-2 h-8 w-8 p-0 hover:bg-gray-100"
          >
            <XIcon className="h-4 w-4" />
          </Button>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="flex-1 flex flex-col bg-white overflow-auto"
          >
            <div className="flex-1 overflow-y-auto p-2">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* 左侧：基本信息 */}
                <div className="space-y-2 bg-white">
                  {/* 素材名称 */}
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex whitespace-nowrap items-center text-gray-800 font-semibold text-sm">
                          素材名称
                          <span className="text-red-600 ml-1 font-bold">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="请输入素材名称"
                            disabled={mode === 'view'}
                            className="h-9 bg-white border-2 border-gray-300 focus:border-blue-700 focus:ring-2 focus:ring-blue-700 text-gray-900 placeholder-gray-500"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* 分类选择 */}
                  <FormField
                    control={form.control}
                    name="category"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center text-gray-800 font-semibold text-sm">
                          分类
                        </FormLabel>
                        <Select
                          onValueChange={(value) => {
                            field.onChange(value === 'all' ? undefined : value)
                            // 清空应用场景选择
                            form.setValue('scenario', '')
                          }}
                          value={field.value || 'all'}
                          disabled={mode === 'view'}
                        >
                          <FormControl>
                            <SelectTrigger className="h-9 bg-white border-gray-300 focus:border-blue-700 focus:ring-blue-700 text-gray-900">
                              <SelectValue
                                placeholder="请选择分类"
                                className="text-gray-500"
                              />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent className="bg-white border border-gray-200 shadow-lg">
                            <SelectItem
                              value="all"
                              className="bg-white hover:bg-blue-50 focus:bg-blue-700 focus:text-white text-gray-900 cursor-pointer"
                            >
                              全部分类
                            </SelectItem>
                            <SelectItem
                              value="text2image"
                              className="bg-white hover:bg-blue-50 focus:bg-blue-700 focus:text-white text-gray-900 cursor-pointer"
                            >
                              text2image
                            </SelectItem>
                            <SelectItem
                              value="image2image"
                              className="bg-white hover:bg-blue-50 focus:bg-blue-700 focus:text-white text-gray-900 cursor-pointer"
                            >
                              image2image
                            </SelectItem>
                            <SelectItem
                              value="text2video"
                              className="bg-white hover:bg-blue-50 focus:bg-blue-700 focus:text-white text-gray-900 cursor-pointer"
                            >
                              text2video
                            </SelectItem>
                            <SelectItem
                              value="image2video"
                              className="bg-white hover:bg-blue-50 focus:bg-blue-700 focus:text-white text-gray-900 cursor-pointer"
                            >
                              image2video
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* 应用场景 */}
                  <FormField
                    control={form.control}
                    name="scenario"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center text-gray-800 font-semibold text-sm">
                          应用场景
                          <span className="text-red-600 ml-1 font-bold">*</span>
                        </FormLabel>
                        <Select
                          onValueChange={(value) => {
                            field.onChange(value)
                            // 如果分类未选中，根据应用场景自动设置分类
                            const selectedScenario = scenarioOptions.find(
                              (opt) => opt.taskType === value
                            )
                            if (
                              selectedScenario &&
                              !form.getValues('category')
                            ) {
                              form.setValue(
                                'category',
                                selectedScenario.category
                              )
                            }
                          }}
                          value={field.value || ''}
                          disabled={mode === 'view'}
                        >
                          <FormControl>
                            <SelectTrigger className="h-9 bg-white border-gray-300 focus:border-blue-700 focus:ring-blue-700 text-gray-900">
                              <SelectValue
                                placeholder="请选择应用场景"
                                className=" text-gray-500"
                              />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent className=" bg-white border border-gray-200 shadow-lg">
                            {(() => {
                              const selectedCategory =
                                form.getValues('category')
                              const filteredOptions = selectedCategory
                                ? scenarioOptions.filter(
                                    (opt) => opt.category === selectedCategory
                                  )
                                : scenarioOptions
                              return filteredOptions.map((option) => (
                                <SelectItem
                                  key={option.taskType}
                                  value={option.taskType}
                                  className="bg-white hover:bg-blue-50 focus:bg-blue-700 focus:text-white text-gray-900 cursor-pointer"
                                >
                                  {option.pathname}
                                </SelectItem>
                              ))
                            })()}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* 提示词 - 所有分类都显示，但只有text2image和text2video是必填 */}
                  <FormField
                    control={form.control}
                    name="prompt"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center text-gray-800 font-semibold text-sm">
                          提示词{' '}
                          {(currentCategory === 'text2image' ||
                            currentCategory === 'text2video') && (
                            <span className="text-red-600 ml-1 font-bold">
                              *
                            </span>
                          )}
                        </FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            placeholder="请输入AI生成的提示词"
                            disabled={mode === 'view'}
                            rows={2}
                            className="bg-white border border-gray-300 focus:border-blue-700 focus:ring-blue-700 text-gray-900 placeholder:text-gray-500 resize-none"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* 评分和点赞数 */}
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="rating"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center text-gray-800 font-semibold text-sm">
                            <Star className="h-4 w-4 mr-1 text-yellow-500 fill-yellow-500" />
                            评分
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              type="number"
                              min="0"
                              max="5"
                              step="0.1"
                              placeholder="4.8"
                              disabled={mode === 'view'}
                              className="h-9 bg-white border-2 border-gray-300 focus:border-blue-700 focus:ring-2 focus:ring-blue-700 text-gray-900 placeholder-gray-500"
                              onChange={(e) => {
                                const value = parseFloat(e.target.value)
                                if (value >= 0 && value <= 5) {
                                  field.onChange(value)
                                }
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="likes_count"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center text-gray-800 font-semibold text-sm">
                            <Heart className="h-4 w-4 mr-1 text-red-500 fill-red-500" />
                            点赞数
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              type="number"
                              min="0"
                              placeholder="100"
                              disabled={mode === 'view'}
                              className="h-9 bg-white border-2 border-gray-300 focus:border-blue-700 focus:ring-2 focus:ring-blue-700 text-gray-900 placeholder-gray-500"
                              onChange={(e) =>
                                field.onChange(Number(e.target.value))
                              }
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* 原创性选择 */}
                  <FormField
                    control={form.control}
                    name="is_original"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border border-gray-300 p-3 bg-white">
                        <div className="space-y-0.5">
                          <FormLabel className="text-gray-800 font-semibold text-sm">
                            原创作品
                          </FormLabel>
                          <div className="text-xs text-gray-600">
                            标记此作品是否为原创内容
                          </div>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value === 1}
                            onCheckedChange={(checked) =>
                              field.onChange(checked ? 1 : 0)
                            }
                            disabled={mode === 'view'}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  {/* 素材比例选择 */}
                  <FormField
                    control={form.control}
                    name="aspect_ratio"
                    render={({ field }) => (
                      <FormItem className="flex items-center space-x-4">
                        <FormLabel className="text-gray-800 font-semibold text-sm w-20 flex-shrink-0">
                          素材比例
                        </FormLabel>
                        <div className="flex-1">
                          <div className="flex space-x-4">
                            {/* 16:9 横屏 */}
                            <div
                              className={`cursor-pointer p-3 border-2 rounded-lg transition-all ${
                                field.value === '16:9'
                                  ? 'border-blue-500 bg-blue-50'
                                  : 'border-gray-300 hover:border-gray-400'
                              } ${
                                mode === 'view' ? 'pointer-events-none' : ''
                              }`}
                              onClick={() =>
                                mode !== 'view' && field.onChange('16:9')
                              }
                            >
                              <div className="flex flex-col items-center space-y-2">
                                <div className="w-12 h-7 bg-gray-400 rounded border"></div>
                                <span className="text-xs text-gray-600">
                                  16:9 横屏
                                </span>
                              </div>
                            </div>

                            {/* 9:16 竖屏 */}
                            <div
                              className={`cursor-pointer p-3 border-2 rounded-lg transition-all ${
                                field.value === '9:16'
                                  ? 'border-blue-500 bg-blue-50'
                                  : 'border-gray-300 hover:border-gray-400'
                              } ${
                                mode === 'view' ? 'pointer-events-none' : ''
                              }`}
                              onClick={() =>
                                mode !== 'view' && field.onChange('9:16')
                              }
                            >
                              <div className="flex flex-col items-center space-y-2">
                                <div className="w-7 h-12 bg-gray-400 rounded border"></div>
                                <span className="text-xs text-gray-600">
                                  9:16 竖屏
                                </span>
                              </div>
                            </div>
                          </div>
                          <FormMessage />
                        </div>
                      </FormItem>
                    )}
                  />

                  {/* 标签 */}
                  <div className="space-y-2">
                    <Label className="flex items-center text-gray-800 font-semibold text-sm">
                      <Tag className="h-4 w-4 mr-1 text-blue-600" />
                      标签
                    </Label>

                    {/* 已选标签 */}
                    {selectedTags.length > 0 && (
                      <div className="flex flex-wrap gap-2">
                        {selectedTags.map((tag) => (
                          <Badge
                            key={tag}
                            className=" text-white bg-blue-700 cursor-pointer font-medium border border-blue-300"
                          >
                            {tag}
                            {mode !== 'view' && (
                              <X
                                className="h-3 w-3 ml-1 hover:text-red-600"
                                onClick={() => handleRemoveTag(tag)}
                              />
                            )}
                          </Badge>
                        ))}
                      </div>
                    )}

                    {/* 常用标签选择 */}
                    {mode !== 'view' && (
                      <>
                        <div className="flex flex-wrap gap-2">
                          {commonTags
                            .filter((tag) => !selectedTags.includes(tag))
                            .map((tag) => (
                              <Badge
                                key={tag}
                                className="bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-700 cursor-pointer font-medium border border-gray-300 hover:border-blue-400"
                                onClick={() => handleAddTag(tag)}
                              >
                                + {tag}
                              </Badge>
                            ))}
                        </div>

                        {/* 自定义标签输入 */}
                        <div className="flex space-x-2">
                          <Input
                            placeholder="输入自定义标签"
                            value={customTag}
                            onChange={(e) => setCustomTag(e.target.value)}
                            onKeyPress={(e) => {
                              if (e.key === 'Enter') {
                                e.preventDefault()
                                handleAddTag(customTag)
                              }
                            }}
                            className="h-9 bg-white border-2 border-gray-300 focus:border-blue-700 focus:ring-2 focus:ring-blue-700 text-gray-900 placeholder-gray-500 resize-none"
                          />
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => handleAddTag(customTag)}
                            disabled={!customTag.trim()}
                            className="bg-blue-600 text-white hover:bg-blue-700 border-blue-600 font-medium w-24"
                          >
                            添加
                          </Button>
                        </div>
                      </>
                    )}
                  </div>

                  {/* 备注 */}
                  <FormField
                    control={form.control}
                    name="comments"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-gray-800 font-semibold text-sm">
                          评论
                        </FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            placeholder="添加备注信息"
                            disabled={mode === 'view'}
                            rows={2}
                            className="bg-white border-2 border-gray-300 focus:border-blue-700 focus:ring-2 focus:ring-blue-700 text-gray-900 placeholder-gray-500 resize-none"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* 右侧：文件上传/预览 */}
                <div className="space-y-4 bg-white">
                  {/* 资源类型显示 */}
                  {currentType && (
                    <div className="flex space-x-4">
                      <div>
                        <Label className="text-gray-800 font-semibold text-sm">
                          资源类型
                        </Label>
                        <div className="mt-1">
                          <Badge
                            className={
                              currentType === 'image'
                                ? 'bg-green-600 text-white font-medium px-3 py-1 text-sm'
                                : 'bg-purple-600 text-white font-medium px-3 py-1 text-sm'
                            }
                          >
                            {currentType === 'image' ? '图片' : '视频'}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* 源图片上传 */}
                  {(currentCategory === 'image2image' ||
                    currentCategory === 'image2video') && (
                    <div>
                      <Label className="flex items-center text-gray-800 font-semibold text-sm">
                        源图片{' '}
                        {isFieldRequired('source_image') && (
                          <span className="text-red-600 ml-1 font-bold">*</span>
                        )}
                      </Label>
                      {mode === 'view' ? (
                        renderFilePreview(asset?.source_image, 'image')
                      ) : (
                        <div className="space-y-2">
                          {/* 显示现有文件预览 */}
                          {mode === 'edit' && asset?.source_image && (
                            <div className="mb-2">
                              <p className="text-sm text-gray-600 mb-1">
                                当前文件：
                              </p>
                              {renderFilePreview(asset.source_image, 'image')}
                            </div>
                          )}

                          <FormField
                            control={form.control}
                            name="source_image"
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <FileUpload
                                    accept="image/*"
                                    multiple={false}
                                    maxSize={10 * 1024 * 1024} // 10MB
                                    onFileChange={(files: File[]) =>
                                      field.onChange(files[0])
                                    }
                                    className="mt-2"
                                  />
                                </FormControl>
                                <FormMessage />
                                <div className="text-xs text-gray-600 mt-1 font-medium">
                                  支持拖拽、粘贴(Ctrl+V)、点击选择等方式上传
                                  {mode === 'edit' && asset?.source_image && (
                                    <span className="block text-orange-600">
                                      选择新文件将替换当前文件
                                    </span>
                                  )}
                                </div>
                              </FormItem>
                            )}
                          />
                        </div>
                      )}
                    </div>
                  )}

                  {/* 目标图片上传 */}
                  {(currentCategory === 'text2image' ||
                    currentCategory === 'image2image') && (
                    <div>
                      <Label className="flex items-center text-gray-800 font-semibold text-sm">
                        生成图片{' '}
                        {isFieldRequired('target_image') && (
                          <span className="text-red-600 ml-1 font-bold">*</span>
                        )}
                      </Label>
                      {mode === 'view' ? (
                        renderFilePreview(asset?.target_image, 'image')
                      ) : (
                        <div className="space-y-2">
                          {/* 显示现有文件预览 */}
                          {mode === 'edit' && asset?.target_image && (
                            <div className="mb-2">
                              <p className="text-sm text-gray-600 mb-1">
                                当前文件：
                              </p>
                              {renderFilePreview(asset.target_image, 'image')}
                            </div>
                          )}

                          <FormField
                            control={form.control}
                            name="target_image"
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <FileUpload
                                    accept="image/*"
                                    multiple={false}
                                    maxSize={10 * 1024 * 1024} // 10MB
                                    onFileChange={(files: File[]) =>
                                      field.onChange(files[0])
                                    }
                                    className="mt-2"
                                  />
                                </FormControl>
                                <FormMessage />
                                <div className="text-xs text-gray-600 mt-1 font-medium">
                                  支持拖拽、粘贴(Ctrl+V)、点击选择等方式上传
                                  {mode === 'edit' && asset?.target_image && (
                                    <span className="block text-orange-600">
                                      选择新文件将替换当前文件
                                    </span>
                                  )}
                                </div>
                              </FormItem>
                            )}
                          />
                        </div>
                      )}
                    </div>
                  )}

                  {/* 视频上传 */}
                  {(currentCategory === 'text2video' ||
                    currentCategory === 'image2video') && (
                    <div>
                      <Label className="flex items-center text-gray-800 font-semibold text-sm">
                        生成视频{' '}
                        {isFieldRequired('video') && (
                          <span className="text-red-600 ml-1 font-bold">*</span>
                        )}
                      </Label>
                      {mode === 'view' ? (
                        renderFilePreview(asset?.video, 'video')
                      ) : (
                        <div className="space-y-2">
                          {/* 显示现有文件预览 */}
                          {mode === 'edit' && asset?.video && (
                            <div className="mb-2">
                              <p className="text-sm text-gray-600 mb-1">
                                当前文件：
                              </p>
                              {renderFilePreview(asset.video, 'video')}
                            </div>
                          )}

                          <FormField
                            control={form.control}
                            name="video"
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <FileUpload
                                    accept="video/*"
                                    multiple={false}
                                    maxSize={100 * 1024 * 1024} // 100MB
                                    onFileChange={(files: File[]) =>
                                      field.onChange(files[0])
                                    }
                                    className="mt-2"
                                  />
                                </FormControl>
                                <FormMessage />
                                <div className="text-xs text-gray-600 mt-1 font-medium">
                                  支持拖拽、粘贴(Ctrl+V)、点击选择等方式上传
                                  {mode === 'edit' && asset?.video && (
                                    <span className="block text-orange-600">
                                      选择新文件将替换当前文件
                                    </span>
                                  )}
                                </div>
                              </FormItem>
                            )}
                          />
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* 错误显示 */}
            {submitError && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3 mx-4">
                <p className="text-red-800 text-sm">{submitError}</p>
              </div>
            )}

            {/* 验证状态显示 (开发调试用) */}
            {process.env.NODE_ENV === 'development' &&
              Object.keys(form.formState.errors).length > 0 && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 mx-4">
                  <p className="text-yellow-800 text-sm font-semibold mb-2">
                    表单验证错误:
                  </p>
                  {Object.entries(form.formState.errors).map(
                    ([field, error]) => (
                      <p key={field} className="text-yellow-700 text-xs">
                        {field}: {(error as any)?.message || '验证失败'}
                      </p>
                    )
                  )}
                </div>
              )}

            <DialogFooter className="bg-white border-t border-gray-200 pt-4 flex gap-3 flex-shrink-0">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                size="default"
                className="border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                {mode === 'view' ? '关闭' : '取消'}
              </Button>
              {mode !== 'view' && (
                <Button
                  type="submit"
                  variant="primary"
                  size="default"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? '提交中...' : '保存更改'}
                </Button>
              )}
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
