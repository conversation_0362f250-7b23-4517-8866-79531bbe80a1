'use client'

import { useState } from 'react'
import { Input } from '@ui/components/input'
import { Button } from '@ui/components/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@ui/components/select'
import { Badge } from '@ui/components/badge'
import { Calendar } from '@ui/components/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@ui/components/popover'
import { CalendarIcon, Search, Plus, Tag } from 'lucide-react'
import { format } from 'date-fns'
import { cn } from '@ui/lib'
import {
  ListAssetsRequest,
  Category,
} from '../../../../api/assets-manager/lib/types'
import { useScenarioConfig } from '../hooks/useScenarioConfig'
import { TaskType } from '@/types'

interface SearchFiltersProps {
  filters: ListAssetsRequest
  onFiltersChange: (filters: ListAssetsRequest) => void
  onSearch: () => void
  onReset: () => void
  onCreateAsset: () => void
  isLoading?: boolean
}

// 分类选项
const categoryOptions: { value: Category; label: string }[] = [
  { value: 'text2image', label: 'text2image' },
  { value: 'image2image', label: 'image2image' },
  { value: 'text2video', label: 'text2video' },
  { value: 'image2video', label: 'image2video' },
]

export function SearchFilters({
  filters,
  onFiltersChange,
  onSearch,
  onReset,
  onCreateAsset,
  isLoading = false,
}: SearchFiltersProps) {
  const { scenarioOptions, getFilteredOptions } = useScenarioConfig()
  const [dateFrom, setDateFrom] = useState<Date>()
  const [dateTo, setDateTo] = useState<Date>()
  const [selectedTags, setSelectedTags] = useState<string[]>([])

  // 常用标签选项
  const commonTags = [
    'AI生成',
    '高质量',
    '创意',
    '艺术',
    '抽象',
    '现实主义',
    '科技',
    '自然',
    '人物',
    '风景',
    '建筑',
    '动物',
    '卡通',
    '插画',
    '摄影',
    '设计',
  ]

  // 处理日期范围变化
  const handleDateRangeChange = (from?: Date, to?: Date) => {
    setDateFrom(from)
    setDateTo(to)

    if (from && to) {
      onFiltersChange({
        ...filters,
        created_at: {
          start: from.toISOString(),
          end: to.toISOString(),
        },
      })
    } else {
      const { created_at, ...restFilters } = filters
      onFiltersChange(restFilters)
    }
  }

  // 处理标签选择
  const handleTagToggle = (tag: string) => {
    const newTags = selectedTags.includes(tag)
      ? selectedTags.filter((t) => t !== tag)
      : [...selectedTags, tag]

    setSelectedTags(newTags)
    // TODO: 需要在 ListAssetsRequest 类型中添加 tags 字段
    // onFiltersChange({
    //   ...filters,
    //   tags: newTags.length > 0 ? newTags : undefined,
    // })
  }

  // 获取当前选中分类对应的场景选项
  const currentScenarioOptions = getFilteredOptions(filters.category)

  return (
    <div className="space-y-3 p-3 bg-white rounded-lg border border-gray-200 shadow-sm">
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 gap-3">
        {/* 分类选择 */}
        <div className="space-y-1">
          <label className="text-xs font-semibold text-gray-900">分类</label>
          <Select
            value={filters.category || 'all'}
            onValueChange={(value) =>
              onFiltersChange({
                ...filters,
                category: value === 'all' ? undefined : (value as Category),
                scenario: undefined, // 重置场景选择
              })
            }
          >
            <SelectTrigger className="bg-white border-gray-300 focus:border-blue-700 focus:ring-blue-700 text-gray-900">
              <SelectValue placeholder="请选择分类" className="text-gray-500" />
            </SelectTrigger>
            <SelectContent className="bg-white border border-gray-200 shadow-lg">
              {categoryOptions.map((option) => (
                <SelectItem
                  key={option.value}
                  value={option.value}
                  className="bg-white hover:bg-blue-50 focus:bg-blue-700 focus:text-white text-gray-900 cursor-pointer"
                >
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* 场景选择 */}
        <div className="space-y-1">
          <label className="text-xs font-semibold text-gray-900">
            应用场景
          </label>
          <Select
            value={filters.scenario || 'all'}
            onValueChange={(value: string) =>
              onFiltersChange({
                ...filters,
                scenario: value === 'all' ? undefined : (value as TaskType),
              })
            }
          >
            <SelectTrigger className="bg-white border-gray-300 focus:border-blue-700 focus:ring-blue-700 text-gray-900">
              <SelectValue
                placeholder="请选择场景"
                className="text-gray-500 placeholder:text-gray-500"
              />
            </SelectTrigger>
            <SelectContent className="bg-white border border-gray-200 shadow-lg">
              {currentScenarioOptions.map((option) => (
                <SelectItem
                  key={option.taskType}
                  value={option.taskType}
                  className="bg-white hover:bg-blue-50 focus:bg-blue-700 focus:text-white text-gray-900 cursor-pointer"
                >
                  {option.pathname}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* 操作按钮 */}
        <div className="space-y-1">
          <label className="text-xs font-semibold text-gray-900 opacity-0">操作</label>
          <div className="flex gap-2">
            <Button
              onClick={onSearch}
              variant="primary"
              size="default"
              disabled={isLoading}
              className="flex-1"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  搜索中...
                </>
              ) : (
                <>
                  <Search className="mr-2 h-4 w-4" />
                  搜索
                </>
              )}
            </Button>
          </div>
        </div>


        {/* 标签筛选 */}
        {/* <div className="space-y-2 md:col-span-2 lg:col-span-3 xl:col-span-4">
          <label className="text-sm font-semibold text-gray-900 flex items-center">
            <Tag className="h-4 w-4 mr-1 text-blue-600" />
            标签筛选
          </label>
          <div className="flex flex-wrap gap-2">
            {commonTags.map((tag) => (
              <Badge
                key={tag}
                variant={selectedTags.includes(tag) ? 'default' : 'outline'}
                className={cn(
                  'cursor-pointer transition-colors',
                  selectedTags.includes(tag)
                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                    : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                )}
                onClick={() => handleTagToggle(tag)}
              >
                {tag}
              </Badge>
            ))}
          </div>
        </div> */}

        {/* 创建时间范围 */}
        {/* <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700 flex items-center">
            <CalendarIcon className="h-4 w-4 mr-1" />
            创建时间
          </label>
          <div className="flex space-x-2">
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    'flex-1 justify-start text-left font-normal text-sm',
                    !dateFrom && 'text-muted-foreground'
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {dateFrom ? format(dateFrom, 'PPP') : '开始日期'}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={dateFrom}
                  onSelect={setDateFrom}
                  initialFocus
                  captionLayout="dropdown"
                />
              </PopoverContent>
            </Popover>

            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    'flex-1 justify-start text-left font-normal text-sm',
                    !dateTo && 'text-muted-foreground'
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {dateTo ? format(dateTo, 'PPP') : '结束日期'}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={dateTo}
                  onSelect={(date) => handleDateRangeChange(dateFrom, date)}
                  initialFocus
                  captionLayout="dropdown"
                />
              </PopoverContent>
            </Popover>
          </div>
        </div> */}

        {/* 排序设置 */}
        {/* <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">排序方式</label>
          <div className="flex space-x-2">
            <Select
              value={filters.sortBy}
              onValueChange={(value) =>
                onFiltersChange({
                  ...filters,
                  sortBy: value as any,
                })
              }
            >
              <SelectTrigger className="flex-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="created_at">创建时间</SelectItem>
                <SelectItem value="updated_at">更新时间</SelectItem>
                <SelectItem value="rating">评分</SelectItem>
                <SelectItem value="likes_count">点赞数</SelectItem>
                <SelectItem value="name">名称</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.sortOrder}
              onValueChange={(value) =>
                onFiltersChange({
                  ...filters,
                  sortOrder: value as 'asc' | 'desc',
                })
              }
            >
              <SelectTrigger className="w-20">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="desc">降序</SelectItem>
                <SelectItem value="asc">升序</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div> */}
      </div>

      {/* 活动过滤器展示 */}
      <div className="flex flex-wrap gap-1">
        {filters.category && (
          <Badge
            variant="secondary"
            className="cursor-pointer"
            onClick={() => onFiltersChange({ ...filters, category: undefined })}
          >
            分类:{' '}
            {categoryOptions.find((c) => c.value === filters.category)?.label}
            <span className="ml-1">×</span>
          </Badge>
        )}
        {filters.scenario && (
          <Badge
            variant="secondary"
            className="cursor-pointer"
            onClick={() => onFiltersChange({ ...filters, scenario: undefined })}
          >
            场景:{' '}
            {
              scenarioOptions.find((s) => s.taskType === filters.scenario)
                ?.pathname
            }
            <span className="ml-1">×</span>
          </Badge>
        )}
        {filters.created_at && (
          <Badge
            variant="secondary"
            className="cursor-pointer"
            onClick={() => handleDateRangeChange()}
          >
            时间范围
            <span className="ml-1">×</span>
          </Badge>
        )}
        {/* {selectedTags.length > 0 && (
          <Badge
            variant="secondary"
            className="cursor-pointer"
            onClick={() => {
              setSelectedTags([])
              // TODO: 需要在 ListAssetsRequest 类型中添加 tags 字段
              // onFiltersChange({ ...filters, tags: undefined })
            }}
          >
            标签: {selectedTags.join(', ')}
            <span className="ml-1">×</span>
          </Badge>
        )} */}
      </div>
    </div>
  )
}
