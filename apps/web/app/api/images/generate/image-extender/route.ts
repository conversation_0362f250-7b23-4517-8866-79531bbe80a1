import { NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { getUserFromServerCookies } from '../../../../utils/server-cookies'
import { PointsService } from '../../../../services/points'
import { createHistoryRecordGeneric } from '../../../aiimage/utils/history'
import { getApiPoints } from '@shared/lib/point/point-config-service'

const API_URL = 'https://kieai.erweima.ai/api/v1/gpt4o-image/generate'
const AUTH_TOKEN = process.env.AI_AUTH_TOKEN

// 扩图提示词生成函数
function buildExpansionPrompt(aspectRatio: string): string {
  const templates = {
    ratioExpansion:
      'Expand this image to {ratio}, keeping all original content visible. Ensure the style of the filled areas matches the original image.',
    scaleExpansion:
      'Upscale to the target resolution without cropping or covering any part. The newly filled regions should seamlessly blend with the original style, color, and lighting.',
    general:
      "When extending the canvas, make sure the completion is consistent with the original image's style and details.",
  }

  let basePrompt: string

  if (aspectRatio === '1.2x') {
    basePrompt = templates.scaleExpansion
  } else {
    basePrompt = templates.ratioExpansion.replace('{ratio}', aspectRatio)
  }

  // 添加通用指导
  basePrompt += ` ${templates.general}`

  return basePrompt
}

export async function POST(request: Request) {
  try {
    const body = await request.json()

    // 验证必需参数
    if (
      !body.filesUrl ||
      !Array.isArray(body.filesUrl) ||
      body.filesUrl.length === 0
    ) {
      return NextResponse.json(
        {
          code: 400000,
          message: {
            en: 'Image URL is required for image extension.',
          },
        },
        { status: 400 }
      )
    }

    if (!body.aspectRatio) {
      return NextResponse.json(
        {
          code: 400000,
          message: {
            en: 'Aspect ratio is required for image extension.',
          },
        },
        { status: 400 }
      )
    }

    // 从 cookies 获取用户信息
    const cookieStore = await cookies()
    const user = await getUserFromServerCookies(cookieStore)

    if (!user) {
      return NextResponse.json(
        {
          code: 401000,
          message: {
            en: 'Please login first.',
          },
        },
        { status: 401 }
      )
    }

    // 确保 nVariants 参数是有效的值
    if (body.nVariants && !['1', '2', '3', '4'].includes(body.nVariants)) {
      body.nVariants = '1' // 默认为1
    }

    // 验证用户积分
    const validationResult = await PointsService.validateAndConsumePoints(
      user,
      getApiPoints('image-extender', body) * Number(body.nVariants),
      'Image extension'
    )

    if (!validationResult.success) {
      return NextResponse.json(
        {
          code: validationResult.code,
          message: validationResult.message,
        },
        { status: validationResult.code === 401000 ? 401 : 400 }
      )
    }

    // 生成扩图专用的提示词
    const expansionPrompt = buildExpansionPrompt(body.aspectRatio)

    console.log('Image extension request:', {
      aspectRatio: body.aspectRatio,
      nVariants: body.nVariants,
      filesUrl: body.filesUrl,
      generatedPrompt: expansionPrompt,
    })

    const response = await fetch(API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
        Authorization: `Bearer ${AUTH_TOKEN}`,
      },
      body: JSON.stringify({
        ...body,
        prompt: expansionPrompt, // 使用生成的扩图提示词
        mode: body.mode || 'image-to-image', // 确保模式正确
        enableFallback: true,
      }),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`)
    }

    const data = await response.json()

    if (data.code === 200 && validationResult.data?.points !== undefined) {
      // 消耗积分
      const consumeResult = await PointsService.consumePoints(
        user,
        getApiPoints('image-extender', body),
        'Image extension points consumption',
        validationResult.data?.points
      )

      if (!consumeResult.success) {
        console.error('Failed to consume points:', consumeResult.message.en)
        // 即使积分扣除失败，我们仍然返回成功，因为生成任务已经创建
        // 这种情况已经记录日志，后续可以人工处理
      }
    }

    console.log('body===========', body)

    // 创建历史记录
    let historyRecord = null
    if (data.code === 200 && data.data?.taskId) {
      try {
        historyRecord = await createHistoryRecordGeneric(
          String(user.id || user.email),
          data.data.taskId,
          'image-extender',
          'KieAI/image-extender',
          {
            aspectRatio: body.aspectRatio,
            nVariants: body.nVariants || '1',
            mode: body.mode || 'image-to-image',
            prompt: expansionPrompt,
            originalImageUrls: body.filesUrl,
          }
        )
        console.log(
          'Image extension history record created:',
          historyRecord?.id
        )
      } catch (historyError) {
        console.error(
          'Failed to create image-extender history record:',
          historyError
        )
      }
    }

    // 返回响应，保持与其他接口一致的格式
    return NextResponse.json(data)
  } catch (error) {
    console.error('Image extension API error:', error)

    let errorMessage = 'Internal server error occurred during image extension'
    if (error instanceof Error) {
      errorMessage = error.message
    }

    return NextResponse.json(
      {
        code: 500000,
        message: {
          en: errorMessage,
        },
      },
      { status: 500 }
    )
  }
}
