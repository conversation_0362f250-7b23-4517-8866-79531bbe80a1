import { NextRequest, NextResponse } from 'next/server'
import { OAuth2Client } from 'google-auth-library'
import { supabase } from '@/lib/supabaseClient'
import { setCookie } from '@/utils/lib/cookie'
//
const client = new OAuth2Client(process.env.GOOGLE_CLIENT_ID)

interface GoogleUser {
  sub: string
  email: string
  name: string
  picture: string
  email_verified: boolean
}

async function updateUser(username: string, email: string, avatarUrl: string) {
  // 检查用户是否已存在
  const { data: existingUser, error: selectError } = await supabase
    .from('img4o_user')
    .select('*')
    .eq('email', email)
    .single()

  console.log('existingUser', existingUser)

  if (existingUser) {
    const { data: updatedUser, error: updateError } = await supabase
      .from('img4o_user')
      .update({
        last_login_time: new Date().toISOString(),
        email: email || existingUser.email,
        avatar_url: avatarUrl || existingUser.avatar_url,
      })
      .eq('email', email)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating user:', updateError)
      throw updateError
    }

    return updatedUser
  }

  const { data: newUser, error: insertError } = await supabase
    .from('img4o_user')
    .insert([
      {
        username,
        email,
        avatar_url: avatarUrl,
        last_login_time: new Date().toISOString(),
        points: 8,
      },
    ])
    .select()
    .single()

  if (insertError) {
    console.error('Error creating user:', insertError)
    throw insertError
  }

  return newUser
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { credential } = body

    if (!credential) {
      return NextResponse.json({ error: 'Missing credential' }, { status: 400 })
    }

    // 验证JWT token
    const ticket = await client.verifyIdToken({
      idToken: credential,
      audience: process.env.GOOGLE_CLIENT_ID,
    })

    const payload = ticket.getPayload()
    if (!payload) {
      return NextResponse.json(
        { error: 'Invalid token payload' },
        { status: 400 }
      )
    }

    const googleUser: GoogleUser = {
      sub: payload.sub,
      email: payload.email!,
      name: payload.name!,
      picture: payload.picture!,
      email_verified: payload.email_verified!,
    }

    console.log('Google One Tap user:', googleUser)

    // 更新或创建用户
    const user = await updateUser(
      googleUser.name,
      googleUser.email,
      googleUser.picture
    )

    // 设置cookies
    const headers = new Headers()
    const cookieOptions = {
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax' as const,
      path: '/',
      maxAge: 60 * 60 * 24 * 1, // 1天
    }

    setCookie(headers, 'oauth_id', googleUser.sub, cookieOptions)
    setCookie(headers, 'oauth_email', googleUser.email, cookieOptions)
    setCookie(headers, 'oauth_avatar', googleUser.picture, cookieOptions)
    setCookie(headers, 'userId', user.id, cookieOptions)
    setCookie(headers, 'username', user.username, cookieOptions)
    setCookie(headers, 'userEmail', user.email, cookieOptions)
    setCookie(headers, 'avatarUrl', user.avatar_url, cookieOptions)
    setCookie(headers, 'points', user.points.toString(), cookieOptions)

    return NextResponse.json(
      {
        success: true,
        user: {
          id: googleUser.sub,
          email: googleUser.email,
          name: googleUser.name,
          avatar: googleUser.picture,
        },
      },
      { headers }
    )
  } catch (error) {
    console.error('Google One Tap verification error:', error)
    return NextResponse.json(
      {
        error: 'Authentication failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
