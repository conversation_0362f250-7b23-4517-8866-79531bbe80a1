/**
 * 删除单个素材API
 */

import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabaseClient'
import { deleteFromR2 } from '../../lib/r2-upload'
import { deleteValidationSchema } from '../../lib/validation'
import { ApiResponse } from '../../lib/types'

interface RouteParams {
  params: { id: string }
}

export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
): Promise<NextResponse<ApiResponse>> {
  try {
    const { id } = params

    // 验证 ID
    const validation = deleteValidationSchema.safeParse({ id })

    if (!validation.success) {
      const errors = validation.error.errors.map(
        (err) => `${err.path.join('.')}: ${err.message}`
      )
      return NextResponse.json(
        {
          code: 400,
          message: '参数验证失败',
          data: { errors },
        },
        { status: 400 }
      )
    }

    // 查询素材详情（获取文件URL用于删除）
    const { data: asset, error: fetchError } = await supabase
      .from('img4o_assets')
      .select('*')
      .eq('id', id)
      .single()

    if (fetchError || !asset) {
      return NextResponse.json(
        {
          code: 404,
          message: '素材不存在',
        },
        { status: 404 }
      )
    }

    // 删除关联的文件
    const deletePromises: Promise<void>[] = []

    if (asset.source_image) {
      deletePromises.push(deleteFromR2(asset.source_image))
    }
    if (asset.target_image) {
      deletePromises.push(deleteFromR2(asset.target_image))
    }
    if (asset.video) {
      deletePromises.push(deleteFromR2(asset.video))
    }

    // 并行删除文件（不阻塞数据库删除）
    Promise.all(deletePromises).catch((error) => {
      console.error('删除文件时出现错误:', error)
    })

    // 删除数据库记录
    const { error: deleteError } = await supabase
      .from('img4o_assets')
      .delete()
      .eq('id', id)

    if (deleteError) {
      console.error('数据库删除失败:', deleteError)
      return NextResponse.json(
        {
          code: 500,
          message: '删除失败',
          data: { error: deleteError.message },
        },
        { status: 500 }
      )
    }

    return NextResponse.json({
      code: 200,
      message: '素材删除成功',
      data: { id },
    })
  } catch (error) {
    console.error('删除素材失败:', error)
    return NextResponse.json(
      {
        code: 500,
        message: '服务器内部错误',
        data: { error: error instanceof Error ? error.message : '未知错误' },
      },
      { status: 500 }
    )
  }
}

// 处理 OPTIONS 请求（CORS 预检）
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
