/**
 * 创建素材API - JSON接口（接收文件URL）
 */

import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabaseClient'
import { getValidationSchema } from '../lib/validation'
import {
  getCategoryByTaskType,
  getMediaTypeByTaskType,
  generateRandomLikesCount,
} from '../lib/utils'
import { ApiResponse } from '../lib/types'

/**
 * 验证文件URL是否有效（可选功能）
 */
async function validateFileUrl(url: string): Promise<boolean> {
  try {
    const response = await fetch(url, { method: 'HEAD' })
    return response.ok
  } catch {
    return false
  }
}

export async function POST(
  request: NextRequest
): Promise<NextResponse<ApiResponse>> {
  try {
    console.log('开始处理创建素材请求')

    // 解析 JSON 数据
    const data = await request.json()

    console.log('接收到的JSON数据:', data)

    // 提取基础字段
    const {
      name,
      scenario,
      prompt,
      tags,
      comments,
      rating = 4.8,
      likes_count = generateRandomLikesCount(),
      user_id,
      is_original = 0,
      aspect_ratio = '16:9',
      source_image,
      target_image,
      video,
    } = data

    console.log('解构后的文件字段:', { source_image, target_image, video })

    // 验证基础字段
    if (!name || !scenario || !user_id) {
      return NextResponse.json(
        {
          code: 400,
          message: '缺少必填字段：name, scenario, user_id',
        },
        { status: 400 }
      )
    }

    // 根据 scenario 推断 category 和 type
    console.log('接收到的scenario值:', scenario)
    console.log('scenario类型:', typeof scenario)

    const category = getCategoryByTaskType(scenario as any)
    const type = getMediaTypeByTaskType(scenario as any)

    console.log('推断的分类和类型:', { category, type })

    // 根据分类构建验证数据对象
    const validationData: any = {
      name,
      category,
      type,
      scenario,
      rating,
      likes_count,
      tags,
      comments,
      is_original,
      aspect_ratio,
    }

    // 根据分类添加对应的字段
    switch (category) {
      case 'text2image':
        // 文生图：需要 prompt + target_image
        if (prompt) validationData.prompt = prompt
        if (
          target_image &&
          target_image !== null &&
          target_image !== undefined
        ) {
          validationData.target_image = target_image
        }
        break
      case 'image2image':
        // 图生图：需要 source_image + target_image
        if (
          source_image &&
          source_image !== null &&
          source_image !== undefined
        ) {
          validationData.source_image = source_image
        }
        if (
          target_image &&
          target_image !== null &&
          target_image !== undefined
        ) {
          validationData.target_image = target_image
        }
        break
      case 'text2video':
        // 文生视频：需要 prompt + video
        if (prompt) validationData.prompt = prompt
        if (video && video !== null && video !== undefined) {
          validationData.video = video
        }
        break
      case 'image2video':
        // 图生视频：需要 source_image + video
        if (
          source_image &&
          source_image !== null &&
          source_image !== undefined
        ) {
          validationData.source_image = source_image
        }
        if (video && video !== null && video !== undefined) {
          validationData.video = video
        }
        break
    }

    console.log('最终验证数据:', validationData)
    console.log('验证数据中的文件字段:', {
      source_image: validationData.source_image,
      target_image: validationData.target_image,
      video: validationData.video,
    })

    // 验证数据
    const schema = getValidationSchema(category)
    console.log('使用的验证schema:', category)

    const validation = schema.safeParse(validationData)

    if (!validation.success) {
      console.log('数据验证失败:', validation.error.errors)
      const errors = validation.error.errors.map(
        (err) => `${err.path.join('.')}: ${err.message}`
      )
      return NextResponse.json(
        {
          code: 400,
          message: '数据验证失败',
          data: { errors },
        },
        { status: 400 }
      )
    }

    console.log('数据验证成功')

    // 可选：验证文件URL有效性
    const fileUrls = [source_image, target_image, video].filter(Boolean)
    for (const url of fileUrls) {
      if (url && typeof url === 'string') {
        const isValid = await validateFileUrl(url)
        if (!isValid) {
          console.warn(`文件URL可能无效: ${url}`)
          // 注意：这里只是警告，不阻止创建，因为可能是网络问题
        }
      }
    }

    // 写入数据库
    const insertData = {
      name: validation.data.name,
      category: validation.data.category,
      type: validation.data.type,
      scenario: validation.data.scenario,
      prompt: validation.data.prompt || null,
      source_image: source_image || null,
      target_image: target_image || null,
      video: video || null,
      tags: validation.data.tags,
      rating: validation.data.rating,
      likes_count: validation.data.likes_count,
      comments: validation.data.comments,
      is_original: validation.data.is_original,
      aspect_ratio: validation.data.aspect_ratio,
      user_id,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }

    console.log('准备写入数据库的数据:', insertData)

    const { data: asset, error } = await supabase
      .from('img4o_assets')
      .insert(insertData)
      .select()
      .single()

    if (error) {
      console.error('数据库写入失败:', error)
      return NextResponse.json(
        {
          code: 500,
          message: '数据库写入失败',
          data: { error: error.message },
        },
        { status: 500 }
      )
    }

    return NextResponse.json({
      code: 200,
      message: '素材创建成功',
      data: asset,
    })
  } catch (error) {
    console.error('创建素材失败:', error)
    return NextResponse.json(
      {
        code: 500,
        message: '服务器内部错误',
        data: { error: error instanceof Error ? error.message : '未知错误' },
      },
      { status: 500 }
    )
  }
}

// 处理 OPTIONS 请求（CORS 预检）
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
