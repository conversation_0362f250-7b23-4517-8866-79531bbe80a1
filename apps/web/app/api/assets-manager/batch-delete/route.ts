/**
 * 批量删除素材API
 */

import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabaseClient'
import { batchDeleteFromR2 } from '../lib/r2-upload'
import { batchDeleteValidationSchema } from '../lib/validation'
import { BatchDeleteRequest, ApiResponse } from '../lib/types'

export async function DELETE(
  request: NextRequest
): Promise<NextResponse<ApiResponse>> {
  try {
    const body: BatchDeleteRequest = await request.json()

    // 验证请求参数
    const validation = batchDeleteValidationSchema.safeParse(body)

    if (!validation.success) {
      const errors = validation.error.errors.map(
        (err) => `${err.path.join('.')}: ${err.message}`
      )
      return NextResponse.json(
        {
          code: 400,
          message: '参数验证失败',
          data: { errors },
        },
        { status: 400 }
      )
    }

    const { ids } = validation.data

    // 查询要删除的素材（获取文件URL用于删除）
    const { data: assets, error: fetchError } = await supabase
      .from('img4o_assets')
      .select('id, source_image, target_image, video')
      .in('id', ids)

    if (fetchError) {
      console.error('查询素材失败:', fetchError)
      return NextResponse.json(
        {
          code: 500,
          message: '查询素材失败',
          data: { error: fetchError.message },
        },
        { status: 500 }
      )
    }

    if (!assets || assets.length === 0) {
      return NextResponse.json(
        {
          code: 404,
          message: '未找到要删除的素材',
        },
        { status: 404 }
      )
    }

    // 收集所有需要删除的文件URL
    const fileUrls: string[] = []
    assets.forEach((asset) => {
      if (asset.source_image) fileUrls.push(asset.source_image)
      if (asset.target_image) fileUrls.push(asset.target_image)
      if (asset.video) fileUrls.push(asset.video)
    })

    // 删除文件（异步进行，不阻塞数据库删除）
    if (fileUrls.length > 0) {
      batchDeleteFromR2(fileUrls).catch((error) => {
        console.error('批量删除文件时出现错误:', error)
      })
    }

    // 删除数据库记录
    const { error: deleteError } = await supabase
      .from('img4o_assets')
      .delete()
      .in('id', ids)

    if (deleteError) {
      console.error('批量删除数据库记录失败:', deleteError)
      return NextResponse.json(
        {
          code: 500,
          message: '批量删除失败',
          data: { error: deleteError.message },
        },
        { status: 500 }
      )
    }

    return NextResponse.json({
      code: 200,
      message: `成功删除 ${assets.length} 个素材`,
      data: {
        deletedCount: assets.length,
        deletedIds: assets.map((asset) => asset.id),
      },
    })
  } catch (error) {
    console.error('批量删除素材失败:', error)
    return NextResponse.json(
      {
        code: 500,
        message: '服务器内部错误',
        data: { error: error instanceof Error ? error.message : '未知错误' },
      },
      { status: 500 }
    )
  }
}

// 处理 OPTIONS 请求（CORS 预检）
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
