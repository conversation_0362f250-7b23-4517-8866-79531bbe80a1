/**
 * 更新素材API - JSON接口（接收文件URL）
 */

import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabaseClient'
import { updateValidationSchema } from '../../lib/validation'
import { getCategoryByTaskType, getMediaTypeByTaskType } from '../../lib/utils'
import { ApiResponse } from '../../lib/types'

interface RouteParams {
  params: { id: string }
}

export async function PUT(
  request: NextRequest,
  { params }: RouteParams
): Promise<NextResponse<ApiResponse>> {
  try {
    const { id } = await params

    // 验证 ID 格式
    if (!id || typeof id !== 'string') {
      return NextResponse.json(
        {
          code: 400,
          message: '无效的素材ID',
        },
        { status: 400 }
      )
    }

    // 查询现有素材
    const { data: existingAsset, error: fetchError } = await supabase
      .from('img4o_assets')
      .select('*')
      .eq('id', id)
      .single()

    if (fetchError || !existingAsset) {
      return NextResponse.json(
        {
          code: 404,
          message: '素材不存在',
        },
        { status: 404 }
      )
    }

    // 解析 JSON 数据
    const data = await request.json()

    // 提取字段
    const {
      name,
      scenario,
      prompt,
      tags,
      comments,
      rating,
      likes_count,
      is_original,
      aspect_ratio,
      source_image,
      target_image,
      video,
    } = data

    // 构建更新数据
    const updateData: any = { id }

    if (name) updateData.name = name
    if (scenario) {
      updateData.scenario = scenario
      updateData.category = getCategoryByTaskType(scenario as any)
      updateData.type = getMediaTypeByTaskType(scenario as any)
    }
    if (prompt !== undefined) updateData.prompt = prompt
    if (tags !== undefined) updateData.tags = tags
    if (comments !== undefined) updateData.comments = comments
    if (rating !== undefined) updateData.rating = rating
    if (likes_count !== undefined) updateData.likes_count = likes_count
    if (is_original !== undefined) updateData.is_original = is_original
    if (aspect_ratio !== undefined) updateData.aspect_ratio = aspect_ratio

    // 添加文件字段（现在都是URL字符串）
    if (source_image !== undefined) updateData.source_image = source_image
    if (target_image !== undefined) updateData.target_image = target_image
    if (video !== undefined) updateData.video = video

    // 验证数据
    const validation = updateValidationSchema.safeParse(updateData)

    if (!validation.success) {
      const errors = validation.error.errors.map(
        (err) => `${err.path.join('.')}: ${err.message}`
      )
      return NextResponse.json(
        {
          code: 400,
          message: '数据验证失败',
          data: { errors },
        },
        { status: 400 }
      )
    }

    // 文件URL处理（现在都是URL字符串，无需上传）
    console.log('处理文件URL更新')

    // 构建数据库更新数据
    const dbUpdateData: any = {
      updated_at: new Date().toISOString(),
    }

    // 复制非文件字段
    if (updateData.name) dbUpdateData.name = updateData.name
    if (updateData.category) dbUpdateData.category = updateData.category
    if (updateData.type) dbUpdateData.type = updateData.type
    if (updateData.scenario) dbUpdateData.scenario = updateData.scenario
    if (updateData.prompt !== undefined) dbUpdateData.prompt = updateData.prompt
    if (updateData.tags !== undefined) dbUpdateData.tags = updateData.tags
    if (updateData.comments !== undefined)
      dbUpdateData.comments = updateData.comments
    if (updateData.rating !== undefined) dbUpdateData.rating = updateData.rating
    if (updateData.likes_count !== undefined)
      dbUpdateData.likes_count = updateData.likes_count
    if (updateData.is_original !== undefined)
      dbUpdateData.is_original = updateData.is_original
    if (updateData.aspect_ratio !== undefined)
      dbUpdateData.aspect_ratio = updateData.aspect_ratio

    // 添加文件 URL（直接使用传入的URL）
    if (updateData.source_image !== undefined)
      dbUpdateData.source_image = updateData.source_image
    if (updateData.target_image !== undefined)
      dbUpdateData.target_image = updateData.target_image
    if (updateData.video !== undefined) dbUpdateData.video = updateData.video

    // 更新数据库
    const { data: updatedAsset, error } = await supabase
      .from('img4o_assets')
      .update(dbUpdateData)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('数据库更新失败:', error)
      return NextResponse.json(
        {
          code: 500,
          message: '数据库更新失败',
          data: { error: error.message },
        },
        { status: 500 }
      )
    }

    return NextResponse.json({
      code: 200,
      message: '素材更新成功',
      data: updatedAsset,
    })
  } catch (error) {
    console.error('更新素材失败:', error)
    return NextResponse.json(
      {
        code: 500,
        message: '服务器内部错误',
        data: { error: error instanceof Error ? error.message : '未知错误' },
      },
      { status: 500 }
    )
  }
}

// 处理 OPTIONS 请求（CORS 预检）
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'PUT, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
