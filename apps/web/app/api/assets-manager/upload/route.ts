/**
 * 文件上传接口
 * 支持按分类和文件用途上传文件到 R2 存储
 */

import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { uploadToR2 } from '../lib/r2-upload'
import { Category } from '../lib/types'
import { ApiResponse } from '../lib/types'

// 文件类型定义
type FileType = 'source_image' | 'target_image' | 'video'

// 分类与文件类型的对应关系
const FILE_TYPE_MAP: Record<Category, { required: FileType[]; optional: FileType[] }> = {
  'text2image': {
    required: ['target_image'],
    optional: []
  },
  'image2image': {
    required: ['source_image', 'target_image'],
    optional: []
  },
  'text2video': {
    required: ['video'],
    optional: []
  },
  'image2video': {
    required: ['source_image', 'video'],
    optional: []
  }
}

// 文件类型到媒体类型的映射
const FILE_TYPE_TO_MEDIA_TYPE: Record<FileType, 'image' | 'video'> = {
  'source_image': 'image',
  'target_image': 'image',
  'video': 'video'
}

// 验证 schema
const uploadValidationSchema = z.object({
  category: z.enum(['text2image', 'image2image', 'text2video', 'image2video']),
  fileType: z.enum(['source_image', 'target_image', 'video']),
})

/**
 * 验证文件类型是否符合分类要求
 */
function validateFileTypeForCategory(category: Category, fileType: FileType): boolean {
  const categoryConfig = FILE_TYPE_MAP[category]
  return categoryConfig.required.includes(fileType) || categoryConfig.optional.includes(fileType)
}

/**
 * 验证文件格式和大小
 */
function validateFile(file: File, fileType: FileType): { isValid: boolean; error?: string } {
  const mediaType = FILE_TYPE_TO_MEDIA_TYPE[fileType]
  
  // 支持的文件类型
  const imageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
  const videoTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/quicktime']
  
  // 文件大小限制
  const maxImageSize = 10 * 1024 * 1024 // 10MB
  const maxVideoSize = 100 * 1024 * 1024 // 100MB
  
  if (mediaType === 'image') {
    if (!imageTypes.includes(file.type)) {
      return {
        isValid: false,
        error: `不支持的图片格式，支持格式：${imageTypes.join(', ')}`
      }
    }
    if (file.size > maxImageSize) {
      return {
        isValid: false,
        error: `图片文件大小不能超过 ${maxImageSize / 1024 / 1024}MB`
      }
    }
  } else if (mediaType === 'video') {
    if (!videoTypes.includes(file.type)) {
      return {
        isValid: false,
        error: `不支持的视频格式，支持格式：${videoTypes.join(', ')}`
      }
    }
    if (file.size > maxVideoSize) {
      return {
        isValid: false,
        error: `视频文件大小不能超过 ${maxVideoSize / 1024 / 1024}MB`
      }
    }
  }
  
  return { isValid: true }
}

export async function POST(
  request: NextRequest
): Promise<NextResponse<ApiResponse>> {
  try {
    console.log('开始处理文件上传请求')

    // 解析 FormData
    const formData = await request.formData()

    // 提取参数
    const file = formData.get('file') as File | null
    const category = formData.get('category') as string
    const fileType = formData.get('fileType') as string

    console.log('接收到的参数:', {
      fileName: file?.name,
      fileSize: file?.size,
      category,
      fileType
    })

    // 验证必填参数
    if (!file) {
      return NextResponse.json(
        {
          code: 400,
          message: '请选择要上传的文件',
        },
        { status: 400 }
      )
    }

    if (!category || !fileType) {
      return NextResponse.json(
        {
          code: 400,
          message: '缺少必填参数：category, fileType',
        },
        { status: 400 }
      )
    }

    // 验证参数格式
    const validation = uploadValidationSchema.safeParse({ category, fileType })
    if (!validation.success) {
      const errors = validation.error.errors.map(
        (err) => `${err.path.join('.')}: ${err.message}`
      )
      return NextResponse.json(
        {
          code: 400,
          message: '参数验证失败',
          data: { errors },
        },
        { status: 400 }
      )
    }

    // 验证文件类型是否符合分类要求
    if (!validateFileTypeForCategory(category as Category, fileType as FileType)) {
      return NextResponse.json(
        {
          code: 400,
          message: `文件类型 ${fileType} 不适用于分类 ${category}`,
        },
        { status: 400 }
      )
    }

    // 验证文件格式和大小
    const fileValidation = validateFile(file, fileType as FileType)
    if (!fileValidation.isValid) {
      return NextResponse.json(
        {
          code: 400,
          message: fileValidation.error,
        },
        { status: 400 }
      )
    }

    // 上传文件到 R2
    const mediaType = FILE_TYPE_TO_MEDIA_TYPE[fileType as FileType]
    const fileUrl = await uploadToR2(file, category as Category, mediaType)

    console.log('文件上传成功:', fileUrl)

    return NextResponse.json({
      code: 200,
      message: '文件上传成功',
      data: {
        url: fileUrl,
        filename: file.name,
        size: file.size,
        type: file.type,
        category,
        fileType
      },
    })
  } catch (error) {
    console.error('文件上传失败:', error)
    return NextResponse.json(
      {
        code: 500,
        message: '文件上传失败',
        data: { error: error instanceof Error ? error.message : '未知错误' },
      },
      { status: 500 }
    )
  }
}

// 处理 OPTIONS 请求（CORS 预检）
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
