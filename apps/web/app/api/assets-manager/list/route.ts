/**
 * 查询素材列表API
 */

import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabaseClient'
import { listValidationSchema } from '../lib/validation'
import { getPaginationInfo } from '../lib/utils'
import {
  ListAssetsRequest,
  ListAssetsResponse,
  ApiResponse,
} from '../lib/types'

export async function POST(
  request: NextRequest
): Promise<
  NextResponse<
    | ApiResponse<ListAssetsResponse>
    | ApiResponse<{ errors: string[] }>
    | ApiResponse<{ error: string }>
  >
> {
  try {
    const body = await request.json()

    // 验证请求参数
    const validation = listValidationSchema.safeParse(body)

    if (!validation.success) {
      const errors = validation.error.errors.map(
        (err) => `${err.path.join('.')}: ${err.message}`
      )
      return NextResponse.json(
        {
          code: 400,
          message: '参数验证失败',
          data: { errors },
        },
        { status: 400 }
      )
    }

    const {
      page,
      pageSize,
      category,
      name,
      rating,
      scenario,
      user_id,
      created_at,
      sortBy,
      sortOrder,
    } = validation.data

    // 构建查询
    let query = supabase.from('img4o_assets').select('*', { count: 'exact' })

    // 应用过滤条件
    if (category) {
      query = query.eq('category', category)
    }

    if (name) {
      query = query.ilike('name', `%${name}%`)
    }

    if (rating) {
      if (rating.min !== undefined && rating.max !== undefined) {
        query = query.gte('rating', rating.min).lte('rating', rating.max)
      } else if (rating.min !== undefined) {
        query = query.gte('rating', rating.min)
      } else if (rating.max !== undefined) {
        query = query.lte('rating', rating.max)
      }
    }

    if (scenario) {
      query = query.eq('scenario', scenario)
    }

    if (user_id) {
      query = query.eq('user_id', user_id)
    }

    if (created_at) {
      query = query
        .gte('created_at', created_at.start)
        .lte('created_at', created_at.end)
    }

    // 应用排序
    query = query.order(sortBy, { ascending: sortOrder === 'asc' })

    // 应用分页
    const { offset } = getPaginationInfo(page, pageSize, 0)
    query = query.range(offset, offset + pageSize - 1)

    // 执行查询
    const { data: assets, error, count } = await query

    if (error) {
      console.error('查询素材失败:', error)
      return NextResponse.json(
        {
          code: 500,
          message: '查询失败',
          data: { error: error.message },
        },
        { status: 500 }
      )
    }

    // 构建响应数据
    const total = count || 0
    const pagination = getPaginationInfo(page, pageSize, total)

    const response: ListAssetsResponse = {
      data: assets || [],
      pagination: {
        page: pagination.page,
        pageSize: pagination.pageSize,
        total: pagination.total,
        totalPages: pagination.totalPages,
      },
    }

    return NextResponse.json({
      code: 200,
      message: '查询成功',
      data: response,
    })
  } catch (error) {
    console.error('查询素材列表失败:', error)
    return NextResponse.json(
      {
        code: 500,
        message: '服务器内部错误',
        data: { error: error instanceof Error ? error.message : '未知错误' },
      },
      { status: 500 }
    )
  }
}

// 处理 OPTIONS 请求（CORS 预检）
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
