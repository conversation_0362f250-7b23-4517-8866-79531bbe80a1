/**
 * 素材管理工具函数
 */

import {
  TaskType,
  taskTypePointConfig,
} from '@shared/lib/point/page-point-config'
import { Category, MediaType } from './types'
import { getTaskTypePointConfigByTaskType } from '@shared/lib/point/point-config-service'

/**
 * 根据 TaskType 推断业务分类
 * @param taskType 任务类型
 * @returns 业务分类
 */
//
export function getCategoryByTaskType(taskType: TaskType): Category {
  // 从配置中查找对应的任务类型
  const config = Object.values(taskTypePointConfig).find(
    (config) => config.taskType === taskType
  )

  if (config && config.category) {
    return config.category as Category
  }

  // 如果找不到配置，默认返回 image2image
  console.warn(`未找到 taskType "${taskType}" 的配置，使用默认分类 image2image`)
  return 'image2image'
}

/**
 * 根据 TaskType 推断媒体类型
 * @param taskType 任务类型
 * @returns 媒体类型
 */
export function getMediaTypeByTaskType(taskType: TaskType): MediaType {
  const config = getTaskTypePointConfigByTaskType(taskType)
  return config?.mediaType === 'video' ? 'video' : 'image'
}

/**
 * 获取场景选项（基于现有配置）
 * @returns 场景选项数组
 */
export function getScenarioOptions() {
  return Object.entries(taskTypePointConfig).map(([, config]) => ({
    value: config.taskType,
    label: (config as any).description,
    mediaType: (config as any).mediaType,
    category: getCategoryByTaskType(config.taskType),
    type: getMediaTypeByTaskType(config.taskType),
  }))
}

/**
 * 按业务分类分组的场景选项
 * @returns 分组的场景选项
 */
export function getGroupedScenarioOptions() {
  const options = getScenarioOptions()
  return {
    text2image: options.filter((opt) => opt.category === 'text2image'),
    image2image: options.filter((opt) => opt.category === 'image2image'),
    text2video: options.filter((opt) => opt.category === 'text2video'),
    image2video: options.filter((opt) => opt.category === 'image2video'),
  }
}

/**
 * 生成随机点赞数
 * @returns 100-1000之间的随机整数
 */
export function generateRandomLikesCount(): number {
  return Math.floor(Math.random() * 901) + 100
}

/**
 * 验证必填字段（基于业务分类）
 * @param category 业务分类
 * @param data 数据对象
 * @returns 验证错误数组
 */
export function validateRequiredFields(
  category: Category,
  data: any
): string[] {
  const errors: string[] = []

  // 基础必填字段
  if (!data.name) errors.push('素材名称不能为空')
  if (!data.category) errors.push('业务分类不能为空')
  if (!data.type) errors.push('媒体类型不能为空')
  if (!data.scenario) errors.push('应用场景不能为空')

  // 根据分类检查特定必填字段
  switch (category) {
    case 'text2image':
      if (!data.prompt) errors.push('提示词不能为空')
      if (!data.target_image) errors.push('目标图片不能为空')
      break

    case 'image2image':
      if (!data.source_image) errors.push('原图不能为空')
      if (!data.target_image) errors.push('目标图片不能为空')
      break

    case 'text2video':
      if (!data.prompt) errors.push('提示词不能为空')
      if (!data.video) errors.push('视频不能为空')
      break

    case 'image2video':
      if (!data.source_image) errors.push('原图不能为空')
      if (!data.video) errors.push('视频不能为空')
      break
  }

  return errors
}

/**
 * 清理字符串（移除首尾空格，处理 null/undefined）
 * @param str 输入字符串
 * @returns 清理后的字符串
 */
export function cleanString(str: any): string | undefined {
  if (str === null || str === undefined) return undefined
  if (typeof str !== 'string') return undefined
  const cleaned = str.trim()
  return cleaned.length > 0 ? cleaned : undefined
}

/**
 * 解析标签字符串
 * @param tags 逗号分隔的标签字符串
 * @returns 标签数组
 */
export function parseTags(tags?: string): string[] {
  if (!tags) return []
  return tags
    .split(',')
    .map((tag) => tag.trim())
    .filter((tag) => tag.length > 0)
}

/**
 * 格式化标签数组为字符串
 * @param tags 标签数组
 * @returns 逗号分隔的标签字符串
 */
export function formatTags(tags: string[]): string {
  return tags.filter((tag) => tag.trim().length > 0).join(',')
}

/**
 * 获取分页信息
 * @param page 页码
 * @param pageSize 每页大小
 * @param total 总数
 */
export function getPaginationInfo(
  page: number,
  pageSize: number,
  total: number
) {
  const totalPages = Math.ceil(total / pageSize)
  const offset = (page - 1) * pageSize

  return {
    page,
    pageSize,
    total,
    totalPages,
    offset,
    hasNext: page < totalPages,
    hasPrev: page > 1,
  }
}

/**
 * 验证评分范围
 * @param rating 评分
 * @returns 是否有效
 */
export function isValidRating(rating: any): boolean {
  return typeof rating === 'number' && rating >= 1 && rating <= 5
}

/**
 * 验证点赞数范围
 * @param likesCount 点赞数
 * @returns 是否有效
 */
export function isValidLikesCount(likesCount: any): boolean {
  return (
    typeof likesCount === 'number' &&
    Number.isInteger(likesCount) &&
    likesCount >= 0
  )
}
