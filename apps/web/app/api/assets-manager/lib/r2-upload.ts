/**
 * Cloudflare R2 文件上传工具
 * 支持按业务分类分文件夹上传
 */

import {
  S3Client,
  PutObjectCommand,
  DeleteObjectCommand,
} from '@aws-sdk/client-s3'

const BUCKET_NAME =
  process.env.NEXT_PUBLIC_CLOUDFLARE_R2_BUCKET_NAME || 'img-gen'

// 验证环境变量的函数
function validateR2Config() {
  if (!process.env.NEXT_PUBLIC_CLOUDFLARE_R2_ACCOUNT_ID) {
    throw new Error('NEXT_PUBLIC_CLOUDFLARE_R2_ACCOUNT_ID 环境变量未设置')
  }
  if (!process.env.NEXT_PUBLIC_CLOUDFLARE_R2_ACCESS_KEY_ID) {
    throw new Error('NEXT_PUBLIC_CLOUDFLARE_R2_ACCESS_KEY_ID 环境变量未设置')
  }
  if (!process.env.NEXT_PUBLIC_CLOUDFLARE_R2_SECRET_ACCESS_KEY) {
    throw new Error(
      'NEXT_PUBLIC_CLOUDFLARE_R2_SECRET_ACCESS_KEY 环境变量未设置'
    )
  }
}

// 创建 R2 客户端
function createR2Client() {
  // validateR2Config()

  return new S3Client({
    region: 'auto',
    endpoint: `https://${process.env.NEXT_PUBLIC_CLOUDFLARE_R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,
    credentials: {
      accessKeyId: process.env.NEXT_PUBLIC_CLOUDFLARE_R2_ACCESS_KEY_ID!,
      secretAccessKey: process.env.NEXT_PUBLIC_CLOUDFLARE_R2_SECRET_ACCESS_KEY!,
    },
  })
}

// 业务分类类型
type Category = 'text2image' | 'image2image' | 'text2video' | 'image2video'

// 支持的文件类型
const SUPPORTED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/webp',
]
const SUPPORTED_VIDEO_TYPES = [
  'video/mp4',
  'video/avi',
  'video/mov',
  'video/quicktime',
]

// 文件大小限制
const MAX_IMAGE_SIZE = 10 * 1024 * 1024 // 10MB
const MAX_VIDEO_SIZE = 100 * 1024 * 1024 // 100MB

/**
 * 生成文件路径
 * @param category 业务分类
 * @param filename 文件名
 * @returns R2 存储路径
 */
function generateFilePath(category: Category, filename: string): string {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')

  // 生成唯一文件名
  const timestamp = Date.now()
  const randomStr = Math.random().toString(36).substring(2, 8)
  const ext = filename.split('.').pop()
  const uniqueFilename = `${timestamp}_${randomStr}.${ext}`

  return `${category}/${year}/${month}/${day}/${uniqueFilename}`
}

/**
 * 验证文件类型和大小
 * @param file 文件对象
 * @param type 媒体类型
 */
function validateFile(file: File, type: 'image' | 'video') {
  // 验证文件类型
  if (type === 'image') {
    if (!SUPPORTED_IMAGE_TYPES.includes(file.type)) {
      throw new Error(
        `不支持的图片格式，支持格式：${SUPPORTED_IMAGE_TYPES.join(', ')}`
      )
    }
    if (file.size > MAX_IMAGE_SIZE) {
      throw new Error(`图片文件大小不能超过 ${MAX_IMAGE_SIZE / 1024 / 1024}MB`)
    }
  } else if (type === 'video') {
    if (!SUPPORTED_VIDEO_TYPES.includes(file.type)) {
      throw new Error(
        `不支持的视频格式，支持格式：${SUPPORTED_VIDEO_TYPES.join(', ')}`
      )
    }
    if (file.size > MAX_VIDEO_SIZE) {
      throw new Error(`视频文件大小不能超过 ${MAX_VIDEO_SIZE / 1024 / 1024}MB`)
    }
  }
}

/**
 * 上传文件到 R2
 * @param file 文件对象
 * @param category 业务分类
 * @param type 媒体类型
 * @returns 上传后的文件 URL
 */
export async function uploadToR2(
  file: File,
  category: Category,
  type: 'image' | 'video'
): Promise<string> {
  try {
    // 验证文件
    validateFile(file, type)

    // 生成文件路径
    const filePath = generateFilePath(category, file.name)
    console.log('生成的文件路径:', filePath)

    // 上传到 R2
    const command = new PutObjectCommand({
      Bucket: BUCKET_NAME,
      Key: filePath,
      Body: Buffer.from(await file.arrayBuffer()),
      ContentType: file.type,
      CacheControl: 'public, max-age=31536000', // 缓存一年
    })

    console.log('准备发送R2命令...')
    const r2Client = createR2Client()
    await r2Client.send(command)
    console.log('R2上传成功')

    // 返回公开访问的 URL
    const fileUrl = `https://oss.x2one.us/${filePath}`
    console.log('生成的文件URL:', fileUrl)
    return fileUrl
  } catch (error) {
    console.error('R2 上传失败:', error)
    console.error('错误详情:', {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    })
    throw new Error(
      `文件上传失败: ${error instanceof Error ? error.message : '未知错误'}`
    )
  }
}

/**
 * 从 R2 删除文件
 * @param fileUrl 文件 URL
 */
export async function deleteFromR2(fileUrl: string): Promise<void> {
  try {
    // 从 URL 中提取文件路径
    const url = new URL(fileUrl)
    const filePath = url.pathname.substring(1) // 移除开头的 '/'

    const command = new DeleteObjectCommand({
      Bucket: BUCKET_NAME,
      Key: filePath,
    })

    const r2Client = createR2Client()
    await r2Client.send(command)
  } catch (error) {
    console.error('R2 删除失败:', error)
    throw new Error(
      `文件删除失败: ${error instanceof Error ? error.message : '未知错误'}`
    )
  }
}

/**
 * 批量上传文件
 * @param files 文件数组
 * @param category 业务分类
 * @param type 媒体类型
 * @returns 上传后的文件 URL 数组
 */
export async function batchUploadToR2(
  files: File[],
  category: Category,
  type: 'image' | 'video'
): Promise<string[]> {
  const uploadPromises = files.map((file) => uploadToR2(file, category, type))
  return Promise.all(uploadPromises)
}

/**
 * 批量删除文件
 * @param fileUrls 文件 URL 数组
 */
export async function batchDeleteFromR2(fileUrls: string[]): Promise<void> {
  const deletePromises = fileUrls.map((url) => deleteFromR2(url))
  await Promise.all(deletePromises)
}
