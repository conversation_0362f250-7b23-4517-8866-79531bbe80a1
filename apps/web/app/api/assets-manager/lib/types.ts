/**
 * 素材管理相关类型定义
 */

import { TaskType } from '@shared/lib/point/page-point-config'

// 业务分类类型
export type Category =
  | 'text2image'
  | 'image2image'
  | 'text2video'
  | 'image2video'

// 媒体类型
export type MediaType = 'image' | 'video'

// 素材数据库记录
export interface AssetRecord {
  id: string
  name: string
  category: Category
  type: MediaType
  scenario: TaskType
  prompt?: string
  source_image?: string
  target_image?: string
  video?: string
  tags?: string
  rating: number
  likes_count: number
  comments?: string
  user_id: string
  created_at: string
  updated_at: string
  aspect_ratio?: string // 素材比例：'16:9' | '9:16'
  is_original: number // 是否原创：0-非原创，1-原创
}

// 创建素材请求参数
export interface CreateAssetRequest {
  // 基础信息
  name: string
  category: Category
  type: MediaType
  scenario: TaskType

  // 可选字段
  prompt?: string
  tags?: string
  comments?: string
  rating?: number
  likes_count?: number
  is_original?: number // 是否原创：0-非原创，1-原创，默认为0
  aspect_ratio?: string // 素材比例：'16:9' | '9:16'，默认为16:9

  // 文件上传（Base64 或 FormData）
  source_image?: File
  target_image?: File
  video?: File
}

// 更新素材请求参数
export interface UpdateAssetRequest {
  id: string
  name?: string
  category?: Category
  type?: MediaType
  scenario?: TaskType
  prompt?: string
  tags?: string
  comments?: string
  rating?: number
  likes_count?: number
  is_original?: number // 是否原创：0-非原创，1-原创
  aspect_ratio?: string // 素材比例：'16:9' | '9:16'

  // 文件更新
  source_image?: File
  target_image?: File
  video?: File
}

// 评分区间类型
export interface RatingRange {
  min?: number
  max?: number
}

// 查询素材请求参数
export interface ListAssetsRequest {
  page: number
  pageSize: number
  category?: Category
  name?: string
  rating?: RatingRange
  scenario?: TaskType
  user_id?: string
  created_at?: {
    start: string
    end: string
  }
  sortBy?: 'created_at' | 'updated_at' | 'rating' | 'likes_count' | 'name'
  sortOrder?: 'asc' | 'desc'
}

// 查询响应
export interface ListAssetsResponse {
  data: AssetRecord[]
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
}

// API 响应格式
export interface ApiResponse<T = any> {
  code: number
  message: string
  data?: T
}

// 批量删除请求
export interface BatchDeleteRequest {
  ids: string[]
}

// 验证错误
export interface ValidationError {
  field: string
  message: string
}
