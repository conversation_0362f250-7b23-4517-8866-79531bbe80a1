/**
 * 素材管理验证规则
 */

import { z } from 'zod'
import { Category } from './types'

// 基础验证 Schema
const baseSchema = z.object({
  name: z
    .string()
    .min(1, '素材名称不能为空')
    .max(200, '素材名称不能超过200个字符'),
  // category和type由scenario自动推断，不需要用户填写
  category: z
    .enum(['text2image', 'image2image', 'text2video', 'image2video'])
    .optional(),
  type: z.enum(['image', 'video']).optional(),
  scenario: z.string().min(1, '请选择应用场景'),
  rating: z.number().min(1).max(5).default(4.8),
  likes_count: z
    .number()
    .int()
    .min(0)
    .max(999999)
    .default(() => Math.floor(Math.random() * 901) + 100),
  tags: z.string().optional(),
  comments: z.string().max(1000, '评论内容不能超过1000个字符').optional(),
  prompt: z.string().optional(),
  is_original: z.number().int().min(0).max(1).default(0), // 是否原创：0-非原创，1-原创，默认为0
  aspect_ratio: z.enum(['16:9', '9:16']).default('16:9'), // 素材比例：16:9横屏或9:16竖屏，默认为16:9
})

// 文件验证 Schema
const fileSchema = z.union([z.instanceof(File), z.string().url()])

/**
 * 根据业务分类生成验证 Schema
 * @param category 业务分类
 * @returns 验证 Schema
 */
export function getValidationSchema(category: Category) {
  switch (category) {
    case 'text2image':
      // 文生图：只需要 prompt + target_image
      return baseSchema.extend({
        prompt: z
          .string()
          .min(1, '请输入提示词')
          .max(4000, '提示词不能超过2000个字符'),
        target_image: fileSchema,
      })

    case 'image2image':
      // 图生图：只需要 source_image + target_image
      return baseSchema.extend({
        source_image: fileSchema,
        target_image: fileSchema,
      })

    case 'text2video':
      // 文生视频：只需要 prompt + video
      return baseSchema.extend({
        prompt: z
          .string()
          .min(1, '请输入提示词')
          .max(4000, '提示词不能超过2000个字符'),
        video: fileSchema,
      })

    case 'image2video':
      // 图生视频：只需要 source_image + video
      return baseSchema.extend({
        source_image: fileSchema,
        video: fileSchema,
      })

    default:
      return baseSchema
  }
}

// 更新验证 Schema（文件为可选）
export const updateValidationSchema = z.object({
  id: z.string().uuid('无效的ID格式'),
  name: z
    .string()
    .min(1, '素材名称不能为空')
    .max(200, '素材名称不能超过200个字符')
    .optional(),
  category: z
    .enum(['text2image', 'image2image', 'text2video', 'image2video'])
    .optional(),
  type: z.enum(['image', 'video']).optional(),
  scenario: z.string().min(1, '请选择应用场景').optional(),
  rating: z.number().min(1).max(5).optional(),
  likes_count: z.number().int().min(0).max(999999).optional(),
  tags: z.string().optional(),
  comments: z.string().max(1000, '评论内容不能超过1000个字符').optional(),
  prompt: z.string().max(2000, '提示词不能超过2000个字符').optional(),
  is_original: z.number().int().min(0).max(1).optional(), // 是否原创：0-非原创，1-原创
  aspect_ratio: z.enum(['16:9', '9:16']).optional(), // 素材比例：16:9横屏或9:16竖屏
  // 文件字段为可选（用于更新）
  source_image: fileSchema.optional(),
  target_image: fileSchema.optional(),
  video: fileSchema.optional(),
})

// 评分区间验证 Schema
const ratingRangeSchema = z.object({
  min: z.number().min(0).max(5).optional(),
  max: z.number().min(0).max(5).optional(),
})

// 查询验证 Schema
export const listValidationSchema = z.object({
  page: z.number().int().min(1).default(1),
  pageSize: z.number().int().min(1).max(100).default(20),
  category: z
    .enum(['text2image', 'image2image', 'text2video', 'image2video'])
    .optional(),
  name: z.string().optional(),
  rating: ratingRangeSchema.optional(),
  scenario: z.string().optional(),
  user_id: z.string().uuid().optional(),
  created_at: z
    .object({
      start: z.string().datetime(),
      end: z.string().datetime(),
    })
    .optional(),
  sortBy: z
    .enum(['created_at', 'updated_at', 'rating', 'likes_count', 'name'])
    .default('created_at'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
})

// 批量删除验证 Schema
export const batchDeleteValidationSchema = z.object({
  ids: z
    .array(z.string().uuid('无效的ID格式'))
    .min(1, '请选择要删除的素材')
    .max(100, '一次最多删除100个素材'),
})

// 删除单个素材验证 Schema
export const deleteValidationSchema = z.object({
  id: z.string().uuid('无效的ID格式'),
})

/**
 * 验证文件类型
 * @param file 文件对象
 * @param expectedType 期望的媒体类型
 * @returns 验证结果
 */
export function validateFileType(
  file: File,
  expectedType: 'image' | 'video'
): { isValid: boolean; error?: string } {
  const imageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
  const videoTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/quicktime']

  if (expectedType === 'image') {
    if (!imageTypes.includes(file.type)) {
      return {
        isValid: false,
        error: `不支持的图片格式，支持格式：${imageTypes.join(', ')}`,
      }
    }
  } else if (expectedType === 'video') {
    if (!videoTypes.includes(file.type)) {
      return {
        isValid: false,
        error: `不支持的视频格式，支持格式：${videoTypes.join(', ')}`,
      }
    }
  }

  return { isValid: true }
}

/**
 * 验证文件大小
 * @param file 文件对象
 * @param type 媒体类型
 * @returns 验证结果
 */
export function validateFileSize(
  file: File,
  type: 'image' | 'video'
): { isValid: boolean; error?: string } {
  const maxImageSize = 10 * 1024 * 1024 // 10MB
  const maxVideoSize = 100 * 1024 * 1024 // 100MB

  if (type === 'image' && file.size > maxImageSize) {
    return {
      isValid: false,
      error: `图片文件大小不能超过 ${maxImageSize / 1024 / 1024}MB`,
    }
  }

  if (type === 'video' && file.size > maxVideoSize) {
    return {
      isValid: false,
      error: `视频文件大小不能超过 ${maxVideoSize / 1024 / 1024}MB`,
    }
  }

  return { isValid: true }
}
