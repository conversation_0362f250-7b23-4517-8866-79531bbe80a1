import { NextRequest, NextResponse } from 'next/server';

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  try {
    const { image, model = 'u2net' } = await request.json();

    if (!image) {
      return NextResponse.json(
        { success: false, error: 'Missing image parameter' },
        { status: 400 }
      );
    }

    // Get base URL from environment variable or default
    const baseUrl = process.env.IOPAINT_BASE_URL || 'https://faith1314666-imggen-magic-wand.hf.space';

    // Prepare request body with secure server-side constants
    const requestBody = {
      name: 'RemoveBG',
      image: image,
      clicks: [],
      scale: 1.0
    };

    const response = await fetch(`${baseUrl}/api/v1/run_plugin_gen_image`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'image/*',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorText = await response.text();
      return NextResponse.json(
        { success: false, error: `Background removal failed: ${response.statusText} - ${errorText}` },
        { status: response.status }
      );
    }

    // Handle different response types
    const contentType = response.headers.get('content-type');
    
    if (contentType?.includes('image/')) {
      // Direct image response
      const blob = await response.blob();
      const buffer = await blob.arrayBuffer();
      const base64 = Buffer.from(buffer).toString('base64');
      
      return NextResponse.json({
        success: true,
        imageBase64: base64
      });
    } else {
      // JSON response
      const result = await response.text();
      
      return NextResponse.json({
        success: true,
        imageBase64: result
      });
    }

  } catch (error) {
    console.error('Background removal API error:', error);
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
