# AI 图像生成模块集成指南

本文档详细介绍了 AI 图像生成模块 (`aiimage`) 的架构、工作流程和关键组件。

## 1. 架构概述

`aiimage` 模块采用分层和模块化的设计，以确保代码的可维护性和可扩展性。

- **API 端点 (`apps/web/app/api/aiimage/generate/route.ts`)**: 这是模块的入口点，负责处理所有传入的图像生成请求。它编排了认证、验证、任务创建和积分管理等整个流程。

- **积分服务 (`apps/web/app/services/points.ts`)**: 一个专门的服务，用于处理所有与用户积分相关的逻辑。它封装了与数据库的交互，提供了积分验证和消耗的统一接口。

- **任务处理器 (`apps/web/app/api/aiimage/utils/processors/`)**: 使用工厂模式 (`TaskProcessorFactory`) 和策略模式 (`ITaskProcessor` 接口)，为不同类型的 AI 任务（如换脸、虚拟试穿等）提供独立的逻辑处理器。这种设计使得添加新的任务类型变得简单。

- **工具函数 (`apps/web/app/api/aiimage/utils/`)**: 包含一系列辅助模块，如输入验证器 (`validators.ts`)、状态映射器 (`status-mappers.ts`) 和历史记录管理器 (`history.ts`)。

## 2. 未来架构和开发方法论

为了提高代码的可维护性、可测试性和团队协作效率，未来所有新的 API 接口开发都应遵循以下方法论。此方法论旨在将业务逻辑与路由处理分离，形成清晰的分层架构。

### 核心原则：瘦路由，胖服务 (Thin Route, Fat Service)

- **路由 (`route.ts`)**: 仅负责处理 HTTP 请求和响应。其职责应严格限制在：

  1.  解析和验证请求参数（Query, Body）。
  2.  调用相应的服务层方法来执行业务逻辑。
  3.  处理服务层返回的结果或错误。
  4.  构建并返回标准化的 HTTP 响应。
      路由文件不应包含任何复杂的业务逻辑、数据库查询或对外部服务的直接调用。

- **服务 (`services/`)**: 封装所有核心业务逻辑。其职责包括：
  1.  执行具体的业务操作。
  2.  与数据库进行交互（通过 Prisma、Supabase Client 等）。
  3.  调用其他内部或外部服务（例如 `PointsService`、`PiAPI`）。
  4.  处理复杂的业务规则和数据转换。

### 推荐目录结构

对于每一个新的 API 功能模块，例如 `some-feature`，应采用以下目录结构：

```
apps/web/app/api/
└── some-feature/
    ├── services/
    │   └── index.ts      # 服务层主文件，封装业务逻辑
    └── route.ts          # 路由文件，处理 HTTP 请求
```

### 示例：创建一个新的“笔记”API

假设我们要创建一个用于管理笔记的新 API。

**1. 服务层 (`apps/web/app/api/notes/services/index.ts`)**

```typescript
// apps/web/app/api/notes/services/index.ts

import { supabase } from '../../../lib/supabaseClient' // 假设使用 Supabase

export class NotesService {
  /**
   * 创建一篇新笔记
   * @param userId 用户 ID
   * @param content 笔记内容
   * @returns 创建的笔记对象
   */
  static async createNote(userId: string, content: string) {
    if (!content) {
      throw new Error('Note content cannot be empty.')
    }

    // 在这里执行数据库操作和其他业务逻辑
    const { data, error } = await supabase
      .from('notes')
      .insert({ user_id: userId, content })
      .select()
      .single()

    if (error) {
      console.error('Failed to create note:', error)
      throw new Error('Database error while creating note.')
    }

    return data
  }

  // ... 其他方法，如 getNote, updateNote, deleteNote
}
```

**2. 路由层 (`apps/web/app/api/notes/route.ts`)**

```typescript
// apps/web/app/api/notes/route.ts

import { NextRequest, NextResponse } from 'next/server'
import { NotesService } from './services'
import { getUserFromRequest } from '../../utils/auth' // 假设的认证工具

export async function POST(request: NextRequest) {
  try {
    // 1. 解析和验证请求
    const user = await getUserFromRequest(request) // 身份认证
    if (!user) {
      return NextResponse.json({ msg: 'Unauthorized' }, { status: 401 })
    }

    const { content } = await request.json()
    if (!content) {
      return NextResponse.json({ msg: 'Content is required' }, { status: 400 })
    }

    // 2. 调用服务层
    const newNote = await NotesService.createNote(user.id, content)

    // 3. 构建并返回成功响应
    return NextResponse.json({
      code: 200,
      data: newNote,
      msg: 'Note created successfully',
    })
  } catch (error) {
    // 4. 处理错误并返回
    console.error('POST /api/notes Error:', error)
    const message =
      error instanceof Error ? error.message : 'Internal server error'
    return NextResponse.json({ msg: message }, { status: 500 })
  }
}
```

通过遵循此方法论，我们可以确保 AI 或人类开发者在扩展新功能时，能够生成结构清晰、职责分明且易于维护的代码。

## 3. 工作流程

下图展示了从用户点击“生成”按钮到完成任务的完整工作流程。

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant GenerateRoute as /api/aiimage/generate
    participant PointsService as 积分服务
    participant PiAPI as 外部 PiAPI
    participant Database as 数据库

    Client->>+GenerateRoute: POST 请求 (task_type, input)
    GenerateRoute->>GenerateRoute: 1. 验证用户身份
    alt 身份验证失败
        GenerateRoute-->>-Client: 401 未授权
    end
    GenerateRoute->>GenerateRoute: 2. 获取任务处理器并验证输入
    alt 输入验证失败
        GenerateRoute-->>-Client: 400 错误的请求
    end
    GenerateRoute->>+PointsService: 3. validateAndConsumePoints(user, points)
    PointsService->>+Database: 查询用户积分
    Database-->>-PointsService: 返回用户当前积分
    alt 积分不足
        PointsService-->>-GenerateRoute: 验证失败
        GenerateRoute-->>-Client: 402 积分不足
    else
        PointsService-->>-GenerateRoute: 验证成功 (包含当前积分)
    end
    GenerateRoute->>+PiAPI: 4. 创建 AI 任务
    PiAPI-->>-GenerateRoute: 返回任务 ID
    alt PiAPI 任务创建失败
        GenerateRoute-->>-Client: 500 内部服务器错误
    end
    GenerateRoute->>+Database: 5. 创建本地历史记录
    Database-->>-GenerateRoute: 历史记录创建成功
    GenerateRoute->>+PointsService: 6. consumePoints(user, points, currentPoints)
    PointsService->>+Database: 更新用户积分
    Database-->>-PointsService: 更新成功
    PointsService->>+Database: 记录积分消耗历史
    Database-->>-PointsService: 记录成功
    PointsService-->>-GenerateRoute: 积分消耗成功
    GenerateRoute-->>-Client: 200 OK (包含任务 ID 和 history_id)
```

## 4. 积分管理

积分管理是系统的核心部分，由 `PointsService` 负责。

### `PointsService.validateAndConsumePoints`

- **目的**: 在执行昂贵操作（如调用第三方 API）之前，预先检查用户是否有足够的积分。
- **逻辑**:
  1.  验证用户是否存在。
  2.  从 `img4o_user` 表中获取用户最新的积分。
  3.  比较用户积分和所需积分。
  4.  如果积分充足，返回成功并附带用户的当前积分。这避免了在后续的消耗步骤中再次查询数据库。

### `PointsService.consumePoints`

- **目的**: 在确认任务成功创建后，实际扣除用户积分。
- **逻辑**:
  1.  根据 `validateAndConsumePoints` 传递的 `currentPoints` 计算新的积分余额。
  2.  更新 `img4o_user` 表中的用户积分。
  3.  在 `img4o_points_history` 表中创建一条详细的消耗记录，以便于审计和跟踪。
  4.  **容错**: 即使积分消耗或历史记录创建失败，`generate/route.ts` 也会向客户端返回成功。这是因为 AI 任务已经创建，不能让用户因为内部的非关键性错误而受到影响。所有失败的操作都会被记录下来，以便进行离线修复。

## 5. 任务处理

为了支持多种 AI 功能，系统采用了可扩展的任务处理器架构。

- **`ITaskProcessor` 接口**: 定义了每个任务处理器必须实现的通用方法：

  - `validateInput(input: any): { isValid: boolean; error?: string }`: 验证特定任务的输入参数。
  - `getPointsConfig(input: any): { pointsConfig: number; pointsDescription: string; batchMultiplier: number }`: 获取该任务所需的积分配置。
  - `buildPiApiRequestBody(input: any, webhookUrl: string, webhookSecret: string): object`: 构建发送给外部 PiAPI 的请求体。
  - `createHistoryRecord(userId: string, taskId: string, input: any): Promise<any>`: 创建特定于任务的历史记录。

- **`TaskProcessorFactory`**: 一个简单的工厂类，根据传入的 `task_type` 返回相应的任务处理器实例。

### 如何添加新的任务类型

1.  在 `apps/web/app/api/aiimage/utils/processors/` 目录下创建一个新的处理器文件，例如 `my-new-task.ts`。
2.  实现 `ITaskProcessor` 接口。
3.  在 `TaskProcessorFactory` 中注册新的处理器。
4.  在 `generate/route.ts` 的 `supportedTaskTypes` 列表中添加新的任务类型。

## 6. API 端点: `POST /api/aiimage/generate`

- **描述**: 创建一个新的 AI 生成任务。
- **请求体 (Body)**:
  ```json
  {
    "task_type": "face_swap", // 任务类型 (例如: "face_swap", "virtual_try_on")
    "input": {
      // 特定于任务的输入参数
      "face_image_url": "...",
      "template_image_url": "..."
    }
  }
  ```
- **成功响应 (200 OK)**:
  ```json
  {
    "code": 200,
    "data": {
      "task_id": "piapi-task-id-12345",
      "task_type": "face_swap",
      "status": "pending",
      "input": { ... },
      "output": null,
      "history_record_id": "local-history-id-67890"
    },
    "msg": "success"
  }
  ```
- **错误响应**:
  - `401 Unauthorized`: 用户未登录。
  - `402 Payment Required`: 积分不足。
  - `400 Bad Request`: 输入参数无效或任务类型不受支持。
  - `500 Internal Server Error`: 其他服务器端错误。

## 7. 错误处理

系统设计考虑了多种错误情况，以确保其健壮性：

- **同步错误**: 如认证失败或积分不足，会立即中断流程并向客户端返回相应的错误代码。
- **异步/非关键性错误**: 如创建历史记录或消耗积分失败，不会阻塞主流程。错误会被记录在服务器日志中，但客户端仍会收到成功响应，确保了核心功能（AI 任务创建）的可靠性。这种“最终一致性”的方法在分布式系统中很常见。
