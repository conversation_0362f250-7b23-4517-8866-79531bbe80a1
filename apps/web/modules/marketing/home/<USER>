import { atom } from 'jotai'
import { atomWithStorage } from 'jotai/utils'
import { z } from 'zod'

// 定义风格类型
export type StyleType = {
  id: number
  title: string
  category: string
  prompt: string
  originalImage: string
  generatedImage: string
  showPrompt: boolean
  showButton: boolean
  ratio: string // 允许任意比例字符串，例如'3:2'、'2:3'、'1:1'等
  showInStyles: boolean // 是否能够用于风格选择
  type: string // 类型：text-to-image 或 image-to-image
  href: string
}

// 定义表单验证模式
export const formSchema = z.object({
  prompt: z.string().min(1, '请输入转换提示语'),
  ratio: z.enum(['3:2', '2:3', '1:1'], {
    required_error: '请选择输出比例',
  }),
  nVariants: z.enum(['1', '2', '4']).default('1'),
})

export type FormData = z.infer<typeof formSchema>

// 创建原子状态，使用atomWithStorage持久化选中的风格到 sessionStorage
export const selectedStyleAtom = atomWithStorage<StyleType | null>(
  'image_converter_selected_style',
  null,
  {
    // 自定义存储对象，使用 sessionStorage 而不是 localStorage
    getItem: (key, initialValue) => {
      try {
        const item = sessionStorage.getItem(key)
        if (!item) return initialValue
        return JSON.parse(item)
      } catch (e) {
        console.error('Error reading from sessionStorage', e)
        return initialValue
      }
    },
    setItem: (key, value) => {
      try {
        sessionStorage.setItem(key, JSON.stringify(value))
      } catch (e) {
        console.error('Error writing to sessionStorage', e)
      }
    },
    removeItem: (key) => {
      try {
        sessionStorage.removeItem(key)
      } catch (e) {
        console.error('Error removing from sessionStorage', e)
      }
    },
  }
)

// 使用 sessionStorage 持久化风格取消状态
export const isStyleCancelledAtom = atomWithStorage<boolean>(
  'image_converter_style_cancelled',
  false,
  {
    // 自定义存储对象，使用 sessionStorage 而不是 localStorage
    getItem: (key, initialValue) => {
      try {
        const item = sessionStorage.getItem(key)
        if (!item) return initialValue
        return JSON.parse(item)
      } catch (e) {
        console.error('Error reading from sessionStorage', e)
        return initialValue
      }
    },
    setItem: (key, value) => {
      try {
        sessionStorage.setItem(key, JSON.stringify(value))
      } catch (e) {
        console.error('Error writing to sessionStorage', e)
      }
    },
    removeItem: (key) => {
      try {
        sessionStorage.removeItem(key)
      } catch (e) {
        console.error('Error removing from sessionStorage', e)
      }
    },
  }
)

// 使用 atomWithStorage 持久化表单数据到 sessionStorage
export const formDataAtom = atomWithStorage<FormData | null>(
  'image_converter_form',
  null,
  {
    // 自定义存储对象，使用 sessionStorage 而不是 localStorage
    getItem: (key, initialValue) => {
      try {
        const item = sessionStorage.getItem(key)
        if (!item) return initialValue
        return JSON.parse(item)
      } catch (e) {
        console.error('Error reading from sessionStorage', e)
        return initialValue
      }
    },
    setItem: (key, value) => {
      try {
        sessionStorage.setItem(key, JSON.stringify(value))
      } catch (e) {
        console.error('Error writing to sessionStorage', e)
      }
    },
    removeItem: (key) => {
      try {
        sessionStorage.removeItem(key)
      } catch (e) {
        console.error('Error removing from sessionStorage', e)
      }
    },
  }
)

// 使用 atomWithStorage 持久化生成模式到 localStorage
export const generationModeAtom = atomWithStorage<
  'text-to-image' | 'image-to-image'
>('image_converter_mode', 'text-to-image', {
  // 自定义存储对象，确保使用 localStorage
  getItem: (key, initialValue) => {
    try {
      const item = localStorage.getItem(key)
      if (!item) return initialValue
      return JSON.parse(item)
    } catch (e) {
      console.error('Error reading from localStorage', e)
      return initialValue
    }
  },
  setItem: (key, value) => {
    try {
      localStorage.setItem(key, JSON.stringify(value))
    } catch (e) {
      console.error('Error writing to localStorage', e)
    }
  },
  removeItem: (key) => {
    try {
      localStorage.removeItem(key)
    } catch (e) {
      console.error('Error removing from localStorage', e)
    }
  },
})

// 定义整合的图片数据结构
export interface ImageItem {
  // 本地文件对象（仅在当前会话有效）
  file?: File
  // 本地预览URL（仅在当前会话有效）
  previewUrl: string
  // OSS URL（持久化存储）
  ossUrl?: string
  // 上传状态
  uploading: boolean
  // 上传错误信息
  error?: string
}

// 整合的图片数据数组
export const imagesAtom = atom<ImageItem[]>([])

// 持久化的OSS URL数组，用于页面刷新后恢复，使用 sessionStorage
export const persistedOssUrlsAtom = atomWithStorage<string[]>(
  'image_converter_oss_urls',
  [],
  {
    // 自定义存储对象，使用 sessionStorage 而不是 localStorage
    getItem: (key, initialValue) => {
      try {
        const item = sessionStorage.getItem(key)
        if (!item) return initialValue
        return JSON.parse(item)
      } catch (e) {
        console.error('Error reading from sessionStorage', e)
        return initialValue
      }
    },
    setItem: (key, value) => {
      try {
        sessionStorage.setItem(key, JSON.stringify(value))
      } catch (e) {
        console.error('Error writing to sessionStorage', e)
      }
    },
    removeItem: (key) => {
      try {
        sessionStorage.removeItem(key)
      } catch (e) {
        console.error('Error removing from sessionStorage', e)
      }
    },
  }
)

export const isGeneratedAtom = atom<boolean>(false)
export const generatedTaskIdAtom = atom<string | null>(null)

// 图片生成相关状态 - 使用 sessionStorage 持久化进度
export const generationProgressAtom = atomWithStorage<number>(
  'image_converter_generation_progress',
  0,
  {
    // 自定义存储对象，使用 sessionStorage
    getItem: (key, initialValue) => {
      try {
        const item = sessionStorage.getItem(key)
        if (!item) return initialValue
        return JSON.parse(item)
      } catch (e) {
        console.error('Error reading from sessionStorage', e)
        return initialValue
      }
    },
    setItem: (key, value) => {
      try {
        sessionStorage.setItem(key, JSON.stringify(value))
      } catch (e) {
        console.error('Error writing to sessionStorage', e)
      }
    },
    removeItem: (key) => {
      try {
        sessionStorage.removeItem(key)
      } catch (e) {
        console.error('Error removing from sessionStorage', e)
      }
    },
  }
)
export const isGeneratingAtom = atomWithStorage<boolean>(
  'image_converter_is_generating',
  false,
  {
    // 自定义存储对象，使用 sessionStorage
    getItem: (key, initialValue) => {
      try {
        const item = sessionStorage.getItem(key)
        if (!item) return initialValue
        return JSON.parse(item)
      } catch (e) {
        console.error('Error reading from sessionStorage', e)
        return initialValue
      }
    },
    setItem: (key, value) => {
      try {
        sessionStorage.setItem(key, JSON.stringify(value))
      } catch (e) {
        console.error('Error writing to sessionStorage', e)
      }
    },
    removeItem: (key) => {
      try {
        sessionStorage.removeItem(key)
      } catch (e) {
        console.error('Error removing from sessionStorage', e)
      }
    },
  }
)
export const generationErrorAtom = atom<string | null>(null)
export const generatedImageUrlAtom = atom<string | null>(null)
export const generatedImageUrlsAtom = atom<string[]>([])

// 添加当前任务ID的全局状态 - 使用 sessionStorage 持久化
export const currentTaskIdAtom = atomWithStorage<string | null>(
  'image_converter_current_task_id',
  null,
  {
    // 自定义存储对象，使用 sessionStorage
    getItem: (key, initialValue) => {
      try {
        const item = sessionStorage.getItem(key)
        if (!item) return initialValue
        return JSON.parse(item)
      } catch (e) {
        console.error('Error reading from sessionStorage', e)
        return initialValue
      }
    },
    setItem: (key, value) => {
      try {
        sessionStorage.setItem(key, JSON.stringify(value))
      } catch (e) {
        console.error('Error writing to sessionStorage', e)
      }
    },
    removeItem: (key) => {
      try {
        sessionStorage.removeItem(key)
      } catch (e) {
        console.error('Error removing from sessionStorage', e)
      }
    },
  }
)

// 派生原子 - 用于重置所有状态
export const resetAtom = atom(
  null, // 读取时返回null
  (_get, set) => {
    set(imagesAtom, [])
    set(persistedOssUrlsAtom, [])
    set(formDataAtom, null)
    set(isGeneratedAtom, false)
    set(generatedTaskIdAtom, null)
    // 不重置选中的风格和风格取消状态，保留用户的选择
    // set(selectedStyleAtom, null)
    // set(isStyleCancelledAtom, false)
    set(generationProgressAtom, 0)
    set(isGeneratingAtom, false)
    set(generationErrorAtom, null)
    set(generatedImageUrlAtom, null)
    set(generatedImageUrlsAtom, [])
    set(currentTaskIdAtom, null)
    // 不重置生成模式，保留用户的偏好
  }
)
