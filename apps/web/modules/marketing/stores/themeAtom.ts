import { atom } from 'jotai'
import { atomWithStorage } from 'jotai/utils'

export type Theme = 'light' | 'dark'

// 主题相关常量
export const THEME_COOKIE_NAME = 'theme'
export const USER_THEME_OVERRIDE_COOKIE = 'user-theme-override'

// 主题优先级配置 - 与ThemeScript中保持一致
// true: 路径判断优先级更高 (AI页面强制light，其他页面强制dark) ———— 此时切换主题也无效
// false: 用户覆盖设置优先级更高 (用户手动设置优先)
export const PATH_THEME_PRIORITY_HIGHER = true

// 主题存储适配器（非null类型）
const createThemeStorage = (cookieName: string) => ({
  getItem: (key: string, initialValue: Theme): Theme => {
    if (typeof window === 'undefined') {
      // 服务端：尝试从document.cookie读取（如果可用）
      if (typeof document !== 'undefined' && document.cookie) {
        const match = document.cookie.match(
          new RegExp(`(^| )${cookieName}=([^;]+)`)
        )
        if (match && (match[2] === 'light' || match[2] === 'dark')) {
          return match[2] as Theme
        }
      }
      return initialValue
    }

    // 客户端：优先从cookie读取，然后是localStorage
    const getCookie = (name: string) => {
      const match = document.cookie.match(new RegExp(`(^| )${name}=([^;]+)`))
      return match ? match[2] : null
    }

    const cookieValue = getCookie(cookieName)
    if (cookieValue && (cookieValue === 'light' || cookieValue === 'dark')) {
      return cookieValue as Theme
    }

    // 回退到localStorage
    try {
      const stored = localStorage.getItem(key)
      if (stored && (stored === '"light"' || stored === '"dark"')) {
        return JSON.parse(stored)
      }
    } catch (e) {
      console.warn('Failed to read from localStorage:', e)
    }

    return initialValue
  },

  setItem: (key: string, value: Theme) => {
    if (typeof window === 'undefined') return

    // 同时设置cookie和localStorage
    const setCookie = (name: string, val: string) => {
      const maxAge = 60 * 60 * 24 * 365 // 1年
      document.cookie = `${name}=${val}; path=/; max-age=${maxAge}; SameSite=Lax${
        window.location.protocol === 'https:' ? '; Secure' : ''
      }`
    }

    setCookie(cookieName, value)

    try {
      localStorage.setItem(key, JSON.stringify(value))
    } catch (e) {
      console.warn('Failed to write to localStorage:', e)
    }
  },

  removeItem: (key: string) => {
    if (typeof window === 'undefined') return

    // 删除cookie和localStorage
    document.cookie = `${cookieName}=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT`

    try {
      localStorage.removeItem(key)
    } catch (e) {
      console.warn('Failed to remove from localStorage:', e)
    }
  },
})

// 用户覆盖存储适配器（可null类型）
const createOverrideStorage = (cookieName: string) => ({
  getItem: (key: string, initialValue: Theme | null): Theme | null => {
    if (typeof window === 'undefined') {
      return initialValue
    }

    // 客户端：优先从cookie读取
    const getCookie = (name: string) => {
      const match = document.cookie.match(new RegExp(`(^| )${name}=([^;]+)`))
      return match ? match[2] : null
    }

    const cookieValue = getCookie(cookieName)
    if (cookieValue && (cookieValue === 'light' || cookieValue === 'dark')) {
      return cookieValue as Theme
    }

    // 回退到localStorage
    try {
      const stored = localStorage.getItem(key)
      if (stored && stored !== 'null') {
        const parsed = JSON.parse(stored)
        if (parsed === 'light' || parsed === 'dark') {
          return parsed
        }
      }
    } catch (e) {
      console.warn('Failed to read from localStorage:', e)
    }

    return initialValue
  },

  setItem: (key: string, value: Theme | null) => {
    if (typeof window === 'undefined') return

    if (value === null) {
      // 删除cookie和localStorage
      document.cookie = `${cookieName}=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT`
      try {
        localStorage.removeItem(key)
      } catch (e) {
        console.warn('Failed to remove from localStorage:', e)
      }
      return
    }

    // 设置cookie和localStorage
    const setCookie = (name: string, val: string) => {
      const maxAge = 60 * 60 * 24 * 365 // 1年
      document.cookie = `${name}=${val}; path=/; max-age=${maxAge}; SameSite=Lax${
        window.location.protocol === 'https:' ? '; Secure' : ''
      }`
    }

    setCookie(cookieName, value)

    try {
      localStorage.setItem(key, JSON.stringify(value))
    } catch (e) {
      console.warn('Failed to write to localStorage:', e)
    }
  },

  removeItem: (key: string) => {
    if (typeof window === 'undefined') return

    // 删除cookie和localStorage
    document.cookie = `${cookieName}=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT`

    try {
      localStorage.removeItem(key)
    } catch (e) {
      console.warn('Failed to remove from localStorage:', e)
    }
  },
})

// 主题atom - 使用主题存储适配器
export const themeAtom = atomWithStorage<Theme>(
  'theme',
  'dark',
  createThemeStorage(THEME_COOKIE_NAME)
)

// 用户主题覆盖atom - 用于跟踪用户手动设置
export const userThemeOverrideAtom = atomWithStorage<Theme | null>(
  'user-theme-override',
  null,
  createOverrideStorage(USER_THEME_OVERRIDE_COOKIE)
)

// 计算属性：判断是否为亮色主题
export const isLightThemeAtom = atom((get) => get(themeAtom) === 'light')

// 计算属性：判断是否为暗色主题
export const isDarkThemeAtom = atom((get) => get(themeAtom) === 'dark')

// 判断是否为AI页面的atom（从路径判断）
export const isAiPageAtom = atom<boolean>(false)

// 计算属性：获取基于路径的默认主题
export const defaultThemeForPathAtom = atom((get) => {
  const isAiPage = get(isAiPageAtom)
  return isAiPage ? 'light' : 'dark'
})

// 主题解析函数：根据优先级配置决定最终主题
export const resolveThemeAtom = atom((get) => {
  const userOverride = get(userThemeOverrideAtom)
  const defaultTheme = get(defaultThemeForPathAtom)
  const isAiPage = get(isAiPageAtom)

  // 根据优先级配置决定主题
  if (PATH_THEME_PRIORITY_HIGHER) {
    // 路径优先级更高：AI页面强制light，其他页面强制dark
    return defaultTheme
  } else {
    // 用户覆盖优先级更高：用户设置 > 路径判断 > 默认
    return userOverride || defaultTheme
  }
})

// 主题解析辅助函数（用于非atom环境）
export function resolveTheme(
  userOverride: Theme | null,
  isAiPage: boolean,
  fallbackTheme: Theme = 'dark'
): Theme {
  const pathBasedTheme = isAiPage ? 'light' : 'dark'

  if (PATH_THEME_PRIORITY_HIGHER) {
    // 路径优先级更高
    return pathBasedTheme
  } else {
    // 用户覆盖优先级更高
    return userOverride || pathBasedTheme || fallbackTheme
  }
}

// 切换主题的 action atom（用户手动切换）
export const toggleThemeAtom = atom(null, (get, set) => {
  const currentTheme = get(themeAtom)
  const newTheme: Theme = currentTheme === 'light' ? 'dark' : 'light'

  // 设置新主题
  set(themeAtom, newTheme)

  // 标记为用户手动设置
  set(userThemeOverrideAtom, newTheme)

  // 设置用户覆盖cookie
  if (typeof window !== 'undefined') {
    const maxAge = 60 * 60 * 24 * 365 // 1年
    document.cookie = `${USER_THEME_OVERRIDE_COOKIE}=${newTheme}; path=/; max-age=${maxAge}; SameSite=Lax${
      window.location.protocol === 'https:' ? '; Secure' : ''
    }`
  }

  return newTheme
})

// 设置主题的 action atom（系统自动设置）
export const setThemeAtom = atom(null, (_get, set, newTheme: Theme) => {
  set(themeAtom, newTheme)
  return newTheme
})

// 清除用户主题覆盖的 action atom
export const clearUserThemeOverrideAtom = atom(null, (get, set) => {
  set(userThemeOverrideAtom, null)

  // 删除用户覆盖cookie
  if (typeof window !== 'undefined') {
    document.cookie = `${USER_THEME_OVERRIDE_COOKIE}=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT`
  }

  // 根据当前路径重新设置主题
  const defaultTheme = get(defaultThemeForPathAtom)
  set(themeAtom, defaultTheme)

  return defaultTheme
})
