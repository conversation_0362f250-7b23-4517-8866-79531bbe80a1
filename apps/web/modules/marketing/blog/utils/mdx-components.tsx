import { Link } from '@i18n/routing'
import { slugifyHeadline } from '@shared/lib/content'
import type { MDXComponents } from 'mdx/types'
import type { ImageProps } from 'next/image'
import Image from 'next/image'

export const mdxComponents = {
  a: (props) => {
    const { href, children, ...rest } = props
    const isInternalLink =
      href && (href.startsWith('/') || href.startsWith('#'))

    return isInternalLink ? (
      <Link href={href} {...rest}>
        {children}
      </Link>
    ) : (
      <a target="_blank" rel="noopener noreferrer" href={href} {...rest}>
        {children}
      </a>
    )
  },
  img: (props) =>
    props.src ? (
      <Image
        {...(props as ImageProps)}
        sizes="100vw"
        style={{ width: '100%', height: 'auto' }}
        className="rounded-lg shadow"
        loading="lazy"
      />
    ) : null,
  h1: ({ children, ...rest }) => {
    const textContent =
      typeof children === 'string' ? children : String(children || '')
    return (
      <h1
        id={slugifyHeadline(textContent)}
        className="mb-6 font-bold text-4xl"
        {...rest}
      >
        {children}
      </h1>
    )
  },
  h2: ({ children, ...rest }) => {
    const textContent =
      typeof children === 'string' ? children : String(children || '')
    return (
      <h2
        id={slugifyHeadline(textContent)}
        className="mb-4 font-bold text-2xl"
        {...rest}
      >
        {children}
      </h2>
    )
  },
  h3: ({ children, ...rest }) => {
    const textContent =
      typeof children === 'string' ? children : String(children || '')
    return (
      <h3
        id={slugifyHeadline(textContent)}
        className="mb-4 font-bold text-xl"
        {...rest}
      >
        {children}
      </h3>
    )
  },
  h4: ({ children, ...rest }) => {
    const textContent =
      typeof children === 'string' ? children : String(children || '')
    return (
      <h4
        id={slugifyHeadline(textContent)}
        className="mb-4 font-bold text-lg"
        {...rest}
      >
        {children}
      </h4>
    )
  },
  h5: ({ children, ...rest }) => {
    const textContent =
      typeof children === 'string' ? children : String(children || '')
    return (
      <h5
        id={slugifyHeadline(textContent)}
        className="mb-4 font-bold text-base"
        {...rest}
      >
        {children}
      </h5>
    )
  },
  h6: ({ children, ...rest }) => {
    const textContent =
      typeof children === 'string' ? children : String(children || '')
    return (
      <h6
        id={slugifyHeadline(textContent)}
        className="mb-4 font-bold text-sm"
        {...rest}
      >
        {children}
      </h6>
    )
  },
  p: ({ children, ...rest }) => (
    <p className="mb-6 text-foreground/80 leading-relaxed" {...rest}>
      {children}
    </p>
  ),
  ul: ({ children, ...rest }) => (
    <ul className="mb-6 list-inside list-disc space-y-2 pl-4" {...rest}>
      {children}
    </ul>
  ),
  ol: ({ children, ...rest }) => (
    <ol className="mb-6 list-inside list-decimal space-y-2 pl-4" {...rest}>
      {children}
    </ol>
  ),
  li: ({ children, ...rest }) => <li {...rest}>{children}</li>,
} satisfies MDXComponents
