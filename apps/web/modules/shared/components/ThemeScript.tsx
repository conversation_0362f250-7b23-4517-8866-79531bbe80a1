/**
 * 服务端主题脚本组件
 * 在页面加载前立即执行，避免主题闪烁
 * 必须在 <head> 中尽早加载
 */

import { PATH_THEME_PRIORITY_HIGHER } from "@marketing/stores"


export function ThemeScript() {
  // 内联脚本，在页面加载前立即执行
  const themeScript = `
    (function() {
      try {
        // 主题优先级配置 - 与 themeAtom.ts 保持一致
        const PATH_THEME_PRIORITY_HIGHER = ${PATH_THEME_PRIORITY_HIGHER};

        // 获取cookie值的辅助函数
        function getCookie(name) {
          const match = document.cookie.match(new RegExp('(^| )' + name + '=([^;]+)'));
          return match ? match[2] : null;
        }

        // 判断是否为AI页面
        function isAiPage(pathname) {
          const pathWithoutLocale = pathname.replace(/^\\/[a-z]{2}(-[A-Z]{2})?/, '') || '/';
          return pathWithoutLocale.startsWith('/ai/');
        }

        // 获取基于路径的默认主题
        function getDefaultThemeForPath(pathname) {
          return isAiPage(pathname) ? 'light' : 'dark';
        }

        // 主题解析函数：根据优先级配置决定最终主题
        function resolveTheme(userOverride, pathname) {
          const pathBasedTheme = getDefaultThemeForPath(pathname);

          if (PATH_THEME_PRIORITY_HIGHER) {
            // 路径优先级更高：AI页面强制light，其他页面强制dark
            return pathBasedTheme;
          } else {
            // 用户覆盖优先级更高：用户设置 > 路径判断
            if (userOverride && (userOverride === 'light' || userOverride === 'dark')) {
              return userOverride;
            }
            return pathBasedTheme;
          }
        }

        // 获取当前路径
        const pathname = window.location.pathname;

        // 获取用户覆盖设置
        const userOverride = getCookie('user-theme-override');

        // 使用主题解析函数确定最终主题
        const targetTheme = resolveTheme(userOverride, pathname);
        
        // 立即应用主题到DOM
        document.documentElement.classList.remove('light', 'dark');
        document.documentElement.classList.add(targetTheme);
        
        // 设置CSS变量
        document.documentElement.style.setProperty('--theme', targetTheme);
        
        // 确保cookie与实际主题一致
        const currentThemeCookie = getCookie('theme');
        if (currentThemeCookie !== targetTheme) {
          const maxAge = 60 * 60 * 24 * 365; // 1年
          document.cookie = 'theme=' + targetTheme + '; path=/; max-age=' + maxAge + '; SameSite=Lax' + 
            (window.location.protocol === 'https:' ? '; Secure' : '');
        }
        
        // 调试信息（仅开发环境）
        if (${process.env.NODE_ENV === 'development'}) {
          console.log('🎨 [ThemeScript] 主题预设:', targetTheme, '路径:', pathname);
        }
        
      } catch (error) {
        console.error('ThemeScript error:', error);
        // 出错时回退到默认主题
        document.documentElement.classList.remove('light', 'dark');
        document.documentElement.classList.add('dark');
      }
    })();
  `

  return (
    <script
      dangerouslySetInnerHTML={{ __html: themeScript }}
      // 确保脚本尽早执行
      // suppressHydrationWarning
    />
  )
}
