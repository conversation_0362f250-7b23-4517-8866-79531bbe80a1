'use client'

import { cn } from '@ui/lib'
import { useState, useCallback, useRef, useEffect } from 'react'
import { Link, usePathname } from '@i18n/routing'
import { useTranslations } from 'next-intl'
import {
  BookOpen,
  GraduationCap,
  Video,
  FileText,
  Lightbulb,
} from 'lucide-react'
import { useAtom } from 'jotai'
import { themeAtom } from '@marketing/stores'

// Resources Dropdown Menu Component
interface ResourcesDropdownMenuProps {
  children: React.ReactNode
  className?: string
}

export function ResourcesDropdownMenu({
  children,
  className,
}: ResourcesDropdownMenuProps) {
  const [isHovered, setIsHovered] = useState(false)
  const pathname = usePathname()
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const t = useTranslations()
  const [theme] = useAtom(themeAtom)

  // 导航处理函数
  const handleNavigation = useCallback(() => {
    setIsHovered(false)
  }, [])

  // 处理鼠标进入事件
  const handleMouseEnter = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
    }
    setIsHovered(true)
  }, [])

  // 处理鼠标离开事件
  const handleMouseLeave = useCallback(() => {
    timeoutRef.current = setTimeout(() => {
      setIsHovered(false)
    }, 150)
  }, [])

  // 清理定时器
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  // 资源菜单项
  const resourceItems = [
    {
      title: t('common.menu.blog'),
      desc: t('common.menu.blogDesc'),
      href: '/blog',
      icon: <FileText className="w-5 h-5 text-blue-400" />,
      gradient:
        theme === 'light'
          ? 'from-blue-500 to-indigo-600'
          : 'from-blue-600 to-indigo-600',
    },
    {
      title: t('common.menu.learnAiImage'),
      desc: t('common.menu.learnAiImageDesc'),
      href: '/learn-ai-image',
      icon: <GraduationCap className="w-5 h-5 text-purple-400" />,
      gradient:
        theme === 'light'
          ? 'from-purple-500 to-pink-600'
          : 'from-purple-600 to-pink-600',
    },
    {
      title: t('common.menu.learnAiVideo'),
      desc: t('common.menu.learnAiVideoDesc'),
      href: '/learn-ai-video',
      icon: <Video className="w-5 h-5 text-green-400" />,
      gradient:
        theme === 'light'
          ? 'from-green-500 to-teal-600'
          : 'from-green-600 to-teal-600',
    },
    {
      title: t('common.menu.learnPrompts'),
      desc: t('common.menu.learnPromptsDesc'),
      href: '/learn-prompts',
      icon: <Lightbulb className="w-5 h-5 text-orange-400" />,
      gradient:
        theme === 'light'
          ? 'from-orange-500 to-red-600'
          : 'from-orange-600 to-red-600',
    },
  ]

  return (
    <div
      className={cn('relative', className)}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {children}

      {/* Dropdown Menu */}
      <div
        className={cn(
          'absolute max-h-[calc(100vh-62px)] overscroll-y-contain !overflow-y-auto top-[calc(100%+8px)] left-1/2 backdrop-blur-md rounded-xl overflow-hidden z-[200]',
          'w-[600px] max-w-[95vw]',
          'transition-all origin-top',
          theme === 'light'
            ? 'bg-white border border-blue-200/50'
            : 'bg-gray-900/95 border border-purple-500/20',
          isHovered
            ? theme === 'light'
              ? 'animate-dropdown opacity-100 visible translate-y-0 shadow-[0_4px_20px_-2px_rgba(0,0,0,0.1),0_0_15px_-3px_rgba(59,130,246,0.2)] border-blue-200/60'
              : 'animate-dropdown opacity-100 visible translate-y-0 shadow-[0_4px_20px_-2px_rgba(0,0,0,0.3),0_0_15px_-3px_rgba(127,50,237,0.3)] border-purple-500/30'
            : 'opacity-0 invisible translate-y-[-8px] shadow-none pointer-events-none'
        )}
        style={{ '--translate-x': '-33.33%' } as React.CSSProperties}
      >
        <div className="p-6">
          <h2
            className={cn(
              'text-xl font-semibold mb-6',
              theme === 'light' ? 'text-gray-900' : 'text-white'
            )}
          >
            {t('common.menu.resources')}
          </h2>

          {/* Resources Grid */}
          <div className="grid grid-cols-2 gap-4">
            {resourceItems.map((item, index) => (
              <Link
                key={index}
                href={item.href}
                onClick={() => handleNavigation()}
                className={cn(
                  'group relative rounded-xl p-4 cursor-pointer transition-all',
                  `bg-gradient-to-r ${item.gradient} hover:shadow-lg hover:shadow-purple-500/20`
                )}
              >
                <div className="flex items-start gap-3">
                  <div className="w-10 h-10 rounded-lg bg-white/20 flex items-center justify-center flex-shrink-0">
                    {item.icon}
                  </div>
                  <div>
                    <h3 className="text-white font-medium text-base mb-1">
                      {item.title}
                    </h3>
                    <p className="text-white/80 text-sm leading-relaxed">
                      {item.desc}
                    </p>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
