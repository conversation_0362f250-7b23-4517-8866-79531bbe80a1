'use client'

import type { PropsWithChildren } from 'react'
import { useEffect, useState } from 'react'
import { googleOneTap } from '@/utils/google-one-tap-login'
import {
  getGoogleOneTapConfig,
  isGoogleOneTapSupported,
  getGoogleOneTapErrorMessage,
} from '@/utils/google-one-tap-config'
import { useAtom } from 'jotai'
import { userInfoAtom } from '@marketing/stores'
import { getUserFromClientCookies } from '@/utils/client-cookies'

// 移动端检测函数
const isMobileDevice = (): boolean => {
  if (typeof window === 'undefined') return false

  // 检查用户代理字符串
  const userAgent = navigator.userAgent.toLowerCase()
  const mobileKeywords = [
    'android',
    'webos',
    'iphone',
    'ipad',
    'ipod',
    'blackberry',
    'windows phone',
    'mobile',
    'tablet',
  ]

  const isMobileUA = mobileKeywords.some((keyword) =>
    userAgent.includes(keyword)
  )

  // 检查屏幕尺寸（移动端通常小于768px）
  const isMobileScreen = window.innerWidth < 768

  // 检查触摸支持
  const hasTouchSupport =
    'ontouchstart' in window || navigator.maxTouchPoints > 0

  // 综合判断：用户代理包含移动设备关键词 或者 (屏幕小且支持触摸)
  return isMobileUA || (isMobileScreen && hasTouchSupport)
}

export function GoogleOneTapProvider({ children }: PropsWithChildren) {
  // 使用状态来处理hydration问题
  const [isClient, setIsClient] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const [userInfoChecked, setUserInfoChecked] = useState(false)
  const [userInfo] = useAtom(userInfoAtom)

  // 处理hydration问题：确保客户端渲染后再进行设备检测
  useEffect(() => {
    setIsClient(true)
    setIsMobile(isMobileDevice())
  }, [])

  // 检查用户信息加载状态
  useEffect(() => {
    if (!isClient) return

    // 检查是否有cookie中的用户信息
    const cookieUser = getUserFromClientCookies()

    if (cookieUser && cookieUser.email) {
      // 有cookie用户信息，等待userInfo从服务器加载
      if (userInfo) {
        // userInfo已加载完成
        setUserInfoChecked(true)
      }
      // 如果userInfo还没加载，继续等待
    } else {
      // 没有cookie用户信息，说明用户未登录，可以直接显示One Tap
      setUserInfoChecked(true)
    }
  }, [isClient, userInfo])

  // 处理One Tap登录成功的回调
  const handleOneTapSuccess = async (response: any) => {
    console.log('Google One Tap response:', response)

    try {
      // 调用后端API验证JWT token
      const verifyResponse = await fetch('/api/oauth/google/one-tap', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          credential: response.credential,
        }),
      })

      const result = await verifyResponse.json()

      if (result.success) {
        console.log('Google One Tap login successful:', result.user)

        // 刷新页面以更新用户状态
        window.location.reload()
      } else {
        console.error('Google One Tap verification failed:', result.error)
      }
    } catch (error) {
      console.error('Error during Google One Tap verification:', error)
    }
  }

  useEffect(() => {
    if (!isClient || typeof window === 'undefined') return
    if (isMobile) return
    if (!userInfoChecked) return // 等待用户信息检查完成

    // 检查环境是否支持Google One Tap
    if (!isGoogleOneTapSupported()) return

    // 如果用户已登录，不显示One Tap
    if (userInfo && userInfo.email) {
      console.log('用户已登录，跳过Google One Tap:', userInfo.email)
      return
    }

    // 获取配置
    const config = getGoogleOneTapConfig()
    if (!config) return

    console.log('初始化Google One Tap')
    try {
      googleOneTap(config, handleOneTapSuccess)
    } catch (error) {
      const errorMessage = getGoogleOneTapErrorMessage(error)
      console.error('GoogleOneTapProvider: 初始化失败:', errorMessage)
    }
  }, [isClient, isMobile, userInfoChecked, userInfo])

  return <>{children}</>
}
