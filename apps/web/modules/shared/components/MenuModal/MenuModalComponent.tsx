'use client'

import { useEffect, useState, useMemo, useCallback } from 'react'
import { X } from 'lucide-react'
import { getMenuCategories } from '@shared/components/Logo'
import { cn } from '@ui/lib'
import { Button } from '@ui/components/button'
import { useTranslations } from 'next-intl'
import { Link } from '@i18n/routing'
import type { CategoryId, ItemId } from '@shared/hooks/useMenuModal'

interface MenuModalByItemIdsComponentProps {
  isOpen: boolean
  itemIds: ItemId[] | ItemId | CategoryId
  onClose: () => void
  title?: string
  viewMoreHref?: string
}

interface MenuItem {
  id?: string
  title: string
  desc: string
  href: string
  seoHref: string
  point?: number
  icon?: React.ReactNode
  isHot?: boolean
  isNew?: boolean
  coming?: boolean
  isVideo?: boolean
  mediaUrl?: string
}

export function MenuModalComponent({
  isOpen,
  itemIds,
  onClose,
  title,
  viewMoreHref = '/templates',
}: MenuModalByItemIdsComponentProps) {
  const [selectedItem, setSelectedItem] = useState<MenuItem | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const t = useTranslations()
  const tm = useTranslations('common')
  const tp = useTranslations('price')

  // 获取菜单数据并筛选出指定ID的项目
  const { filteredItems, categoryInfo } = useMemo(() => {
    const menuCategories = getMenuCategories(false, t)
    const allItems: MenuItem[] = []
    const itemToCategoryMap = new Map<
      string,
      { categoryId: string; categoryLabel: string }
    >()

    // 遍历所有分类，收集所有项目并建立映射关系
    Object.entries(menuCategories).forEach(
      ([categoryId, category]: [string, any]) => {
        if (category.items) {
          category.items.forEach((item: MenuItem) => {
            if (item.id) {
              itemToCategoryMap.set(item.id, {
                categoryId,
                categoryLabel: category.label,
              })
            }
          })
          allItems.push(...category.items)
        }
      }
    )

    // 根据itemIds筛选项目
    let filteredItems: MenuItem[] = []

    if (typeof itemIds === 'string') {
      // 如果传入的是字符串（分类ID），返回该分类下的所有项目
      const category = menuCategories[itemIds as keyof typeof menuCategories]
      if (category && category.items) {
        filteredItems = category.items
      }
    } else {
      // 如果传入的是数组（项目ID），按ID筛选项目
      filteredItems = allItems.filter(
        (item) => item.id && itemIds.includes(item.id)
      )
    }

    // 获取分类信息
    let categoryInfo: {
      categories: string[]
      categoryLabels: string[]
      isSingleCategory: boolean
      singleCategoryLabel?: string
    }

    if (typeof itemIds === 'string') {
      // 如果传入的是字符串（分类ID），直接使用该分类信息
      const category = menuCategories[itemIds as keyof typeof menuCategories]
      categoryInfo = {
        categories: [itemIds],
        categoryLabels: category ? [category.label] : [],
        isSingleCategory: true,
        singleCategoryLabel: category ? category.label : undefined,
      }
    } else {
      // 如果传入的是数组（项目ID），从筛选结果中获取分类信息
      const categories = new Set<string>()
      const categoryLabels = new Set<string>()
      filteredItems.forEach((item) => {
        if (item.id) {
          const categoryInfo = itemToCategoryMap.get(item.id)
          if (categoryInfo) {
            categories.add(categoryInfo.categoryId)
            categoryLabels.add(categoryInfo.categoryLabel)
          }
        }
      })

      categoryInfo = {
        categories: Array.from(categories),
        categoryLabels: Array.from(categoryLabels),
        isSingleCategory: categories.size === 1,
        singleCategoryLabel:
          categories.size === 1 ? Array.from(categoryLabels)[0] : undefined,
      }
    }

    return {
      filteredItems,
      categoryInfo,
    }
  }, [itemIds, t])

  // 计算标题的优先级逻辑
  const modalTitle = useMemo(() => {
    // 最高优先级：调用时传入的title
    if (title) {
      return title
    }

    // 次高优先级：如果是单个分类，使用分类的label
    if (categoryInfo.isSingleCategory && categoryInfo.singleCategoryLabel) {
      return categoryInfo.singleCategoryLabel
    }

    // 默认：空字符串
    return ''
  }, [title, categoryInfo])

  useEffect(() => {
    if (filteredItems.length > 0) {
      setSelectedItem(filteredItems[0])
    }
    // 重置加载状态
    setIsLoading(false)
  }, [filteredItems])

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  if (!isOpen) {
    return null
  }

  if (filteredItems.length === 0) {
    return (
      <div
        className="fixed inset-0 z-[9999] flex items-center justify-center backdrop-blur-sm"
        style={{ pointerEvents: 'auto', backgroundColor: 'rgba(0,0,0,0.4)' }}
      >
        <div className="absolute inset-0" onClick={onClose} />
        <div className="relative bg-white/95 dark:bg-gray-900/95 backdrop-blur-md rounded-2xl shadow-2xl max-w-md w-full mx-4 p-6 border border-white/20 dark:border-gray-700/50">
          <div className="text-center">
            <h3 className="text-lg font-semibold mb-2">未找到项目</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              指定的项目ID未找到对应的菜单项目
            </p>
            <Button onClick={onClose}>关闭</Button>
          </div>
        </div>
      </div>
    )
  }

  const handleTryNow = useCallback(() => {
    if (selectedItem?.seoHref && !isLoading) {
      setIsLoading(true)
      // 添加小延迟以显示加载状态
      setTimeout(() => {
        window.location.href = selectedItem.seoHref
        onClose()
      }, 100)
    }
  }, [selectedItem?.seoHref, onClose, isLoading])

  // 键盘事件处理（ESC关闭，上下箭头导航）
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isOpen || filteredItems.length === 0) return

      switch (event.key) {
        case 'Escape':
          onClose()
          break
        case 'ArrowUp':
          event.preventDefault()
          setSelectedItem((prev) => {
            if (!prev) return filteredItems[0]
            const currentIndex = filteredItems.indexOf(prev)
            const newIndex =
              currentIndex > 0 ? currentIndex - 1 : filteredItems.length - 1
            return filteredItems[newIndex]
          })
          break
        case 'ArrowDown':
          event.preventDefault()
          setSelectedItem((prev) => {
            if (!prev) return filteredItems[0]
            const currentIndex = filteredItems.indexOf(prev)
            const newIndex =
              currentIndex < filteredItems.length - 1 ? currentIndex + 1 : 0
            return filteredItems[newIndex]
          })
          break
        case 'Enter':
          if (selectedItem && !(selectedItem as any).coming) {
            handleTryNow()
          }
          break
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown)
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [isOpen, onClose, filteredItems, selectedItem, handleTryNow])

  return (
    <div
      className="fixed inset-0 z-[9999] flex items-center justify-center backdrop-blur-sm"
      style={{ pointerEvents: 'auto', backgroundColor: 'rgba(0,0,0,0.4)' }}
    >
      {/* Backdrop */}
      <div className="absolute inset-0" onClick={onClose} />

      {/* Modal */}
      <div className="relative bg-white/95 dark:bg-gray-900/95 backdrop-blur-md rounded-2xl shadow-2xl max-w-5xl w-full mx-4 max-h-[90vh] overflow-hidden border border-white/20 dark:border-gray-700/50">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200/50 dark:border-gray-700/50 bg-gradient-to-r from-blue-50/80 to-purple-50/80 dark:from-gray-800/80 dark:to-gray-800/80 backdrop-blur-sm">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
              <span className="text-white font-bold text-sm">AI</span>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              {modalTitle ||
                (filteredItems.length === 1 ? selectedItem?.title : ``)}
            </h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="flex h-[600px]">
          {/* Left Sidebar - Items List */}
          <div className="w-2/5 border-r border-gray-200/50 dark:border-gray-700/50 bg-gray-50/80 dark:bg-gray-800/60 backdrop-blur-sm flex flex-col">
            {/* Scrollable Items List */}
            <div className="flex-1 overflow-y-auto">
              <div className="p-4 space-y-2">
                {filteredItems.map((item, index) => (
                  <div
                    key={item.id || index}
                    onClick={() => setSelectedItem(item)}
                    className={cn(
                      'p-3 rounded-lg cursor-pointer transition-all duration-200',
                      'hover:bg-gray-50 dark:hover:bg-gray-800',
                      selectedItem === item
                        ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700'
                        : 'border border-transparent'
                    )}
                  >
                    <div className="flex items-start gap-3">
                      {item.icon && (
                        <div className="flex-shrink-0 mt-0.5">{item.icon}</div>
                      )}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-medium text-gray-900 dark:text-white text-sm truncate">
                            {item.title}
                          </h3>
                          {(item as any).isHot && (
                            <span className="px-1.5 py-0.5 text-xs font-medium bg-red-100 text-red-600 rounded">
                              {tm('menu.hot')}
                            </span>
                          )}
                          {(item as any).isNew && (
                            <span className="px-1.5 py-0.5 text-xs font-medium bg-green-100 text-green-600 rounded">
                              {tm('menu.new')}
                            </span>
                          )}
                          {(item as any).coming && (
                            <span className="px-1.5 py-0.5 text-xs font-medium bg-gray-100 text-gray-600 rounded">
                              {tm('menu.coming')}
                            </span>
                          )}
                        </div>
                        <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-2">
                          {item.desc}
                        </p>
                        {(item as any).point && (
                          <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                            {(item as any).point} {tm('points')}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* View More Link - Fixed at bottom left */}
            <div className="p-4 border-t border-gray-200/50 dark:border-gray-700/50">
              <Link
                href={viewMoreHref}
                className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors underline"
              >
                {tm('viewMore')}
              </Link>
            </div>
          </div>

          {/* Right Content - Selected Item Details */}
          <div className="flex-1 p-6 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
            {selectedItem && (
              <div className="h-full flex flex-col justify-between">
                {/* Media Preview */}
                <div className="h-96 mb-6 flex-shrink-0">
                  {(selectedItem as any).mediaUrl ? (
                    <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 rounded-xl overflow-hidden shadow-inner">
                      {(selectedItem as any).isVideo ? (
                        <video
                          src={(selectedItem as any).mediaUrl}
                          autoPlay
                          loop
                          muted
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <img
                          src={(selectedItem as any).mediaUrl}
                          alt={selectedItem.title}
                          className="w-full h-full object-cover"
                        />
                      )}
                    </div>
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 rounded-xl flex items-center justify-center">
                      <div className="text-center">
                        <div className="w-16 h-16 mx-auto mb-4 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                          {(selectedItem as any).icon || (
                            <span className="text-2xl">🎨</span>
                          )}
                        </div>
                        <p className="text-gray-500 dark:text-gray-400">
                          {tm('previewComingSoon')}
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Item Info */}
                <div className="space-y-4 min-h-0">
                  <div className="flex flex-col justify-between items-start">
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                        {selectedItem.title}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400">
                        {selectedItem.desc}
                      </p>
                    </div>

                    {/* Try Now Button - Positioned on the right */}
                    <div className="ml-6 self-end flex-shrink-0">
                      <Link
                        href={selectedItem.seoHref}
                        className={`${
                          (selectedItem as any).coming || isLoading
                            ? 'pointer-events-none'
                            : ''
                        }`}
                      >
                        <Button
                          disabled={(selectedItem as any).coming || isLoading}
                          className={cn(
                            'px-8 py-3 font-semibold text-base transition-all duration-200 rounded-xl',
                            (selectedItem as any).coming || isLoading
                              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                              : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transform hover:scale-[1.02]'
                          )}
                        >
                          {(selectedItem as any).coming
                            ? tm('menu.coming')
                            : isLoading
                            ? tm('loading')
                            : tp('tryNowButton')}
                        </Button>
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
