/**
 * 积分配置数据
 * 纯配置文件，包含所有积分相关的配置数据
 */

// ========== 类型定义 ==========
//
export type ApiProvider = 'piapi' | 'kieai' | 'kling' | 'remaker' | 'suno'
export type MediaType = 'image' | 'video' | 'audio'
export type Category =
  | 'text2image'
  | 'image2image'
  | 'text2video'
  | 'image2video'

const taskTypePointConfig = {
  // PiAPI支持的任务类型
  'ai-face-swap': {
    taskType: 'face-swap' as const,
    apiProvider: 'piapi',
    mediaType: 'image',
    category: 'image2image',
    pathname: '/ai/face-swap',
    seoPathname: '/tools/ai-face-swap',
    description: '人脸替换',
    pointRules: [
      {
        isDefault: true,
        points: 8,
        description: '标准人脸替换',
      },
    ],
    menuCategory: 'fun',
    isHot: true,
    iconType: 'zap',
    mediaUrl: '/images/ai-face-swap/use-case-1.jpg',
    titleKey: 'tools.fun.items.aiFaceSwap.title',
    descKey: 'tools.fun.items.aiFaceSwap.desc',
  },

  'text-to-video': {
    apiProvider: 'kieai',
    mediaType: 'video',
    category: 'text2video',
    taskType: 'text-to-video' as const,
    pathname: '/ai/text-to-video',
    seoPathname: '/tools/text-to-video',
    description: '文生视频',
    pointRules: [
      {
        isDefault: true,
        points: 6,
        description: '文生视频',
      },
    ],
    menuCategory: 'fun',
    isVideo: true,
    iconType: 'zap',
    mediaUrl:
      'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/ai-hug/A-2.mp4',
    titleKey: '文生视频',
    descKey: '文生视频描述',
  },

  'ai-hug': {
    apiProvider: 'piapi',
    mediaType: 'video',
    category: 'image2video',
    taskType: 'ai_hug' as const,
    pathname: '/ai/ai-hug',
    seoPathname: '/tools/ai-hug',
    description: 'AI拥抱视频生成',
    pointRules: [
      {
        isDefault: true,
        points: 12,
        description: '标准AI拥抱视频',
      },
    ],
    menuCategory: 'fun',
    isVideo: true,
    iconType: 'zap',
    mediaUrl:
      'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/ai-hug/A-2.mp4',
    titleKey: 'tools.fun.items.aiHug.title',
    descKey: 'tools.fun.items.aiHug.desc',
  },

  upscale: {
    apiProvider: 'piapi',
    mediaType: 'image',
    category: 'image2image',
    taskType: 'upscale',
    pathname: '/ai/image-upscaler',
    seoPathname: '/tools/upscaler',
    description: '图像放大',
    pointRules: [
      {
        isDefault: true,
        points: 3,
        description: '标准图像放大',
      },
    ],
    menuCategory: 'imageTools',
    bgRemove: true,
    beforeImage: '/samples/upscale_before.webp',
    afterImage: '/samples/upscale_after.webp',
    iconType: 'camera',
    titleKey: 'tools.imageTools.items.upscale.title',
    descKey: 'tools.imageTools.items.upscale.desc',
  },

  'background-removal': {
    apiProvider: 'piapi',
    mediaType: 'image',
    category: 'image2image',
    taskType: 'background-remove',
    pathname: '/ai/background-removal',
    seoPathname: '/tools/background-removal',
    description: '背景移除',
    pointRules: [
      {
        isDefault: true,
        points: 3,
        description: '标准背景移除',
      },
    ],
    menuCategory: 'imageTools',
    bgRemove: true,
    beforeImage: '/images/ai-image-background-remover/woman-before.png',
    afterImage: '/images/ai-image-background-remover/woman-after.png',
    iconType: 'settings',
    mediaUrl: '/images/ai-clothes-changer/use-case-1.jpg',
    titleKey: 'tools.imageTools.items.backgroundRemoval.title',
    descKey: 'tools.imageTools.items.backgroundRemoval.desc',
  },

  // KieAI支持的任务类型
  'ai-smile': {
    apiProvider: 'kieai',
    mediaType: 'video',
    category: 'image2video',
    taskType: 'aismile',
    pathname: '/ai/ai-smile',
    seoPathname: '/tools/ai-smile',
    description: 'AI微笑视频生成',
    pointRules: [
      {
        isDefault: true,
        points: 12,
        description: '标准AI微笑视频',
        conditions: (params: any) => {
          return params.duration === 5 && params.quality === '720p'
        },
      },
      {
        isDefault: false,
        points: 15,
        description: '8秒或1080p视频',
        conditions: (params: any) => {
          return params.duration === 8 || params.quality === '1080p'
        },
      },
    ],
    menuCategory: 'videoCreation',
    isVideo: true,
    iconType: 'camera',
    titleKey: 'tools.videoCreation.items.aiSmile.title',
    descKey: 'tools.videoCreation.items.aiSmile.desc',
  },

  'ai-image-to-video': {
    apiProvider: 'kieai',
    mediaType: 'video',
    category: 'image2video',
    taskType: 'imagetovideo',
    pathname: '/ai/image-to-video',
    seoPathname: '/tools/ai-image-to-video',
    description: '图像转视频',
    pointRules: [
      {
        isDefault: true,
        points: 7,
        description: '5秒720p视频',
        conditions: (params: any) => {
          return params.duration === 5 && params.quality === '720p'
        },
      },
      {
        isDefault: false,
        points: 15,
        description: '8秒或1080p视频',
        conditions: (params: any) => {
          return params.duration === 8 || params.quality === '1080p'
        },
      },
    ],
    menuCategory: 'imageTools',
    isHot: true,
    isVideo: true,
    iconType: 'camera',
    mediaUrl:
      'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/photo-to-video/B2.mp4',
    titleKey: 'tools.imageTools.items.imageToVideo.title',
    descKey: 'tools.imageTools.items.imageToVideo.desc',
  },

  'photo-to-anime': {
    apiProvider: 'kieai',
    mediaType: 'image',
    category: 'image2image',
    taskType: 'photo-to-anime',
    pathname: '/ai/photo-to-anime',
    seoPathname: '/tools/photo-to-anime',
    description: '照片转动漫风格',
    pointRules: [
      {
        isDefault: true,
        points: 10,
        description: '标准动漫风格转换',
      },
    ],
    menuCategory: 'creative',
    iconType: 'zap',
    mediaUrl: '/images/photo-to-anime/use-case-1.jpg',
    titleKey: 'tools.creative.items.photoToAnime.title',
    descKey: 'tools.creative.items.photoToAnime.desc',
  },

  'photo-restore': {
    apiProvider: 'kieai',
    mediaType: 'image',
    category: 'image2image',
    taskType: 'photo-restoration',
    pathname: '/ai/photo-restoration',
    seoPathname: '/tools/photo-restore',
    description: '照片修复',
    pointRules: [
      {
        isDefault: true,
        points: 3,
        description: '标准照片修复',
      },
    ],
    menuCategory: 'memory',
    isHot: true,
    iconType: 'fileText',
    mediaUrl: '/samples/enhancer restore old photo.gif',
    titleKey: 'tools.memory.items.photoRestore.title',
    descKey: 'tools.memory.items.photoRestore.desc',
  },

  'old-filter': {
    apiProvider: 'kieai',
    mediaType: 'image',
    category: 'image2image',
    taskType: 'old-filter',
    pathname: '/ai/old-filter',
    seoPathname: '/tools/old-filter',
    description: '老照片滤镜',
    pointRules: [
      {
        isDefault: true,
        points: 2,
        description: '标准老照片滤镜',
      },
    ],
    menuCategory: 'memory',
    isNew: true,
    iconType: 'fileText',
    mediaUrl: '/samples/age-case6.png',
    titleKey: 'tools.memory.items.oldFilter.title',
    descKey: 'tools.memory.items.oldFilter.desc',
  },

  'text-to-image': {
    apiProvider: 'kieai',
    mediaType: 'image',
    category: 'text2image',
    taskType: 'text-to-image',
    pathname: '/ai/text-to-image',
    seoPathname: '/tools/text-to-image',
    description: '文本生成图像',
    pointRules: [
      {
        isDefault: true,
        points: 6,
        description: '标准文本生成图像',
      },
    ],
    menuCategory: 'imageTools',
    isHot: true,
    iconType: 'image',
    mediaUrl: '/images/home/<USER>',
    titleKey: 'tools.imageTools.items.textToImage.title',
    descKey: 'tools.imageTools.items.textToImage.desc',
  },

  'image-to-image': {
    apiProvider: 'kieai',
    mediaType: 'image',
    category: 'image2image',
    taskType: 'image-to-image',
    seoPathname: '/tools/image-to-image',
    pathname: '/ai/image-to-image',
    description: '图像转图像',
    pointRules: [
      {
        isDefault: true,
        points: 3,
        description: '标准图像转换',
      },
    ],
    menuCategory: 'imageTools',
    isHot: true,
    coming: true,
    iconType: 'camera',
    mediaUrl: '/images/ai-clothes-changer/use-case-2.jpg',
    titleKey: 'tools.imageTools.items.imageToImage.title',
    descKey: 'tools.imageTools.items.imageToImage.desc',
  },

  ghibli: {
    apiProvider: 'kieai',
    mediaType: 'image',
    category: 'image2image',
    taskType: 'ghibli',
    pathname: '/ai/ghibli',
    seoPathname: '/tools/ghibli',
    description: '吉卜力风格转换',
    pointRules: [
      {
        isDefault: true,
        points: 5,
        description: '标准吉卜力风格',
      },
    ],
    menuCategory: 'creative',
    isHot: true,
    iconType: 'zap',
    mediaUrl: '/ghibli/ghibli-1.jpg',
    titleKey: 'tools.creative.items.ghibli.title',
    descKey: 'tools.creative.items.ghibli.desc',
  },

  'photo-colorizer': {
    apiProvider: 'kieai',
    mediaType: 'image',
    category: 'image2image',
    taskType: 'photo-colorizer',
    pathname: '/ai/photo-colorizer',
    seoPathname: '/tools/photo-colorizer',
    description: '照片上色',
    pointRules: [
      {
        isDefault: true,
        points: 3,
        description: '标准照片上色',
      },
    ],
    menuCategory: 'memory',
    iconType: 'fileText',
    mediaUrl: '/images/color-enhance/photo-colorize.png',
    titleKey: 'tools.memory.items.colorize.title',
    descKey: 'tools.memory.items.colorize.desc',
  },

  // Kling支持的任务类型
  'ai-clothes-changer': {
    apiProvider: 'kling',
    mediaType: 'image',
    category: 'image2image',
    taskType: 'ai_try_on',
    pathname: '/ai/virtual-try-on',
    seoPathname: '/tools/ai-clothes-changer',
    description: '虚拟试穿',
    pointRules: [
      {
        isDefault: true,
        points: 10,
        description: '标准虚拟试穿',
      },
    ],
    menuCategory: 'business',
    isHot: true,
    iconType: 'image',
    mediaUrl: '/images/ai-clothes-changer/use-case-3.jpg',
    titleKey: 'tools.business.items.aiClothesChanger.title',
    descKey: 'tools.business.items.aiClothesChanger.desc',
  },

  'memory-video': {
    apiProvider: 'piapi',
    mediaType: 'video',
    category: 'image2video',
    taskType: 'memory_video',
    pathname: '/ai/memory-video',
    seoPathname: '/tools/memory-video',
    description: '记忆视频生成',
    pointRules: [
      {
        isDefault: true,
        points: 10,
        description: '标准记忆视频生成',
      },
    ],
    menuCategory: 'memory',
    isVideo: true,
    iconType: 'fileText',
    mediaUrl: '/samples/memorial-video-maker/A-4-after.mp4',
    titleKey: 'tools.memory.items.memoryVideo.title',
    descKey: 'tools.memory.items.memoryVideo.desc',
  },

  // 其他独立API
  'ai-tattoo-generator': {
    apiProvider: 'remaker',
    mediaType: 'image',
    category: 'text2image',
    taskType: 'tattoo',
    pathname: '/ai/tattoo',
    seoPathname: '/tools/ai-tattoo-generator',
    description: 'AI纹身生成',
    pointRules: [
      {
        isDefault: true,
        points: 3,
        description: '标准纹身生成',
      },
    ],
    menuCategory: 'fun',
    isHot: true,
    iconType: 'zap',
    mediaUrl: '/images/templates/tattoo.png',
    titleKey: 'tools.fun.items.aiTattooGenerator.title',
    descKey: 'tools.fun.items.aiTattooGenerator.desc',
  },

  sketch: {
    apiProvider: 'kieai',
    mediaType: 'image',
    category: 'text2image',
    taskType: 'sketch-to-image',
    pathname: '/ai/sketch-to-image',
    seoPathname: '/tools/sketch',
    description: '草图转图像',
    pointRules: [
      {
        isDefault: true,
        points: 10,
        description: '标准草图转图像',
      },
    ],
    menuCategory: 'creative',
    iconType: 'zap',
    mediaUrl: '/samples/sketch-1.png',
    titleKey: 'tools.creative.items.sketch.title',
    descKey: 'tools.creative.items.sketch.desc',
  },

  'test-consume': {
    apiProvider: 'piapi',
    mediaType: 'image',
    category: 'image2image',
    taskType: 'test-consume',
    pathname: '/ai/test-consume',
    seoPathname: '/tools/test-consume',
    description: '测试功能',
    pointRules: [
      {
        isDefault: true,
        points: 100,
        description: '标准测试功能',
      },
    ],
    menuCategory: 'utilities',
    iconType: 'settings',
    titleKey: 'tools.utilities.items.testConsume.title',
    descKey: 'tools.utilities.items.testConsume.desc',
  },

  // 新增配置项 - business 分类
  'product-video': {
    apiProvider: 'piapi',
    mediaType: 'video',
    category: 'image2video',
    taskType: 'product-video',
    pathname: '/ai/product-video',
    seoPathname: '/tools/product-video',
    description: '商品视频生成',
    pointRules: [
      {
        isDefault: true,
        points: 10,
        description: '标准商品视频生成',
      },
    ],
    menuCategory: 'business',
    coming: true,
    isVideo: true,
    iconType: 'camera',
    mediaUrl: '/videos/ai-image-to-video/product-animate.mp4',
    titleKey: 'tools.business.items.productVideo.title',
    descKey: 'tools.business.items.productVideo.desc',
  },

  'custom-model': {
    apiProvider: 'piapi',
    mediaType: 'image',
    category: 'image2image',
    taskType: 'custom-model',
    pathname: '/ai/custom-model',
    seoPathname: '/tools/custom-model',
    description: '自定义模型',
    pointRules: [
      {
        isDefault: true,
        points: 15,
        description: '标准自定义模型',
      },
    ],
    menuCategory: 'business',
    coming: true,
    iconType: 'settings',
    mediaUrl: '/images/color-enhance/photo-custom.png',
    titleKey: 'tools.business.items.customModel.title',
    descKey: 'tools.business.items.customModel.desc',
  },

  // creative 分类
  'ai-art-generator-free': {
    apiProvider: 'kieai',
    mediaType: 'image',
    category: 'text2image',
    taskType: 'ai-art-generator-free',
    pathname: '/ai/ai-art-generator-free',
    seoPathname: '/tools/ai-art-generator-free',
    description: 'AI艺术生成器',
    pointRules: [
      {
        isDefault: true,
        points: 2,
        description: '标准AI艺术生成',
      },
    ],
    menuCategory: 'creative',
    iconType: 'zap',
    mediaUrl: '/images/ai-art-generator-free/Snipaste_2025-06-29_15-52-03.png',
    titleKey: 'tools.creative.items.aiArtGenerator.title',
    descKey: 'tools.creative.items.aiArtGenerator.desc',
  },

  'color-enhance': {
    apiProvider: 'kieai',
    mediaType: 'image',
    category: 'image2image',
    taskType: 'color-enhance',
    pathname: '/ai/color-enhance',
    seoPathname: '/tools/color-enhance',
    description: '颜色增强',
    pointRules: [
      {
        isDefault: true,
        points: 2,
        description: '标准颜色增强',
      },
    ],
    mediaUrl: '/images/color-enhance/before-after.png',
    menuCategory: 'imageTools',
    coming: true,
    iconType: 'camera',
    titleKey: 'tools.imageTools.items.enhance.title',
    descKey: 'tools.imageTools.items.enhance.desc',
  },

  // imageTools 分类
  'element-extraction': {
    apiProvider: 'kieai',
    mediaType: 'image',
    category: 'image2image',
    taskType: 'element-extraction',
    pathname: '/ai/element-extraction',
    seoPathname: '/tools/element-extraction',
    description: '元素提取',
    pointRules: [
      {
        isDefault: true,
        points: 3,
        description: '标准元素提取',
      },
    ],
    menuCategory: 'imageTools',
    coming: true,
    iconType: 'settings',
    mediaUrl: '/images/templates/extract.png',
    titleKey: 'tools.imageTools.items.elementExtraction.title',
    descKey: 'tools.imageTools.items.elementExtraction.desc',
  },

  'background-replacement': {
    apiProvider: 'kieai',
    mediaType: 'image',
    category: 'image2image',
    taskType: 'background-replacement',
    pathname: '/ai/background-replacement',
    seoPathname: '/tools/background-replacement',
    description: '背景替换',
    pointRules: [
      {
        isDefault: true,
        points: 4,
        description: '标准背景替换',
      },
    ],
    menuCategory: 'imageTools',
    coming: true,
    bgRemove: true,
    beforeImage:
      '/images/ai-background-replace/stunning-quality-car-before.png',
    afterImage:
      '/images/ai-background-replace/stunning-quality-car-replace.png',
    iconType: 'settings',
    titleKey: 'tools.imageTools.items.backgroundReplacement.title',
    descKey: 'tools.imageTools.items.backgroundReplacement.desc',
  },

  'ai-remove-watermark': {
    apiProvider: 'kieai',
    mediaType: 'image',
    category: 'image2image',
    taskType: 'ai-remove-watermark',
    pathname: '/ai/ai-remove-watermark',
    seoPathname: '/tools/ai-remove-watermark',
    description: 'AI去水印',
    pointRules: [
      {
        isDefault: true,
        points: 3,
        description: '标准AI去水印',
      },
    ],
    menuCategory: 'imageTools',
    iconType: 'settings',
    mediaUrl: '/images/ai-remove-watermark/sample-before1.png',
    titleKey: 'tools.imageTools.items.aiRemoveWatermark.title',
    descKey: 'tools.imageTools.items.aiRemoveWatermark.desc',
  },

  // videoCreation 分类
  'photo-to-video': {
    apiProvider: 'kieai',
    mediaType: 'video',
    category: 'image2video',
    taskType: 'photo-to-video',
    pathname: '/ai/photo-to-video',
    seoPathname: '/tools/photo-to-video',
    description: '照片转视频',
    pointRules: [
      {
        isDefault: true,
        points: 8,
        description: '标准照片转视频',
      },
    ],
    menuCategory: 'videoCreation',
    coming: true,
    isVideo: true,
    iconType: 'camera',
    mediaUrl:
      'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/photo-to-video/D2.mp4',
    titleKey: 'tools.videoCreation.items.photoToVideo.title',
    descKey: 'tools.videoCreation.items.photoToVideo.desc',
  },

  'ai-face-swap-video': {
    apiProvider: 'kieai',
    mediaType: 'video',
    category: 'image2video',
    taskType: 'ai-face-swap-video',
    pathname: '/ai/ai-face-swap-video',
    seoPathname: '/tools/ai-face-swap-video',
    description: 'AI换脸视频',
    pointRules: [
      {
        isDefault: true,
        points: 12,
        description: '标准AI换脸视频',
      },
    ],
    menuCategory: 'videoCreation',
    isVideo: true,
    iconType: 'camera',
    mediaUrl: '/images/templates/face-swap.png',
    titleKey: 'tools.videoCreation.items.aiFaceSwapVideo.title',
    descKey: 'tools.videoCreation.items.aiFaceSwapVideo.desc',
  },

  'person-animation': {
    apiProvider: 'kieai',
    mediaType: 'video',
    category: 'image2video',
    taskType: 'person-animation',
    pathname: '/ai/person-animation',
    seoPathname: '/tools/person-animation',
    description: '人物动画',
    pointRules: [
      {
        isDefault: true,
        points: 8,
        description: '标准人物动画',
      },
    ],
    menuCategory: 'videoCreation',
    coming: true,
    isVideo: true,
    iconType: 'camera',
    mediaUrl:
      'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/photo-to-video/C2.mp4',
    titleKey: 'tools.videoCreation.items.personAnimation.title',
    descKey: 'tools.videoCreation.items.personAnimation.desc',
  },

  'scene-video': {
    apiProvider: 'kieai',
    mediaType: 'video',
    category: 'image2video',
    taskType: 'scene-video',
    pathname: '/ai/scene-video',
    seoPathname: '/tools/scene-video',
    description: '场景视频',
    pointRules: [
      {
        isDefault: true,
        points: 14,
        description: '标准场景视频',
      },
    ],
    menuCategory: 'videoCreation',
    coming: true,
    isVideo: true,
    iconType: 'camera',
    mediaUrl: '/videos/templates/scree_1_V2.mp4',
    titleKey: 'tools.videoCreation.items.sceneVideo.title',
    descKey: 'tools.videoCreation.items.sceneVideo.desc',
  },

  // utilities 分类
  'batch-process': {
    apiProvider: 'piapi',
    mediaType: 'image',
    category: 'image2image',
    taskType: 'batch-process',
    pathname: '/ai/batch-process',
    seoPathname: '/tools/batch-process',
    description: '批量处理',
    pointRules: [
      {
        isDefault: true,
        points: 20,
        description: '标准批量处理',
      },
    ],
    menuCategory: 'utilities',
    coming: true,
    iconType: 'settings',
    mediaUrl: '/images/templates/bg-remove.webp',
    titleKey: 'tools.utilities.items.batchProcess.title',
    descKey: 'tools.utilities.items.batchProcess.desc',
  },

  // fun 分类
  'ai-portrait': {
    apiProvider: 'kieai',
    mediaType: 'image',
    category: 'image2image',
    taskType: 'ai-portrait',
    pathname: '/ai/ai-portrait',
    seoPathname: '/tools/ai-portrait',
    description: 'AI肖像',
    pointRules: [
      {
        isDefault: true,
        points: 8,
        description: '标准AI肖像',
      },
    ],
    menuCategory: 'fun',
    coming: true,
    iconType: 'zap',
    mediaUrl: '/images/templates/portrait.png',
    titleKey: 'tools.fun.items.aiPortrait.title',
    descKey: 'tools.fun.items.aiPortrait.desc',
  },

  'meme-generator': {
    apiProvider: 'kieai',
    mediaType: 'image',
    category: 'text2image',
    taskType: 'meme-generator',
    pathname: '/ai/meme-generator',
    seoPathname: '/tools/meme-generator',
    description: '表情包生成器',
    pointRules: [
      {
        isDefault: true,
        points: 2,
        description: '标准表情包生成',
      },
    ],
    menuCategory: 'fun',
    coming: true,
    iconType: 'zap',
    mediaUrl: '/images/templates/meme.png',
    titleKey: 'tools.fun.items.memeGenerator.title',
    descKey: 'tools.fun.items.memeGenerator.desc',
  },

  'image-extender': {
    apiProvider: 'kieai',
    mediaType: 'image',
    category: 'image2image',
    taskType: 'image-extender',
    pathname: '/ai/image-extender',
    seoPathname: '/tools/image-extender',
    description: '智能扩图',
    pointRules: [
      {
        isDefault: true,
        points: 6,
        description: '标准扩图',
      },
    ],
    // menuCategory: 'imageTools',
    // iconType: 'camera',
    // mediaUrl: '/images/templates/outpainting.png',
    // titleKey: 'tools.imageTools.items.imageExpansion.title',
    // descKey: 'tools.imageTools.items.imageExpansion.desc',
  },

  // ========== 预留功能 - 注释掉的菜单项 ==========

  // 商业功能预留
  // 'scene-generator': {
  //   apiProvider: 'piapi',
  //   mediaType: 'image',
  //   category: 'image2image',
  //   pathname: '/ai/scene-generator',
  //   description: '场景图生成',
  //   pointRules: [
  //     {
  //       isDefault: true,
  //       points: 10,
  //       description: '标准场景图生成',
  //     },
  //   ],
  //   menuCategory: 'business',
  //   coming: true,
  //   iconType: 'camera',
  //   titleKey: 'tools.business.items.sceneGenerator.title',
  //   descKey: 'tools.business.items.sceneGenerator.desc',
  // },

  // 'model-poses': {
  //   apiProvider: 'piapi',
  //   mediaType: 'image',
  //   category: 'image2image',
  //   pathname: '/ai/model-poses',
  //   description: '高转化率姿势',
  //   pointRules: [
  //     {
  //       isDefault: true,
  //       points: 8,
  //       description: '标准姿势生成',
  //     },
  //   ],
  //   menuCategory: 'business',
  //   coming: true,
  //   iconType: 'camera',
  //   titleKey: 'tools.business.items.modelPoses.title',
  //   descKey: 'tools.business.items.modelPoses.desc',
  // },

  // 'logo-generator': {
  //   apiProvider: 'piapi',
  //   mediaType: 'image',
  //   category: 'text2image',
  //   pathname: '/ai/logo-generator',
  //   description: 'AI Logo生成器',
  //   pointRules: [
  //     {
  //       isDefault: true,
  //       points: 6,
  //       description: '标准Logo生成',
  //     },
  //   ],
  //   menuCategory: 'business',
  //   iconType: 'image',
  //   titleKey: 'tools.business.items.logoGenerator.title',
  //   descKey: 'tools.business.items.logoGenerator.desc',
  // },

  // 创意功能预留
  // 'poster': {
  //   apiProvider: 'piapi',
  //   mediaType: 'image',
  //   category: 'text2image',
  //   pathname: '/ai/poster',
  //   description: '营销海报',
  //   pointRules: [
  //     {
  //       isDefault: true,
  //       points: 8,
  //       description: '标准海报生成',
  //     },
  //   ],
  //   menuCategory: 'creative',
  //   coming: true,
  //   iconType: 'image',
  //   titleKey: 'tools.creative.items.poster.title',
  //   descKey: 'tools.creative.items.poster.desc',
  // },

  // 记忆功能预留
  // 'memorial-video': {
  //   apiProvider: 'piapi',
  //   mediaType: 'video',
  //   category: 'image2video',
  //   pathname: '/ai/memorial-video',
  //   description: '专业追思视频制作',
  //   pointRules: [
  //     {
  //       isDefault: true,
  //       points: 15,
  //       description: '标准追思视频',
  //     },
  //   ],
  //   menuCategory: 'memory',
  //   coming: true,
  //   isVideo: true,
  //   iconType: 'fileText',
  //   titleKey: 'tools.memory.items.memorialVideo.title',
  //   descKey: 'tools.memory.items.memorialVideo.desc',
  // },

  // 图像工具预留
  // 'ai-retouch': {
  //   apiProvider: 'piapi',
  //   mediaType: 'image',
  //   category: 'image2image',
  //   pathname: '/ai/ai-retouch',
  //   description: 'AI修图大师',
  //   pointRules: [
  //     {
  //       isDefault: true,
  //       points: 5,
  //       description: '标准修图',
  //     },
  //   ],
  //   menuCategory: 'imageTools',
  //   coming: true,
  //   iconType: 'camera',
  //   titleKey: 'tools.imageTools.items.aiRetouch.title',
  //   descKey: 'tools.imageTools.items.aiRetouch.desc',
  // },

  // 'object-removal': {
  //   apiProvider: 'piapi',
  //   mediaType: 'image',
  //   category: 'image2image',
  //   pathname: '/ai/object-removal',
  //   description: '一键消除',
  //   pointRules: [
  //     {
  //       isDefault: true,
  //       points: 4,
  //       description: '标准对象移除',
  //     },
  //   ],
  //   menuCategory: 'imageTools',
  //   coming: true,
  //   iconType: 'settings',
  //   titleKey: 'tools.imageTools.items.objectRemoval.title',
  //   descKey: 'tools.imageTools.items.objectRemoval.desc',
  // },

  // 'outpainting': {
  //   apiProvider: 'piapi',
  //   mediaType: 'image',
  //   category: 'image2image',
  //   pathname: '/ai/outpainting',
  //   description: '智能扩图',
  //   pointRules: [
  //     {
  //       isDefault: true,
  //       points: 6,
  //       description: '标准扩图',
  //     },
  //   ],
  //   menuCategory: 'imageTools',
  //   isNew: true,
  //   iconType: 'camera',
  //   titleKey: 'tools.imageTools.items.outpainting.title',
  //   descKey: 'tools.imageTools.items.outpainting.desc',
  // },

  // 视频创作预留
  // 'product-showcase': {
  //   apiProvider: 'piapi',
  //   mediaType: 'video',
  //   category: 'image2video',
  //   pathname: '/ai/product-showcase',
  //   description: '商品展示视频',
  //   pointRules: [
  //     {
  //       isDefault: true,
  //       points: 12,
  //       description: '标准商品展示视频',
  //     },
  //   ],
  //   menuCategory: 'videoCreation',
  //   coming: true,
  //   isVideo: true,
  //   iconType: 'camera',
  //   titleKey: 'tools.videoCreation.items.productShowcase.title',
  //   descKey: 'tools.videoCreation.items.productShowcase.desc',
  // },

  // 'video-style': {
  //   apiProvider: 'piapi',
  //   mediaType: 'video',
  //   category: 'image2video',
  //   pathname: '/ai/video-style',
  //   description: '视频风格转换',
  //   pointRules: [
  //     {
  //       isDefault: true,
  //       points: 10,
  //       description: '标准视频风格转换',
  //     },
  //   ],
  //   menuCategory: 'videoCreation',
  //   coming: true,
  //   isVideo: true,
  //   iconType: 'camera',
  //   titleKey: 'tools.videoCreation.items.videoStyle.title',
  //   descKey: 'tools.videoCreation.items.videoStyle.desc',
  // },
} as const satisfies Record<string, TaskTypePointConfig>

// 从配置对象推断出TaskType类型
export type TaskType =
  (typeof taskTypePointConfig)[keyof typeof taskTypePointConfig]['taskType']

export type TaskId = keyof typeof taskTypePointConfig

export interface PointCondition {
  isDefault: boolean
  points: number
  conditions?: (params: any) => boolean
  description?: string
}

// 菜单分类类型
export type MenuCategory =
  | 'business'
  | 'creative'
  | 'memory'
  | 'imageTools'
  | 'videoCreation'
  | 'utilities'
  | 'fun'

// 图标类型
export type IconType = 'camera' | 'image' | 'fileText' | 'settings' | 'zap'

export interface TaskTypePointConfig<T extends string = string> {
  taskType: T
  apiProvider: ApiProvider
  mediaType: MediaType
  category: Category
  pathname: string
  seoPathname: string
  description: string
  pointRules: readonly PointCondition[]
  // 菜单相关字段
  menuCategory?: MenuCategory
  isHot?: boolean
  isNew?: boolean
  coming?: boolean
  isVideo?: boolean
  iconType?: IconType
  mediaUrl?: string
  bgRemove?: boolean
  beforeImage?: string
  afterImage?: string
  // 翻译 key
  titleKey?: string
  descKey?: string
}

export interface PointCalculationParams {
  taskType: TaskType
  duration?: number
  quality?: string
  aspectRatio?: string
  batchSize?: number
  nVariants?: string | number
  [key: string]: any
}

export interface PointCalculationResult {
  points: number
  description: string
  rule: PointCondition
}

// ========== 数据驱动函数 ==========

/**
 * 获取任务配置
 */
export const getTaskConfig = (
  taskId: TaskId
): TaskTypePointConfig | undefined => {
  return taskTypePointConfig[taskId]
}

/**
 * 获取任务路径
 */
export const getTaskPathname = (taskId: TaskId, isAiPage: boolean): string => {
  const config = getTaskConfig(taskId)
  if (!config) {
    // 如果没有配置，返回默认路径
    return isAiPage ? `/ai/${taskId}` : `/tools/${taskId}`
  }

  if (isAiPage) {
    return config.pathname
  } else {
    // 将 /ai/ 路径转换为 /tools/ 路径
    return config.seoPathname
  }
}

/**
 * 获取默认积分
 */
export const getDefaultPoints = (taskId: TaskId): number => {
  const config = getTaskConfig(taskId)
  if (!config) return 0

  const defaultRule = config.pointRules.find((rule) => rule.isDefault)
  return defaultRule?.points || 0
}

/**
 * 根据图标类型返回图标类型字符串
 */
export const getTaskIconType = (iconType: IconType): IconType => {
  return iconType
}

/**
 * 获取任务的所有配置信息
 */
export const getTaskInfo = (taskId: TaskId) => {
  const config = getTaskConfig(taskId)
  if (!config) return null

  return {
    taskId,
    pathname: config.pathname,
    seoPathname: config.seoPathname,
    description: config.description,
    points: getDefaultPoints(taskId),
    menuCategory: config.menuCategory,
    isHot: config.isHot,
    isNew: config.isNew,
    coming: config.coming,
    isVideo: config.isVideo,
    iconType: config.iconType,
    mediaUrl: config.mediaUrl,
    bgRemove: config.bgRemove,
    beforeImage: config.beforeImage,
    afterImage: config.afterImage,
    titleKey: config.titleKey,
    descKey: config.descKey,
  }
}

/**
 * 获取所有任务配置
 */
export const getAllTaskConfigs = () => {
  return taskTypePointConfig
}

// ========== 导出 ==========

// 导出配置数据
export { taskTypePointConfig }
