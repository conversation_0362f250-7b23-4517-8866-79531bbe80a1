import { useAtom } from 'jotai'
import { showMenuModalByItemIdsAtom } from '@shared/stores/modalAtoms'
import { getMenuCategories } from '@shared/components/Logo'

// 从 getMenuCategories 返回类型中推断分类 ID
type MenuCategoriesReturn = ReturnType<typeof getMenuCategories>

// 动态推断分类ID类型
export type CategoryId = keyof MenuCategoriesReturn

type Id = MenuCategoriesReturn[keyof MenuCategoriesReturn]['items'][0]['id']
// 从实际菜单数据中提取的所有项目ID类型
export type ItemId =
  // Business 分类
  | 'product-video'
  | 'ai-clothes-changer'
  | 'custom-model'

  // Creative 分类
  | 'ghibli'
  | 'photo-to-anime'
  | 'ai-art-generator-free'
  | 'sketch-to-image'

  // Memory 分类
  | 'photo-restore'
  | 'colorize'
  | 'color-enhance'
  | 'old-filter'
  | 'memory-video'

  // Image Tools 分类
  | 'image-to-image'
  | 'text-to-image'
  | 'image-to-video'
  | 'background-removal'
  | 'element-extraction'
  | 'upscale'
  | 'background-replacement'
  | 'enhance'

  // Video Creation 分类
  | 'photo-to-video'
  | 'ai-face-swap-video'
  | 'person-animation'
  | 'scene-video'

  // Utilities 分类
  | 'batch-process'

  // Fun 分类
  | 'ai-portrait'
  | 'ai-face-swap'
  | 'ai-hug'
  | 'ai-tattoo-generator'
  | 'meme-generator'
  | Id
/**
 * 菜单模态框的便捷hook
 */
export function useMenuModal() {
  const [, showMenuModalByItemIds] = useAtom(showMenuModalByItemIdsAtom)

  /**
   * 显示菜单模态框（按项目ID）
   * @param itemIds 项目ID数组或单个ID，支持类型推断
   * @param config 可选的额外配置
   */
  const openMenuModal = (
    itemIds: ItemId[] | ItemId | CategoryId,
    config?: any
  ) => {
    showMenuModalByItemIds(itemIds, config)
  }

  return {
    openMenuModal,
  }
}
