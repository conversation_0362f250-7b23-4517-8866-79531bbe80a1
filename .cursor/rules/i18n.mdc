---
description: 
globs: packages/i18n/translations/*.json
alwaysApply: false
---
多语言标签文件位于 `packages/i18n/translations` 文件夹中，每种语言对应一个 JSON 文件。例如，英语为 `en.json` 文件。

### 需要翻译的语言及地区：
- **en**: 美国英语（美国）
- **es**: 西班牙语（西班牙）
- **de**: 德语（德国）
- **fr**: 法语（法国）
- **ja**: 日语（日本）
- **ko**: 韩语（韩国）
- **pt**: 葡萄牙语（葡萄牙，但以巴西货币为主）
- **ru**: 俄语（俄罗斯）
- **th**: 泰语（泰国）
- **vi**: 越南语（越南）
- **zh-CN**: 简体中文（中国大陆）
- **zh-HK**: 繁体中文（香港）
- **zh-TW**: 繁体中文（台湾）

### 注意事项：
1. **专有名词和人名**保持原始英文单词，不进行翻译。
2. 请确保翻译内容语法正确，符合目标语言的文化背景和规范。
3. 提供的 JSON 文件应完整匹配原始语言文件的结构，避免遗漏或格式错误。

最终输出应该便于直接集成到项目中，确保准确性和一致性。
