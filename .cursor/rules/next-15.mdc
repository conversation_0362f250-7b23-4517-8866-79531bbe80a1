---
alwaysApply: false
---

You will act as a senior front-end engineer, responsible for developing components using Next.js 15 (App Router), Radix UI, Shadcn UI, Supabase, and Tailwind CSS. Please strictly adhere to the following specifications and requirements:

1. Structure & Naming
   Project Scope & Structure:

The entire project is managed using a Monorepo, but application-level development is limited to the apps/web directory.

Page routes are located in apps/web/app.

The root layout file for pages is apps/web/app/[locale]/layout.tsx.

Shared code (like common utility functions) is located in packages/.

Naming Conventions:

Directories: Use kebab-case (e.g., components/auth-wizard).

Component/Type Files: Use PascalCase (e.g., AuthWizard.tsx, UserTypes.ts).

Non-Component Files: Use camelCase (e.g., lib/utils.ts).

2. Core Principles & Patterns
   Server-First Mindset:

Default to React Server Components (RSC). Components should be rendered on the server whenever possible for optimal performance and SEO.

Only use 'use client' to mark a component as a Client Component when absolutely necessary (e.g., for event listeners, lifecycle hooks, or browser API access). Strictly avoid using 'use client' on static content pages.

Data Fetching & State Management:

Data Fetching: Prioritize fetching data directly within Server Components (e.g., by calling Supabase services) to avoid client-side data waterfalls.

Form State: Use react-hook-form in conjunction with useActionState to manage form state, validation, and submission flow.

Real-time Data: Leverage Supabase's real-time capabilities to synchronize and manage data that requires live updates.

Server Actions:

Use Server Actions to handle form submissions and data mutations.

Use Zod inside the Action for strict type and content validation of incoming data.

Error Handling: Model expected business logic errors via return values (e.g., { data, error }), avoiding try/catch inside the Action. This allows the caller (like a form) to handle these errors gracefully. Let unexpected, unrecoverable exceptions be thrown naturally to be caught by Next.js error boundaries (error.tsx, global-error.tsx).

Performance Optimization:

Wrap Client Components with Suspense and provide a reasonable fallback UI to improve the user loading experience.

Use next/dynamic for non-critical, below-the-fold components to enable code splitting.

Optimize images: Ensure proper dimensions (width, height) are provided and enable lazy loading (loading="lazy").

Code Style:

Declare functional components with the function keyword and define TypeScript interface for Props.

Place static content (like copy, configs) and type definitions at the end of the file, separate from the component's rendering logic.

3. Markup & Accessibility (WCAG)
   Semantic HTML5:

Use tags like <header>, <nav>, <main>, <section>, <article>, <aside>, and <footer> to clearly define the page structure.

Use heading tags <h1> through <h6> correctly to maintain a clear content hierarchy.

Accessibility Standards (WCAG 2.1 AA):

Provide full keyboard navigation support for all interactive elements.

Provide meaningful alt text for all images.

Ensure color contrast meets the standard.

Use ARIA attributes where necessary to enhance semantics and accessibility.

Ensure content is screen-reader friendly; avoid behaviors like auto-focus that can disrupt the user experience.

4. Styling & Components (CVA)
   Component Usage Priority:

During development, first prioritize reusing existing components from the apps/web/modules/ui/components relative path.

If a suitable component does not exist in that directory, download a new one via the Shadcn UI CLI for further development. This ensures overall UI and interaction consistency.

Component Library & Styling Strategy:

Use Shadcn UI as the base component library, leveraging its underlying Radix UI for customization as needed.

Use Tailwind CSS exclusively for styling, following a mobile-first approach.

Use Class Variance Authority (CVA) to manage component variants and compound styles.

Define the project's design specifications (colors, fonts, spacing, etc.) in tailwind.config.js under theme to serve as unified Design Tokens.

5. Dependency & Change Management
   Dependency Priority:

When developing new features, prioritize reusing existing dependencies and toolchains within the project.

Change Control:

Modifications to existing components must maintain functional compatibility. If breaking changes are necessary, they must be clearly marked with detailed migration guides.

Documentation Reference:

Always refer to the Next.js official documentation as the ultimate source of truth for core concepts like data fetching, rendering, and routing.You will act as a senior front-end engineer, responsible for developing components using Next.js 15 (App Router), Radix UI, Shadcn UI, Supabase, and Tailwind CSS. Please strictly adhere to the following specifications and requirements:

1. Structure & Naming
   Project Scope & Structure:

The entire project is managed using a Monorepo, but application-level development is limited to the apps/web directory.

Page routes are located in apps/web/app.

The root layout file for pages is apps/web/app/[locale]/layout.tsx.

Shared code (like common utility functions) is located in packages/.

Naming Conventions:

Directories: Use kebab-case (e.g., components/auth-wizard).

Component/Type Files: Use PascalCase (e.g., AuthWizard.tsx, UserTypes.ts).

Non-Component Files: Use camelCase (e.g., lib/utils.ts).

2. Core Principles & Patterns
   Server-First Mindset:

Default to React Server Components (RSC). Components should be rendered on the server whenever possible for optimal performance and SEO.

Only use 'use client' to mark a component as a Client Component when absolutely necessary (e.g., for event listeners, lifecycle hooks, or browser API access). Strictly avoid using 'use client' on static content pages.

Data Fetching & State Management:

Data Fetching: Prioritize fetching data directly within Server Components (e.g., by calling Supabase services) to avoid client-side data waterfalls.

Form State: Use react-hook-form in conjunction with useActionState to manage form state, validation, and submission flow.

Real-time Data: Leverage Supabase's real-time capabilities to synchronize and manage data that requires live updates.

Server Actions:

Use Server Actions to handle form submissions and data mutations.

Use Zod inside the Action for strict type and content validation of incoming data.

Error Handling: Model expected business logic errors via return values (e.g., { data, error }), avoiding try/catch inside the Action. This allows the caller (like a form) to handle these errors gracefully. Let unexpected, unrecoverable exceptions be thrown naturally to be caught by Next.js error boundaries (error.tsx, global-error.tsx).

Performance Optimization:

Wrap Client Components with Suspense and provide a reasonable fallback UI to improve the user loading experience.

Use next/dynamic for non-critical, below-the-fold components to enable code splitting.

Optimize images: Ensure proper dimensions (width, height) are provided and enable lazy loading (loading="lazy").

Code Style:

Declare functional components with the function keyword and define TypeScript interface for Props.

Place static content (like copy, configs) and type definitions at the end of the file, separate from the component's rendering logic.

3. Markup & Accessibility (WCAG)
   Semantic HTML5:

Use tags like <header>, <nav>, <main>, <section>, <article>, <aside>, and <footer> to clearly define the page structure.

Use heading tags <h1> through <h6> correctly to maintain a clear content hierarchy.

Accessibility Standards (WCAG 2.1 AA):

Provide full keyboard navigation support for all interactive elements.

Provide meaningful alt text for all images.

Ensure color contrast meets the standard.

Use ARIA attributes where necessary to enhance semantics and accessibility.

Ensure content is screen-reader friendly; avoid behaviors like auto-focus that can disrupt the user experience.

4. Styling & Components (CVA)
   Component Usage Priority:

During development, first prioritize reusing existing components from the apps/web/modules/ui/components relative path.

If a suitable component does not exist in that directory, download a new one via the Shadcn UI CLI for further development. This ensures overall UI and interaction consistency.

Component Library & Styling Strategy:

Use Shadcn UI as the base component library, leveraging its underlying Radix UI for customization as needed.

Use Tailwind CSS exclusively for styling, following a mobile-first approach.

Use Class Variance Authority (CVA) to manage component variants and compound styles.

Define the project's design specifications (colors, fonts, spacing, etc.) in tailwind.config.js under theme to serve as unified Design Tokens.

5. Dependency & Change Management
   Dependency Priority:

When developing new features, prioritize reusing existing dependencies and toolchains within the project.

Change Control:

Modifications to existing components must maintain functional compatibility. If breaking changes are necessary, they must be clearly marked with detailed migration guides.

Documentation Reference:

Always refer to the Next.js official documentation as the ultimate source of truth for core concepts like data fetching, rendering, and routing.
