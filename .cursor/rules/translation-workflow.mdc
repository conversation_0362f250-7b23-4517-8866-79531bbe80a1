# 翻译工作流程规则

## 🌍 翻译流程标准

### 必须遵循的翻译步骤

**严格按照以下顺序执行：**

1. **第一步：内容提取到 addTranslation.js**

   - 将需要翻译的英文内容添加到 `addTranslation.js` 文件中
   - 保持英文原文完全不变
   - 不允许修改原始内容的意思

2. **第二步：执行翻译脚本**

   ```bash
   node addTranslation.js
   ```

   - 此命令会自动将翻译内容添加到 `packages/i18n/translations/` 各语言文件中

3. **第三步：页面中使用翻译**
   - 在页面组件中导入翻译函数：`const t = await getTranslations()`
   - 使用翻译键：`{t('translation.key')}`
   - 完全替换硬编码的文本内容

### ❌ 翻译过程中禁止的行为

1. **禁止修改英文原文**

   - 不允许改变原始英文内容的任何字词
   - 不允许调整句子结构
   - 不允许简化或扩展原文

2. **禁止跳过翻译流程**

   - 不允许直接在组件中硬编码翻译
   - 不允许绕过 addTranslation.js 步骤
   - 必须通过标准工作流程

3. **禁止不完整翻译**
   - 必须翻译页面上所有可见文本
   - 不允许遗漏任何用户界面文字
   - 确保所有语言版本完整

### ✅ 正确的翻译示例

#### Step 1: addTranslation.js 内容

```javascript
const translations = {
  'hero.title': 'Free AI Image Editor for Everyday Creativity',
  'hero.description':
    'IMGGen is a free AI image editor that lets anyone—from beginners to creators—edit, enhance, or transform photos in seconds, all in one place with tools for AI picture correction, image-to-image generation, and more.',
  'hero.badge': '✨ Introducing ImgGen AI V2.0 - Completely Reimagined',
}
```

#### Step 2: 页面使用

```tsx
// ❌ 错误方式 - 硬编码
<h1>Free AI Image Editor for Everyday Creativity</h1>

// ✅ 正确方式 - 使用翻译
const t = await getTranslations()
<h1>{t('hero.title')}</h1>
```

### 🔑 翻译键命名规则

**使用语义化的键名：**

- `hero.title` - 主标题
- `hero.description` - 主描述
- `hero.badge` - 顶部标签
- `hero.cta.primary` - 主要按钮
- `hero.cta.secondary` - 次要按钮

### 📝 翻译质量要求

1. **保持原意不变**

   - 翻译必须准确传达原文意思
   - 保持专业术语的一致性
   - 保持品牌调性

2. **语言自然流畅**

   - 符合目标语言的表达习惯
   - 避免直译造成的生硬感
   - 保持营销文案的吸引力

3. **技术术语一致**
   - AI、Image Editor 等术语保持一致
   - 品牌名 "IMGGen" 保持不变
   - 功能名称统一翻译

### 🔄 翻译更新流程

**当需要更新翻译时：**

1. 先在 `addTranslation.js` 中修改或添加内容
2. 重新执行 `node addTranslation.js`
3. 检查各语言文件是否正确更新
4. 在页面中更新对应的翻译键

### 🚫 常见错误避免

### 📋 翻译检查清单

完成翻译后必须确认：

- [ ] 所有硬编码文本已替换为翻译函数
- [ ] addTranslation.js 已正确执行
- [ ] 各语言文件已更新
- [ ] 页面功能正常，无显示错误
- [ ] 英文原文保持完全不变
- [ ] 翻译键命名符合规范
      description:
      globs:
      alwaysApply: false

---
