# Design Guidelines

## Typography and Colors

### ❌ FORBIDDEN: Gradient Text Effects

**Never use the following gradient text techniques:**

```css
/* ❌ DON'T USE */
.gradient-text {
  background: linear-gradient(...);
  background-clip: text;
  -webkit-background-clip: text;
  text-transparent: true;
}
```

```tsx
/* ❌ DON'T USE */
<span className="bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400 bg-clip-text text-transparent">
  Text
</span>
```

**Reasons:**

1. **SEO Issues**: Search engines may not properly read gradient text
2. **Accessibility**: Screen readers may have difficulty with transparent text
3. **Consistency**: Maintain clean, readable design standards
4. **Performance**: Avoid unnecessary CSS complexity

### ✅ PREFERRED: Solid Color Text

Use solid, high-contrast colors instead:

```tsx
/* ✅ GOOD */
<h1 className="text-white">Main Title</h1>
<h1 className="text-purple-400">Accent Title</h1>
<h1 className="text-pink-500">Secondary Title</h1>
<h1 className="text-blue-400">Info Title</h1>
```

### Color Palette

**Primary Colors:**

- `text-white` - Main text
- `text-gray-100` - Light text
- `text-gray-300` - Secondary text

**Accent Colors:**

- `text-purple-400` - Primary accent
- `text-purple-500` - Strong accent
- `text-pink-400` - Secondary accent
- `text-pink-500` - Strong secondary
- `text-blue-400` - Info accent

### Text Effects Alternatives

Instead of gradients, use:

1. **Solid accent colors**
2. **Text shadows** for depth
3. **Opacity variations**
4. **Hover effects** with solid colors

```tsx
/* ✅ GOOD ALTERNATIVES */
<h1 className="text-purple-400 drop-shadow-lg">Title</h1>
<h1 className="text-white hover:text-purple-400 transition-colors">Interactive Title</h1>
<h1 className="text-white opacity-90">Subtle Title</h1>
```

## Implementation Rules

1. **Always use solid colors for text**
2. **Prioritize readability and accessibility**
3. **Test with screen readers when possible**
4. **Maintain high contrast ratios**
5. **Use semantic color meanings consistently**
6. **NEVER modify user content** - See `content-protection.mdc` for details

## Code Review Checklist

Before submitting code, ensure:

- [ ] No `bg-clip-text` or `text-transparent` classes
- [ ] No gradient backgrounds on text elements
- [ ] Text colors have sufficient contrast
- [ ] Text is readable by screen readers
- [ ] Colors follow the established palette

description:
globs:
alwaysApply: false

---
