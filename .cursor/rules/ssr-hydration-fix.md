# SSR 水合（Hydration）问题文档

## 🤖 开发指导原则

**在为本项目编写代码时，请严格遵循以下 SSR 最佳实践：**

### ⚠️ 关键禁止事项

1. **禁止**在任何可能被服务端调用的[返回数据是用于渲染页面的函数]中使用 `typeof window === 'undefined'` 检查后返回空数据,造成客户端与服务端渲染内容不一致
2. **禁止**在 SSR 函数中直接访问 `window`、`document`、`localStorage` 等浏览器 API
3. **禁止**让服务端和客户端渲染返回不同的数据结构

### ✅ 必须遵循的模式

1. **参数化设计**：将浏览器相关信息作为参数传入，而不是在函数内部获取
2. **数据一致性**：确保服务端和客户端始终返回相同的数据结构

### 🎯 本项目特定规则

- 菜单相关函数(getMenuCategories、getVideoMenuItems)须接收 `isAiPage: boolean` 和 `t: TranslationFunction` 参数
- 路径前缀通过 `isAiPage` 参数确定，在不确定是由服务端还是客户端调用的返回用于渲染页面的数据的函数中 不得使用 `window.location` 检查然后返回不同的渲染结果
- 所有导出的工具函数都应该是纯函数，不依赖浏览器环境

### 📋 代码审查检查清单

在编写或修改代码时，请检查：
- [ ] 函数是否在服务端和客户端返回相同结果？
- [ ] 是否避免了所有浏览器 API 的直接调用？
- [ ] 是否正确处理了国际化翻译？
- [ ] 是否保持了 SSR 的性能优势？

--- 问题举例

## 1. 原始问题分析

### 1.1 问题代码

在修复之前，`getMenuCategories` 和 `getVideoMenuItems` 函数包含了对 `window` 对象的检查：

```typescript
// 修复前的问题代码
export const getMenuCategories = (isAiPage: boolean = false) => {
  if (typeof window === 'undefined') {
    return {} // 服务端返回空对象
  }

  const getHrefPrefix = () =>
    window.location.href.includes('/tools') ||
    !window.location.href.includes('/ai/')
      ? '/tools'
      : '/ai'
  const t = useTranslations()

  return {
    // ... 完整的菜单数据
  }
}

export const getVideoMenuItems = (isAiPage: boolean = false) => {
  if (typeof window === 'undefined') return [] // 服务端返回空数组
  // ... 处理逻辑 并返回视频数据 ...
  const videoItems = [
    // ... 经过翻译处理后完整的菜单数据
  ]
  return videoItems
}
```

### 1.2 问题根源

这种写法导致了严重的 SSR 水合不匹配问题：

1. **服务端渲染阶段**：`window` 对象未定义，函数返回空数据（`{}` 或 `[]`）
2. **客户端水合阶段**：`window` 对象存在，函数返回完整的菜单数据
3. **结果**：服务端和客户端渲染的内容不一致，导致水合失败，转为客户端渲染，丢失了 SSR 的优势

### 1.3 具体错误流程

```
服务端渲染 → 返回空菜单数据 → 生成HTML
    ↓
客户端接收HTML → 开始水合过程 → 发现内容不匹配
    ↓
水合失败 → 回退到客户端渲染 → 丢失SSR优势
```

## 2. 根本原因分析

### 2.1 为什么 `window` 在服务端未定义

- **Node.js 环境**：服务端运行在 Node.js 环境中，没有浏览器的 `window` 对象
- **SSR 特性**：Next.js 在服务端预渲染页面时，无法访问浏览器特定的 API
- **安全检查误用**：虽然 `typeof window === 'undefined'` 是常见的环境检查，但在这里被误用了

### 2.2 水合错误的影响

1. **SEO 损失**：搜索引擎爬虫看到的是空菜单内容
2. **性能下降**：失去 SSR 的首屏渲染优势
3. **用户体验**：页面可能出现闪烁或布局跳动
4. **控制台错误**：React 会报告水合不匹配警告

### 2.3 水合过程详解

```
1. 服务端渲染：HTML 包含空菜单结构
2. 浏览器接收：显示服务端生成的 HTML
3. JavaScript 加载：React 开始水合过程
4. 内容对比：发现服务端和客户端内容不匹配
5. 重新渲染：丢弃服务端 HTML，重新渲染整个组件
```

## 3. 解决方案实现

### 3.1 修复后的代码

```typescript
// 修复后的代码
export const getMenuCategories = (isAiPage: boolean = false, t: any) => {
  const getHrefPrefix = () => {
    return isAiPage ? '/ai' : '/tools'
  }

  return {
    business: {
      label: t('tools.business.label'),
      bgClass: 'from-purple-900/40 to-purple-800/40',
      items: [
        // ... 完整的菜单项
      ],
    },
    // ... 其他分类
  }
}
```

### 3.2 关键修复点

1. **移除 `window` 检查**：不再检查 `window` 对象是否存在
2. **参数化路径逻辑**：通过 `isAiPage` 参数确定路径前缀，而不是检查 `window.location`
3. **外部传入翻译函数**：将 `useTranslations()，t` 的调用移到组件层面,因为组件知道自己是客户端还是服务端组件,自行决定并将翻译函数传给 `getMenuCategories`
4. **确保一致性**：服务端和客户端始终返回相同的数据结构
