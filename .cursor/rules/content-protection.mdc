# Content Protection Rules

## 🚫 NEVER MODIFY: User Content

### What NOT to Change

**Absolutely forbidden to modify:**

1. **Marketing Copy & Messaging**

   - Headlines, titles, taglines
   - Product descriptions
   - Value propositions
   - Call-to-action text
   - Brand messaging

2. **User-Written Content**

   - Blog posts, articles
   - Documentation text
   - Legal content (terms, privacy policy)
   - About us pages
   - User testimonials

3. **Business Information**
   - Company names, product names
   - Contact information
   - Pricing information
   - Feature descriptions

### ❌ Examples of FORBIDDEN Changes

```tsx
// ❌ DON'T DO THIS
// Original: "Free AI Image Editor for Everyday Creativity"
// Changed to: "Transform Your Photos with AI-Powered Editing"

// ❌ DON'T DO THIS
// Original: "IMG<PERSON>en is a free AI image editor..."
// Changed to: "Easily create stunning photos..."
```

## ✅ ALLOWED: Technical & Layout Changes

### What CAN be Modified

**Only these types of changes are permitted:**

1. **Layout & Structure**

   ```tsx
   // ✅ ALLOWED: Layout changes
   <div className="flex flex-col"> → <div className="grid grid-cols-2">
   <h1 className="text-left"> → <h1 className="text-center">
   ```

2. **Styling & Design**

   ```tsx
   // ✅ ALLOWED: Visual improvements
   className="text-blue-500" → className="text-purple-400"
   className="bg-gradient-to-r..." → className="text-white" // following design rules
   ```

3. **Technical Implementation**

   ```tsx
   // ✅ ALLOWED: Code structure
   {t('home.title')} // Keep translation keys intact
   useState, useEffect, etc. // Technical improvements
   ```

4. **Translations**

   - Adding new language support
   - Fixing translation keys
   - Adding missing i18n support

5. **Accessibility Improvements**
   - Adding aria-labels
   - Improving semantic HTML
   - Adding alt text (without changing meaning)

## Implementation Guidelines

### Before Making Changes

**Always ask yourself:**

1. Am I changing the **meaning** of the content?
2. Am I changing **marketing copy** or **messaging**?
3. Am I modifying **business information**?

**If YES to any → DON'T CHANGE IT**

### When in Doubt

**Always ask the user:**

```
"I can improve the layout/styling, but I notice this would change
your content. Should I keep your original text: '[original text]'?"
```

### Safe Changes Checklist

- [ ] Only changing CSS classes/styling
- [ ] Only restructuring HTML/layout
- [ ] Only improving technical implementation
- [ ] Original text/content remains identical
- [ ] Translation keys remain intact
- [ ] Business logic unchanged

## Code Examples

### ✅ GOOD: Layout Optimization

```tsx
// Before
<div className="flex flex-col items-start">
  <h1>Free AI Image Editor for Everyday Creativity</h1>
  <p>IMGGen is a free AI image editor...</p>
</div>

// After - GOOD: Only layout changed
<div className="text-center space-y-4">
  <h1>Free AI Image Editor for Everyday Creativity</h1>
  <p>IMGGen is a free AI image editor...</p>
</div>
```

### ❌ BAD: Content Change

```tsx
// Before
<h1>Free AI Image Editor for Everyday Creativity</h1>

// After - BAD: Content changed
<h1>Transform Your Photos with AI-Powered Editing</h1>
```

## Emergency Override

**Only if explicitly requested by user:**

- User says "change the text to..."
- User provides new copy to replace old
- User explicitly asks for content modifications

**Always confirm before changing content:**
"You want me to change '[original]' to '[new]', correct?"

## Violation Consequences

**If these rules are violated:**

1. Immediately apologize
2. Restore original content
3. Explain what should have been done instead
4. Ask for permission before any content changes
   description:
   globs:
   alwaysApply: false

---
